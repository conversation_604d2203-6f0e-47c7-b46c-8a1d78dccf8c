import { logger } from './logger';

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: any;
  attempts: number;
  totalTime: number;
}

/**
 * 智能重试处理器
 * 实现指数退避重试算法和智能重试条件判断
 */
export class RetryHandler {
  private static readonly DEFAULT_OPTIONS: RetryOptions = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryCondition: (error: any) => {
      // 默认重试条件：网络错误、超时、5xx服务器错误
      if (error.code === 'ECONNRESET' || 
          error.code === 'ENOTFOUND' || 
          error.code === 'ETIMEDOUT') {
        return true;
      }
      
      if (error.response && error.response.status >= 500) {
        return true;
      }
      
      return false;
    }
  };

  /**
   * 执行带重试的异步操作
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {}
  ): Promise<RetryResult<T>> {
    const config = { ...RetryHandler.DEFAULT_OPTIONS, ...options };
    const startTime = Date.now();
    let lastError: any;

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        logger.debug(`Executing operation, attempt ${attempt}/${config.maxAttempts}`);
        
        const result = await operation();
        const totalTime = Date.now() - startTime;
        
        logger.debug(`Operation succeeded on attempt ${attempt}`, { totalTime });
        
        return {
          success: true,
          result,
          attempts: attempt,
          totalTime
        };
        
      } catch (error) {
        lastError = error;
        
        logger.warn(`Operation failed on attempt ${attempt}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          attempt,
          maxAttempts: config.maxAttempts
        });

        // 检查是否应该重试
        if (attempt === config.maxAttempts || !config.retryCondition!(error)) {
          break;
        }

        // 调用重试回调
        if (config.onRetry) {
          config.onRetry(attempt, error);
        }

        // 计算延迟时间（指数退避）
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffFactor, attempt - 1),
          config.maxDelay
        );

        logger.debug(`Retrying in ${delay}ms...`);
        await RetryHandler.sleep(delay);
      }
    }

    const totalTime = Date.now() - startTime;
    
    logger.error('Operation failed after all retry attempts', {
      attempts: config.maxAttempts,
      totalTime,
      lastError: lastError instanceof Error ? lastError.message : 'Unknown error'
    });

    return {
      success: false,
      error: lastError,
      attempts: config.maxAttempts,
      totalTime
    };
  }

  /**
   * 为HTTP请求创建专用的重试配置
   */
  static createHttpRetryOptions(options: Partial<RetryOptions> = {}): RetryOptions {
    return {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 8000,
      backoffFactor: 2,
      retryCondition: (error: any) => {
        // HTTP特定的重试条件
        if (error.code === 'ECONNRESET' || 
            error.code === 'ENOTFOUND' || 
            error.code === 'ETIMEDOUT' ||
            error.code === 'ECONNREFUSED') {
          return true;
        }
        
        // 5xx服务器错误
        if (error.response && error.response.status >= 500) {
          return true;
        }
        
        // 429 Too Many Requests
        if (error.response && error.response.status === 429) {
          return true;
        }
        
        // 408 Request Timeout
        if (error.response && error.response.status === 408) {
          return true;
        }
        
        return false;
      },
      onRetry: (attempt: number, error: any) => {
        logger.info(`HTTP request retry ${attempt}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          status: error.response?.status,
          url: error.config?.url
        });
      },
      ...options
    };
  }

  /**
   * 为数据库操作创建专用的重试配置
   */
  static createDatabaseRetryOptions(options: Partial<RetryOptions> = {}): RetryOptions {
    return {
      maxAttempts: 5,
      baseDelay: 500,
      maxDelay: 5000,
      backoffFactor: 1.5,
      retryCondition: (error: any) => {
        // 数据库特定的重试条件
        const retryableCodes = [
          'ECONNRESET',
          'ENOTFOUND',
          'ETIMEDOUT',
          'ECONNREFUSED',
          'ER_LOCK_WAIT_TIMEOUT',
          'ER_LOCK_DEADLOCK'
        ];
        
        return retryableCodes.includes(error.code);
      },
      onRetry: (attempt: number, error: any) => {
        logger.info(`Database operation retry ${attempt}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error.code
        });
      },
      ...options
    };
  }

  /**
   * 为文件操作创建专用的重试配置
   */
  static createFileRetryOptions(options: Partial<RetryOptions> = {}): RetryOptions {
    return {
      maxAttempts: 3,
      baseDelay: 200,
      maxDelay: 2000,
      backoffFactor: 2,
      retryCondition: (error: any) => {
        // 文件操作特定的重试条件
        const retryableCodes = [
          'EBUSY',
          'EMFILE',
          'ENFILE',
          'ENOENT',
          'EPERM',
          'EACCES'
        ];
        
        return retryableCodes.includes(error.code);
      },
      onRetry: (attempt: number, error: any) => {
        logger.info(`File operation retry ${attempt}`, {
          error: error instanceof Error ? error.message : 'Unknown error',
          code: error.code,
          path: error.path
        });
      },
      ...options
    };
  }

  /**
   * 创建带重试的函数包装器
   */
  static withRetry<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    options: Partial<RetryOptions> = {}
  ): T {
    return (async (...args: any[]) => {
      const result = await RetryHandler.executeWithRetry(
        () => fn(...args),
        options
      );
      
      if (result.success) {
        return result.result;
      } else {
        throw result.error;
      }
    }) as T;
  }

  /**
   * 睡眠函数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建带抖动的延迟（避免惊群效应）
   */
  static createJitteredDelay(baseDelay: number, jitterFactor: number = 0.1): number {
    const jitter = baseDelay * jitterFactor * Math.random();
    return baseDelay + jitter;
  }

  /**
   * 批量重试操作
   */
  static async executeBatchWithRetry<T>(
    operations: (() => Promise<T>)[],
    options: Partial<RetryOptions> = {},
    concurrency: number = 3
  ): Promise<RetryResult<T>[]> {
    const results: RetryResult<T>[] = [];
    
    // 分批执行操作
    for (let i = 0; i < operations.length; i += concurrency) {
      const batch = operations.slice(i, i + concurrency);
      const batchPromises = batch.map(operation => 
        RetryHandler.executeWithRetry(operation, options)
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }
    
    return results;
  }
}

// 导出便捷函数
export const withHttpRetry = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: Partial<RetryOptions> = {}
) => RetryHandler.withRetry(fn, RetryHandler.createHttpRetryOptions(options));

export const withDatabaseRetry = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: Partial<RetryOptions> = {}
) => RetryHandler.withRetry(fn, RetryHandler.createDatabaseRetryOptions(options));

export const withFileRetry = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: Partial<RetryOptions> = {}
) => RetryHandler.withRetry(fn, RetryHandler.createFileRetryOptions(options));
