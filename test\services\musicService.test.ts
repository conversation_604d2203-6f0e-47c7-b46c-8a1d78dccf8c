import { MusicService } from '../../src/services/musicService';
import { SearchParams, UrlParams, PictureParams, LyricParams } from '../../src/types/music';

describe('MusicService', () => {
  let musicService: MusicService;

  beforeEach(() => {
    musicService = new MusicService();
  });

  describe('search', () => {
    it('should return search results for valid source', async () => {
      // 使用通用的英文关键词进行测试，避免硬编码特定歌手名称
      const params: SearchParams = {
        source: 'netease',
        keyword: 'music',
        limit: 20,
        offset: 0,
      };

      const results = await musicService.search(params);

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      // 不强制要求结果数量，因为API可能返回空结果
      if (results.length > 0) {
        expect(results[0]).toHaveProperty('id');
        expect(results[0]).toHaveProperty('name');
        expect(results[0]).toHaveProperty('artist');
        expect(results[0]).toHaveProperty('source');
      }
    });

    it('should throw error for invalid source', async () => {
      const params: SearchParams = {
        source: 'invalid_source',
        keyword: 'test',
        limit: 20,
        offset: 0,
      };

      await expect(musicService.search(params)).rejects.toThrow('Invalid source');
    });
  });

  describe('getMusicUrl', () => {
    it('should return music URL for valid parameters', async () => {
      // 使用动态获取的歌曲ID，而不是硬编码
      // 首先搜索获取真实的歌曲ID
      const searchParams: SearchParams = {
        source: 'netease',
        keyword: 'music',
        limit: 1,
        offset: 0,
      };

      const searchResults = await musicService.search(searchParams);

      // 如果搜索有结果，使用真实ID进行测试
      if (searchResults && searchResults.length > 0 && searchResults[0]) {
        const params: UrlParams = {
          source: 'netease',
          id: searchResults[0].id,
          quality: 320,
        };

        const result = await musicService.getMusicUrl(params);

        expect(result).toBeDefined();
        expect(result).toHaveProperty('url');
        expect(result).toHaveProperty('br');
        expect(result).toHaveProperty('size');
      } else {
        // 如果搜索无结果，跳过此测试
        console.warn('Skipping getMusicUrl test: no search results available');
      }
    });

    it('should throw error for invalid source', async () => {
      const params: UrlParams = {
        source: 'invalid_source',
        id: 'test_id',
        quality: 320,
      };

      await expect(musicService.getMusicUrl(params)).rejects.toThrow('Invalid source');
    });
  });

  describe('getPicture', () => {
    it('should return picture URL for valid parameters', async () => {
      // 使用动态获取的图片ID
      const searchParams: SearchParams = {
        source: 'netease',
        keyword: 'music',
        limit: 1,
        offset: 0,
      };

      const searchResults = await musicService.search(searchParams);

      if (searchResults && searchResults.length > 0 && searchResults[0]) {
        const params: PictureParams = {
          source: 'netease',
          id: searchResults[0].id, // 使用歌曲ID作为图片ID
          size: 300,
        };

        const result = await musicService.getPicture(params);

        expect(result).toBeDefined();
        expect(result).toHaveProperty('url');
        expect(typeof result.url).toBe('string');
      } else {
        console.warn('Skipping getPicture test: no search results available');
      }
    });

    it('should throw error for invalid source', async () => {
      const params: PictureParams = {
        source: 'invalid_source',
        id: 'test_id',
        size: 300,
      };

      await expect(musicService.getPicture(params)).rejects.toThrow('Invalid source');
    });
  });

  describe('getLyric', () => {
    it('should return lyrics for valid parameters', async () => {
      // 使用动态获取的歌曲ID
      const searchParams: SearchParams = {
        source: 'netease',
        keyword: 'music',
        limit: 1,
        offset: 0,
      };

      const searchResults = await musicService.search(searchParams);

      if (searchResults && searchResults.length > 0 && searchResults[0]) {
        const params: LyricParams = {
          source: 'netease',
          id: searchResults[0].id,
        };

        const result = await musicService.getLyric(params);

        expect(result).toBeDefined();
        expect(result).toHaveProperty('lyric');
        expect(typeof result.lyric).toBe('string');
      } else {
        console.warn('Skipping getLyric test: no search results available');
      }
    });

    it('should throw error for invalid source', async () => {
      const params: LyricParams = {
        source: 'invalid_source',
        id: 'test_id',
      };

      await expect(musicService.getLyric(params)).rejects.toThrow('Invalid source');
    });
  });
});
