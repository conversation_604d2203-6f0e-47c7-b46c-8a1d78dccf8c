# 🚀 生产环境部署指南

本文档详细说明如何在生产环境中部署和运行UNM API Server。

## 📋 前置要求

### 系统要求
- **Node.js**: >= 18.0.0
- **内存**: >= 2GB RAM
- **磁盘**: >= 10GB 可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- **OpenSSL**: 用于生成SSL证书
- **PM2** (可选): 进程管理器
- **Nginx** (推荐): 反向代理和负载均衡

## 🔧 生产环境配置

### 1. 环境变量配置

生产环境使用 `.env.production` 配置文件，主要差异：

```bash
# 生产环境特定配置
NODE_ENV=production
LOG_LEVEL=warn
ENABLE_HTTPS=true
BLOCK_SUSPICIOUS_REQUESTS=true
WARMUP_ENABLED=true

# 性能优化
CACHE_TTL=600
CACHE_MAX_SIZE=5000
RATE_LIMIT_MAX=200

# 安全加固
BLOCK_EMPTY_USER_AGENT=true
MAX_HEADER_SIZE=4096
REQUEST_TIMEOUT=20000
```

### 2. SSL证书配置

#### 自签名证书（测试用）
```bash
# 自动生成（启动脚本会自动处理）
npm run start-production
```

#### Let's Encrypt证书（推荐）
```bash
# 安装certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 配置环境变量
SSL_KEY_PATH=/etc/letsencrypt/live/yourdomain.com/privkey.pem
SSL_CERT_PATH=/etc/letsencrypt/live/yourdomain.com/fullchain.pem
```

### 3. 反向代理配置（Nginx）

```nginx
# /etc/nginx/sites-available/unm-api
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 限制请求大小
    client_max_body_size 5M;

    location / {
        proxy_pass http://localhost:5678;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

## 🚀 部署步骤

### 1. 准备部署环境

```bash
# 克隆项目
git clone <repository-url>
cd unm-api-agc

# 安装依赖
npm install --production

# 验证配置
npm run validate-config
npm run compare-env-files
```

### 2. 启动生产服务器

```bash
# 方式1: 使用内置启动脚本
npm run start-production

# 方式2: 使用PM2（推荐）
npm install -g pm2
pm2 start ecosystem.config.js --env production

# 方式3: 使用systemd服务
sudo systemctl start unm-api-server
```

### 3. 验证部署

```bash
# 健康检查
curl -f http://localhost:5678/health

# API测试
curl "http://localhost:5678/api/v1/search?source=netease&q=周杰伦"

# HTTPS测试（如果启用）
curl -k "https://localhost:443/health"
```

## 📊 监控和维护

### 1. 启动监控

```bash
# 实时监控
npm run monitor-production start

# 生成报告
npm run monitor-production report
```

### 2. 日志管理

```bash
# 查看日志
tail -f logs/production.log
tail -f logs/monitor.log

# 日志轮转（推荐使用logrotate）
sudo nano /etc/logrotate.d/unm-api-server
```

### 3. 性能优化

#### 系统级优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化TCP设置
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

#### 应用级优化
```bash
# 设置Node.js环境变量
export UV_THREADPOOL_SIZE=128
export NODE_OPTIONS="--max-old-space-size=2048"
```

## 🔒 安全最佳实践

### 1. 防火墙配置

```bash
# UFW配置
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 2. 定期安全检查

```bash
# 运行安全测试
npm run security-test-full

# 更新依赖
npm audit fix
npm update
```

### 3. 备份策略

```bash
# 配置文件备份
cp .env.production .env.production.backup.$(date +%Y%m%d)

# 日志备份
tar -czf logs-backup-$(date +%Y%m%d).tar.gz logs/
```

## 🛠️ 故障排除

### 常见问题

#### 1. 服务器启动失败
```bash
# 检查端口占用
netstat -tulpn | grep :5678

# 检查权限
ls -la certs/
chmod 600 certs/server.key
```

#### 2. SSL证书问题
```bash
# 验证证书
openssl x509 -in certs/server.crt -text -noout

# 重新生成证书
rm -rf certs/
npm run start-production
```

#### 3. 性能问题
```bash
# 检查系统资源
htop
df -h
free -h

# 检查Node.js进程
ps aux | grep node
```

## 📈 扩展部署

### 1. 负载均衡

```nginx
# Nginx负载均衡配置
upstream unm_api_backend {
    server 127.0.0.1:5678;
    server 127.0.0.1:5679;
    server 127.0.0.1:5680;
}

server {
    location / {
        proxy_pass http://unm_api_backend;
    }
}
```

### 2. 容器化部署

```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 5678
CMD ["npm", "run", "start-production"]
```

### 3. 集群部署

```javascript
// cluster.js
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
} else {
  require('./src/server.ts');
}
```

## 📞 支持和维护

### 定期维护任务

1. **每日**: 检查日志和监控指标
2. **每周**: 运行安全测试和性能测试
3. **每月**: 更新依赖和系统补丁
4. **每季度**: 全面安全审计

### 紧急联系

- 技术支持: [技术支持邮箱]
- 监控告警: [告警通知渠道]
- 文档更新: [文档维护地址]

---

**注意**: 在生产环境中部署前，请务必在测试环境中验证所有配置和功能。
