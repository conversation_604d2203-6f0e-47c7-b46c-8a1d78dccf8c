import { Request, Response, NextFunction } from 'express';

// 缓存数据模型
export interface CacheItem<T> {
  key: string;         // 缓存键
  data: T;            // 缓存数据
  timestamp: number;   // 缓存时间戳
  ttl: number;        // 生存时间 (秒)
}

// 缓存配置
export interface CacheConfig {
  defaultTTL: number;      // 默认TTL (秒)
  maxKeys: number;         // 最大缓存键数量
  checkPeriod: number;     // 清理检查周期 (秒)
}

// 环境配置接口
export interface EnvironmentConfig {
  // 服务器配置
  nodeEnv: 'development' | 'production' | 'test';
  port: number;
  serverName: string;
  serverVersion: string;
  serverAuthor: string;
  serverDescription: string;

  // 外部API配置
  musicApiUrl: string;
  httpTimeout: number;

  // User-Agent配置
  userAgents: string[];

  // CORS配置
  corsOrigin: string;
  corsMethods: string[];
  corsHeaders: string[];

  // 缓存配置
  cacheTTL: number;
  enableCache: boolean;
  cacheMaxSize: number;
  cacheCleanupInterval: number;
  cacheSearchTTL: number;
  cacheMusicUrlTTL: number;
  cacheLyricsTTL: number;
  cachePictureTTL: number;
  cacheUnmTTL: number;

  // 频率限制配置
  rateLimitWindow: number;
  rateLimitMax: number;
  rateLimitMessage: string;

  // 日志配置
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  logFile: string;

  // 音乐服务配置
  defaultSource: string;
  supportedSources: string[];
  stableSources: string[];

  // API配置
  apiVersion: string;
  apiBasePath: string;
  maxSearchLimit: number;
  maxBatchSize: number;
  defaultSearchLimit: number;
  defaultSearchOffset: number;

  // UNM配置
  unmDefaultSources: string[];
  unmSupportedQualities: number[];
  unmDefaultQuality: number;
  unmSupportedSizes: number[];
  unmDefaultSize: number;

  // 安全配置
  helmetEnabled: boolean;
  requestSizeLimit: string;

  // 文件上传配置
  uploadMaxSize: number;
  uploadAllowedTypes: string[];

  // 静态文件配置
  staticPath: string;
  staticMaxAge: number;

  // 健康检查配置
  healthCheckInterval: number;
  healthCheckTimeout: number;

  // 预热配置
  warmupQueries: string[];
  warmupEnabled: boolean;

  // 错误消息配置
  errorMessages: {
    invalidParams: string;
    missingParam: string;
    invalidSource: string;
    invalidQuality: string;
    invalidSize: string;
    songNotFound: string;
    urlNotFound: string;
    pictureNotFound: string;
    lyricsNotFound: string;
    serviceUnavailable: string;
    timeout: string;
    networkError: string;
    unknown: string;
  };

  // 成功消息配置
  successMessages: {
    default: string;
    searchCompleted: string;
    urlRetrieved: string;
    pictureRetrieved: string;
    lyricsRetrieved: string;
    cacheCleared: string;
    warmupCompleted: string;
  };

  // 验证配置
  validation: {
    minSearchLength: number;
    maxSearchLength: number;
    minIdLength: number;
    maxIdLength: number;
    idPattern: RegExp;
    numericIdPattern: RegExp;
  };
}

// Express中间件类型
export type Middleware = (req: Request, res: Response, next: NextFunction) => void;
export type AsyncMiddleware = (req: Request, res: Response, next: NextFunction) => Promise<void>;
export type ErrorMiddleware = (error: Error, req: Request, res: Response, next: NextFunction) => void;

// API错误类
export class APIError extends Error {
  constructor(
    public statusCode: number,
    public override message: string,
    public details?: any,
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// 验证结果接口
export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

// 日志级别
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// HTTP方法类型
export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD';
