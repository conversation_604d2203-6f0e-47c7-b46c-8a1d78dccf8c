// 音源配置模型
export interface MusicSource {
  id: string;          // 音源标识
  name: string;        // 音源名称
  enabled: boolean;    // 是否启用
  priority: number;    // 优先级 (1-10)
  stable: boolean;     // 是否为稳定音源
}

// 搜索参数
export interface SearchParams {
  source: string;
  keyword: string;
  limit: number;
  offset: number;
}

// URL获取参数
export interface UrlParams {
  source: string;
  id: string;
  quality: number;
}

// 图片获取参数
export interface PictureParams {
  source: string;
  id: string;
  size: number;
}

// 歌词获取参数
export interface LyricParams {
  source: string;
  id: string;
}

// 搜索结果数据模型
export interface MusicSearchResult {
  id: string;
  name: string;
  artist: string | string[];
  album?: string;
  duration?: number;
  source: string;
  pic?: string;
}

// 音乐URL数据模型
export interface MusicUrl {
  url: string;
  br: number;
  size: number;
  type: string;
  source?: string;
}

// 音乐图片数据模型
export interface MusicPicture {
  url: string;
  size?: number;
}

// 音乐歌词数据模型
export interface MusicLyric {
  lyric: string;
  tlyric?: string; // 翻译歌词
}

// UnblockNeteaseMusic库返回的数据格式
export interface UNMResult {
  url?: string;
  br?: number;
  size?: number;
  type?: string;
  source?: string;
}

// 音源列表配置
export interface SourcesConfig {
  stable: string[];
  all: string[];
}
