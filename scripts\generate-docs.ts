#!/usr/bin/env tsx

/**
 * 文档生成和验证脚本
 * 用于生成项目文档的目录结构和验证文档完整性
 */

import fs from 'fs';
import path from 'path';

interface DocumentInfo {
  path: string;
  title: string;
  description: string;
  size: number;
  lastModified: Date;
}

class DocumentationGenerator {
  private readonly docsDir = path.join(process.cwd(), 'docs');
  private readonly rootDir = process.cwd();
  
  /**
   * 生成文档目录结构
   */
  async generateDocumentationIndex(): Promise<void> {
    console.log('🚀 开始生成文档索引...\n');
    
    const documents = await this.scanDocuments();
    const toc = this.generateTableOfContents(documents);
    const stats = this.generateDocumentationStats(documents);
    
    console.log('📚 文档目录结构:');
    console.log(toc);
    
    console.log('\n📊 文档统计信息:');
    console.log(stats);
    
    // 生成文档索引文件
    await this.generateIndexFile(documents);
    
    console.log('\n✅ 文档索引生成完成！');
  }
  
  /**
   * 扫描所有文档文件
   */
  private async scanDocuments(): Promise<DocumentInfo[]> {
    const documents: DocumentInfo[] = [];
    
    // 扫描根目录的主要文档
    const rootDocs = [
      'README.md',
      'CHANGELOG.md',
      'requirements.md',
      'design.md',
      'tasks.md'
    ];
    
    for (const doc of rootDocs) {
      const filePath = path.join(this.rootDir, doc);
      if (fs.existsSync(filePath)) {
        documents.push(await this.getDocumentInfo(filePath, doc));
      }
    }
    
    // 扫描docs目录
    if (fs.existsSync(this.docsDir)) {
      const docFiles = fs.readdirSync(this.docsDir)
        .filter(file => file.endsWith('.md'))
        .sort();
      
      for (const file of docFiles) {
        const filePath = path.join(this.docsDir, file);
        documents.push(await this.getDocumentInfo(filePath, `docs/${file}`));
      }
    }
    
    return documents;
  }
  
  /**
   * 获取文档信息
   */
  private async getDocumentInfo(filePath: string, relativePath: string): Promise<DocumentInfo> {
    const stats = fs.statSync(filePath);
    const content = fs.readFileSync(filePath, 'utf-8');
    
    // 提取文档标题
    const titleMatch = content.match(/^#\s+(.+)$/m);
    const title = titleMatch ? titleMatch[1] : path.basename(relativePath, '.md');
    
    // 提取文档描述
    const descMatch = content.match(/^#\s+.+\n\n(.+)$/m);
    const description = descMatch ? descMatch[1].substring(0, 100) + '...' : '暂无描述';
    
    return {
      path: relativePath,
      title: title.replace(/[📚🎵🚀💻🏗️🔒🔧📝📖⚙️🐳📦]/g, '').trim(),
      description,
      size: stats.size,
      lastModified: stats.mtime
    };
  }
  
  /**
   * 生成目录结构
   */
  private generateTableOfContents(documents: DocumentInfo[]): string {
    let toc = '';
    
    // 根目录文档
    const rootDocs = documents.filter(doc => !doc.path.startsWith('docs/'));
    if (rootDocs.length > 0) {
      toc += '📁 根目录文档\n';
      for (const doc of rootDocs) {
        toc += `  📄 ${doc.title} (${doc.path})\n`;
      }
      toc += '\n';
    }
    
    // docs目录文档
    const docsDocs = documents.filter(doc => doc.path.startsWith('docs/'));
    if (docsDocs.length > 0) {
      toc += '📁 docs/ 目录文档\n';
      for (const doc of docsDocs) {
        toc += `  📄 ${doc.title} (${doc.path})\n`;
      }
    }
    
    return toc;
  }
  
  /**
   * 生成文档统计信息
   */
  private generateDocumentationStats(documents: DocumentInfo[]): string {
    const totalDocs = documents.length;
    const totalSize = documents.reduce((sum, doc) => sum + doc.size, 0);
    const avgSize = Math.round(totalSize / totalDocs);
    
    const sizeInKB = Math.round(totalSize / 1024);
    const avgSizeInKB = Math.round(avgSize / 1024);
    
    const lastModified = documents
      .map(doc => doc.lastModified)
      .sort((a, b) => b.getTime() - a.getTime())[0];
    
    return `
总文档数量: ${totalDocs} 个
总文档大小: ${sizeInKB} KB
平均文档大小: ${avgSizeInKB} KB
最后更新时间: ${lastModified.toLocaleString('zh-CN')}
    `.trim();
  }
  
  /**
   * 生成文档索引文件
   */
  private async generateIndexFile(documents: DocumentInfo[]): Promise<void> {
    const indexPath = path.join(this.docsDir, 'INDEX.md');
    
    let content = `# 📚 文档索引

> 自动生成于 ${new Date().toLocaleString('zh-CN')}

## 📋 文档列表

`;
    
    // 按类型分组
    const rootDocs = documents.filter(doc => !doc.path.startsWith('docs/'));
    const docsDocs = documents.filter(doc => doc.path.startsWith('docs/'));
    
    if (rootDocs.length > 0) {
      content += '### 🏠 主要文档\n\n';
      for (const doc of rootDocs) {
        content += `- **[${doc.title}](../${doc.path})** - ${doc.description}\n`;
      }
      content += '\n';
    }
    
    if (docsDocs.length > 0) {
      content += '### 📖 详细文档\n\n';
      for (const doc of docsDocs) {
        const fileName = doc.path.replace('docs/', '');
        content += `- **[${doc.title}](${fileName})** - ${doc.description}\n`;
      }
      content += '\n';
    }
    
    content += `## 📊 统计信息

${this.generateDocumentationStats(documents)}

## 🔄 更新说明

此文档索引由脚本自动生成，包含了项目中所有的文档文件信息。

要重新生成此索引，请运行：
\`\`\`bash
npm run generate-docs
\`\`\`

---

📝 如需更新文档，请编辑对应的源文件，然后重新运行生成脚本。
`;
    
    fs.writeFileSync(indexPath, content, 'utf-8');
    console.log(`📄 已生成文档索引: ${indexPath}`);
  }
  
  /**
   * 验证文档完整性
   */
  async validateDocumentation(): Promise<void> {
    console.log('🔍 开始验证文档完整性...\n');
    
    const requiredDocs = [
      'README.md',
      'docs/API.md',
      'docs/DEPLOYMENT.md',
      'docs/DEVELOPMENT.md',
      'docs/ARCHITECTURE.md',
      'docs/SECURITY.md',
      'docs/TROUBLESHOOTING.md'
    ];
    
    const missingDocs: string[] = [];
    const existingDocs: string[] = [];
    
    for (const doc of requiredDocs) {
      const filePath = path.join(this.rootDir, doc);
      if (fs.existsSync(filePath)) {
        existingDocs.push(doc);
        console.log(`✅ ${doc}`);
      } else {
        missingDocs.push(doc);
        console.log(`❌ ${doc} - 缺失`);
      }
    }
    
    console.log(`\n📊 验证结果:`);
    console.log(`✅ 存在的文档: ${existingDocs.length}/${requiredDocs.length}`);
    
    if (missingDocs.length > 0) {
      console.log(`❌ 缺失的文档: ${missingDocs.length}`);
      console.log('缺失文档列表:');
      missingDocs.forEach(doc => console.log(`  - ${doc}`));
    } else {
      console.log('🎉 所有必需文档都已存在！');
    }
  }
  
  /**
   * 检查文档链接
   */
  async checkDocumentLinks(): Promise<void> {
    console.log('🔗 开始检查文档链接...\n');
    
    const documents = await this.scanDocuments();
    const brokenLinks: Array<{doc: string, link: string}> = [];
    
    for (const doc of documents) {
      const filePath = path.join(this.rootDir, doc.path);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      // 查找Markdown链接
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
      let match;
      
      while ((match = linkRegex.exec(content)) !== null) {
        const linkText = match[1];
        const linkUrl = match[2];
        
        // 检查相对路径链接
        if (!linkUrl.startsWith('http') && !linkUrl.startsWith('#')) {
          const targetPath = path.resolve(path.dirname(filePath), linkUrl);
          
          if (!fs.existsSync(targetPath)) {
            brokenLinks.push({
              doc: doc.path,
              link: `${linkText} -> ${linkUrl}`
            });
          }
        }
      }
    }
    
    if (brokenLinks.length > 0) {
      console.log('❌ 发现损坏的链接:');
      brokenLinks.forEach(item => {
        console.log(`  ${item.doc}: ${item.link}`);
      });
    } else {
      console.log('✅ 所有文档链接都正常！');
    }
  }
}

// 主函数
async function main() {
  const generator = new DocumentationGenerator();
  const command = process.argv[2] || 'generate';
  
  try {
    switch (command) {
      case 'generate':
        await generator.generateDocumentationIndex();
        break;
      case 'validate':
        await generator.validateDocumentation();
        break;
      case 'check-links':
        await generator.checkDocumentLinks();
        break;
      case 'all':
        await generator.generateDocumentationIndex();
        await generator.validateDocumentation();
        await generator.checkDocumentLinks();
        break;
      default:
        console.log(`
📚 文档生成工具

用法: npm run generate-docs [command]

命令:
  generate     生成文档索引 (默认)
  validate     验证文档完整性
  check-links  检查文档链接
  all          执行所有操作

示例:
  npm run generate-docs
  npm run generate-docs validate
  npm run generate-docs all
        `);
    }
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

export { DocumentationGenerator };
