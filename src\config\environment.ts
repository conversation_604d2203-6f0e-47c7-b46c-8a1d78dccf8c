import dotenv from 'dotenv';
import { EnvironmentConfig } from './../types/common';

// 加载环境变量
dotenv.config();

// 解析数组配置的辅助函数
function parseArray(value: string | undefined, defaultValue: string[]): string[] {
  if (!value) return defaultValue;
  return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
}

// 解析数字数组配置的辅助函数
function parseNumberArray(value: string | undefined, defaultValue: number[]): number[] {
  if (!value) return defaultValue;
  return value.split(',').map(item => parseInt(item.trim())).filter(num => !isNaN(num));
}

// 默认配置值
const defaults = {
  userAgents: [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
  ],
  supportedSources: ['netease', 'qq', 'xiami', 'baidu', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'bilibili', 'pyncmd'],
  stableSources: ['netease', 'kuwo', 'pyncmd'],
  unmDefaultSources: ['pyncmd', 'netease', 'kuwo'],
  unmSupportedQualities: [128, 192, 320, 740, 999],
  unmSupportedSizes: [300, 500],
  corsMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  corsHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  uploadAllowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
  // 移除硬编码的warmupQueries，改为从环境变量或动态获取
};

// 环境配置
export const config: EnvironmentConfig = {
  // 服务器配置
  nodeEnv: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
  port: parseInt(process.env.PORT || '5678', 10),
  serverName: process.env.SERVER_NAME || 'UNM API Server',
  serverVersion: process.env.SERVER_VERSION || '1.0.0',
  serverAuthor: process.env.SERVER_AUTHOR || 'UNM API Server Contributors',
  serverDescription: process.env.SERVER_DESCRIPTION || 'UnblockNeteaseMusic API Server - Modern RESTful music API service',

  // 外部API配置
  musicApiUrl: process.env.MUSIC_API_URL || 'https://music-api.gdstudio.xyz/api.php',
  httpTimeout: parseInt(process.env.HTTP_TIMEOUT || '10000', 10),

  // User-Agent配置
  userAgents: process.env.USER_AGENTS ? process.env.USER_AGENTS.split('|') : defaults.userAgents,

  // CORS配置
  corsOrigin: process.env.CORS_ORIGIN || '*',
  corsMethods: parseArray(process.env.CORS_METHODS, defaults.corsMethods),
  corsHeaders: parseArray(process.env.CORS_HEADERS, defaults.corsHeaders),

  // 缓存配置
  cacheTTL: parseInt(process.env.CACHE_TTL || '300', 10),
  enableCache: process.env.ENABLE_CACHE !== 'false',
  cacheMaxSize: parseInt(process.env.CACHE_MAX_SIZE || '1000', 10),
  cacheCleanupInterval: parseInt(process.env.CACHE_CLEANUP_INTERVAL || '300000', 10),
  cacheSearchTTL: parseInt(process.env.CACHE_SEARCH_TTL || '600', 10),
  cacheMusicUrlTTL: parseInt(process.env.CACHE_MUSIC_URL_TTL || '1800', 10),
  cacheLyricsTTL: parseInt(process.env.CACHE_LYRICS_TTL || '3600', 10),
  cachePictureTTL: parseInt(process.env.CACHE_PICTURE_TTL || '86400', 10),
  cacheUnmTTL: parseInt(process.env.CACHE_UNM_TTL || '1200', 10),

  // 频率限制配置
  rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '900000', 10),
  rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
  rateLimitMessage: process.env.RATE_LIMIT_MESSAGE || 'Too many requests, please try again later',

  // 日志配置
  logLevel: (process.env.LOG_LEVEL as 'debug' | 'info' | 'warn' | 'error') || 'info',
  logFile: process.env.LOG_FILE || '',

  // 音乐服务配置
  defaultSource: process.env.DEFAULT_SOURCE || 'netease',
  supportedSources: parseArray(process.env.SUPPORTED_SOURCES, defaults.supportedSources),
  stableSources: parseArray(process.env.STABLE_SOURCES, defaults.stableSources),

  // API配置
  apiVersion: process.env.API_VERSION || 'v1',
  apiBasePath: process.env.API_BASE_PATH || '/api',
  maxSearchLimit: parseInt(process.env.MAX_SEARCH_LIMIT || '100', 10),
  maxBatchSize: parseInt(process.env.MAX_BATCH_SIZE || '50', 10),
  defaultSearchLimit: parseInt(process.env.DEFAULT_SEARCH_LIMIT || '20', 10),
  defaultSearchOffset: parseInt(process.env.DEFAULT_SEARCH_OFFSET || '0', 10),

  // UNM配置
  unmDefaultSources: parseArray(process.env.UNM_DEFAULT_SOURCES, defaults.unmDefaultSources),
  unmSupportedQualities: parseNumberArray(process.env.UNM_SUPPORTED_QUALITIES, defaults.unmSupportedQualities),
  unmDefaultQuality: parseInt(process.env.UNM_DEFAULT_QUALITY || '999', 10),
  unmSupportedSizes: parseNumberArray(process.env.UNM_SUPPORTED_SIZES, defaults.unmSupportedSizes),
  unmDefaultSize: parseInt(process.env.UNM_DEFAULT_SIZE || '300', 10),

  // 安全配置
  helmetEnabled: process.env.HELMET_ENABLED !== 'false',
  requestSizeLimit: process.env.REQUEST_SIZE_LIMIT || '10mb',

  // 文件上传配置
  uploadMaxSize: parseInt(process.env.UPLOAD_MAX_SIZE || '10485760', 10),
  uploadAllowedTypes: parseArray(process.env.UPLOAD_ALLOWED_TYPES, defaults.uploadAllowedTypes),

  // 静态文件配置
  staticPath: process.env.STATIC_PATH || '/',
  staticMaxAge: parseInt(process.env.STATIC_MAX_AGE || '86400000', 10),

  // 健康检查配置
  healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
  healthCheckTimeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '5000', 10),

  // 预热配置 - 从环境变量获取，如果没有则使用空数组
  warmupQueries: parseArray(process.env.WARMUP_QUERIES, []),
  warmupEnabled: process.env.WARMUP_ENABLED === 'true',

  // 错误消息配置
  errorMessages: {
    invalidParams: process.env.ERROR_INVALID_PARAMS || 'Invalid parameters',
    missingParam: process.env.ERROR_MISSING_PARAM || 'Required parameter is missing',
    invalidSource: process.env.ERROR_INVALID_SOURCE || 'Invalid source parameter',
    invalidQuality: process.env.ERROR_INVALID_QUALITY || 'Invalid quality parameter',
    invalidSize: process.env.ERROR_INVALID_SIZE || 'Invalid size parameter',
    songNotFound: process.env.ERROR_SONG_NOT_FOUND || 'No matching song found',
    urlNotFound: process.env.ERROR_URL_NOT_FOUND || 'No music URL found',
    pictureNotFound: process.env.ERROR_PICTURE_NOT_FOUND || 'No picture URL found',
    lyricsNotFound: process.env.ERROR_LYRICS_NOT_FOUND || 'No lyrics found',
    serviceUnavailable: process.env.ERROR_SERVICE_UNAVAILABLE || 'Service temporarily unavailable',
    timeout: process.env.ERROR_TIMEOUT || 'Request timeout',
    networkError: process.env.ERROR_NETWORK_ERROR || 'Network error',
    unknown: process.env.ERROR_UNKNOWN || 'Unknown error occurred',
  },

  // 成功消息配置
  successMessages: {
    default: process.env.SUCCESS_DEFAULT || 'Success',
    searchCompleted: process.env.SUCCESS_SEARCH_COMPLETED || 'Search completed successfully',
    urlRetrieved: process.env.SUCCESS_URL_RETRIEVED || 'Music URL retrieved successfully',
    pictureRetrieved: process.env.SUCCESS_PICTURE_RETRIEVED || 'Picture retrieved successfully',
    lyricsRetrieved: process.env.SUCCESS_LYRICS_RETRIEVED || 'Lyrics retrieved successfully',
    cacheCleared: process.env.SUCCESS_CACHE_CLEARED || 'Cache cleared successfully',
    warmupCompleted: process.env.SUCCESS_WARMUP_COMPLETED || 'Cache warmup completed',
  },

  // 验证配置
  validation: {
    minSearchLength: parseInt(process.env.MIN_SEARCH_LENGTH || '1', 10),
    maxSearchLength: parseInt(process.env.MAX_SEARCH_LENGTH || '100', 10),
    minIdLength: parseInt(process.env.MIN_ID_LENGTH || '1', 10),
    maxIdLength: parseInt(process.env.MAX_ID_LENGTH || '50', 10),
    idPattern: new RegExp(process.env.ID_PATTERN || '^[a-zA-Z0-9_-]+$'),
    numericIdPattern: new RegExp(process.env.NUMERIC_ID_PATTERN || '^[0-9]+$'),
  },
};

// 验证必需的环境变量
const validateConfig = () => {
  const requiredEnvVars = ['NODE_ENV'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // 验证音乐API URL格式
  if (config.musicApiUrl && !config.musicApiUrl.startsWith('http')) {
    throw new Error('MUSIC_API_URL must be a valid HTTP/HTTPS URL');
  }
  
  // 验证端口范围
  if (config.port < 1 || config.port > 65535) {
    throw new Error('PORT must be between 1 and 65535');
  }
  
  // 验证日志级别
  const validLogLevels = ['debug', 'info', 'warn', 'error'];
  if (!validLogLevels.includes(config.logLevel)) {
    throw new Error(`LOG_LEVEL must be one of: ${validLogLevels.join(', ')}`);
  }
};

// 执行配置验证
validateConfig();

export default config;
