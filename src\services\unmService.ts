import { logger } from './../utils/logger';
import { APIError } from './../types/common';
import { MusicUrl } from './../types/api';
import { getDefaultSources } from './../config/sources';

// 导入UnblockNeteaseMusic核心功能
import match from '@unblockneteasemusic/server';

/**
 * UnblockNeteaseMusic服务类
 * 专门处理第三方音乐播放源
 */
export class UNMService {
  
  /**
   * 使用UNM获取音乐播放链接
   * @param songId 歌曲ID
   * @param sources 音源列表，如果为空则使用默认音源
   * @param quality 音质要求
   */
  async getMusicUrl(songId: string, sources?: string[], quality: number = 999): Promise<MusicUrl> {
    try {
      logger.info('UNM: Getting music URL', { songId, sources, quality });

      // 如果没有指定音源，使用默认音源
      const targetSources = sources && sources.length > 0 ? sources : getDefaultSources();
      
      // 转换songId为数字
      const numericSongId = parseInt(songId);
      if (isNaN(numericSongId)) {
        throw new APIError(400, 'Invalid song ID: must be numeric');
      }

      // 调用UNM匹配函数
      const result = await match(numericSongId, targetSources);
      
      if (!result) {
        throw new APIError(404, 'No matching song found from any source');
      }

      // 检查结果是否包含有效的URL
      if (!result.url) {
        throw new APIError(404, 'No playable URL found for this song');
      }

      logger.info('UNM: Music URL retrieved successfully', { 
        songId, 
        source: result.source || 'unknown',
        quality: result.br || quality,
        size: result.size || 0,
      });

      return {
        url: result.url,
        br: result.br || quality,
        size: result.size || 0,
        type: result.type || 'mp3',
        source: result.source || 'unknown',
      };

    } catch (error: any) {
      logger.error('UNM: Get music URL failed', { songId, sources, error });

      if (error instanceof APIError) {
        throw error;
      }

      // 处理UNM特定的错误
      if (error && typeof error === 'object' && error.message) {
        if (error.message.includes('timeout')) {
          throw new APIError(504, 'Music source timeout');
        }

        if (error.message.includes('network')) {
          throw new APIError(503, 'Music source network error');
        }

        throw new APIError(500, `UNM service error: ${error.message}`);
      }

      throw new APIError(500, 'UNM service error: Unknown error');
    }
  }

  /**
   * 批量获取音乐链接
   * @param songIds 歌曲ID列表
   * @param sources 音源列表
   * @param quality 音质要求
   */
  async getBatchMusicUrls(
    songIds: string[], 
    sources?: string[], 
    quality: number = 999,
  ): Promise<{ [songId: string]: MusicUrl | null }> {
    try {
      logger.info('UNM: Getting batch music URLs', { 
        songCount: songIds.length, 
        sources, 
        quality, 
      });

      const results: { [songId: string]: MusicUrl | null } = {};
      
      // 并发处理多个歌曲，但限制并发数量避免过载
      const concurrencyLimit = 5;
      const chunks = [];
      
      for (let i = 0; i < songIds.length; i += concurrencyLimit) {
        chunks.push(songIds.slice(i, i + concurrencyLimit));
      }

      for (const chunk of chunks) {
        const promises = chunk.map(async (songId) => {
          try {
            const result = await this.getMusicUrl(songId, sources, quality);
            return { songId, result };
          } catch (error: any) {
            const errorMessage = error && typeof error === 'object' && error.message ? error.message : 'Unknown error';
            logger.warn('UNM: Failed to get URL for song', { songId, error: errorMessage });
            return { songId, result: null };
          }
        });

        const chunkResults = await Promise.all(promises);
        chunkResults.forEach(({ songId, result }) => {
          results[songId] = result;
        });
      }

      const successCount = Object.values(results).filter(r => r !== null).length;
      logger.info('UNM: Batch music URLs completed', { 
        total: songIds.length, 
        success: successCount,
        failed: songIds.length - successCount,
      });

      return results;

    } catch (error: any) {
      logger.error('UNM: Batch get music URLs failed', { songIds, error });
      const errorMessage = error && typeof error === 'object' && error.message ? error.message : 'Unknown error';
      throw new APIError(500, `Batch UNM service error: ${errorMessage}`);
    }
  }

  /**
   * 测试音源可用性
   * @param sources 要测试的音源列表
   * @param testSongId 用于测试的歌曲ID（默认使用一个已知的歌曲ID）
   */
  async testSources(sources: string[], testSongId: string = '418602084'): Promise<{ [source: string]: boolean }> {
    try {
      logger.info('UNM: Testing sources availability', { sources, testSongId });

      const results: { [source: string]: boolean } = {};
      
      // 逐个测试每个音源
      for (const source of sources) {
        try {
          const result = await this.getMusicUrl(testSongId, [source]);
          results[source] = !!result.url;
          logger.debug('UNM: Source test result', { source, available: results[source] });
        } catch (error: any) {
          results[source] = false;
          const errorMessage = error && typeof error === 'object' && error.message ? error.message : 'Unknown error';
          logger.debug('UNM: Source test failed', { source, error: errorMessage });
        }
      }

      const availableCount = Object.values(results).filter(r => r).length;
      logger.info('UNM: Source testing completed', { 
        total: sources.length, 
        available: availableCount,
        results, 
      });

      return results;

    } catch (error: any) {
      logger.error('UNM: Source testing failed', { sources, error });
      const errorMessage = error && typeof error === 'object' && error.message ? error.message : 'Unknown error';
      throw new APIError(500, `Source testing error: ${errorMessage}`);
    }
  }

  /**
   * 获取UNM支持的音源列表
   */
  getSupportedSources(): string[] {
    try {
      // UNM支持的音源列表
      const supportedSources = [
        'pyncmd',      // pyncmd
        'netease',    // 网易云音乐
        'qq',         // QQ音乐
        'xiami',      // 虾米音乐
        'baidu',      // 百度音乐
        'kugou',      // 酷狗音乐
        'kuwo',       // 酷我音乐
        'migu',       // 咪咕音乐
        'joox',       // JOOX音乐
        'youtube',    // YouTube Music
        'bilibili',    // 哔哩哔哩
      ];

      logger.debug('UNM: Supported sources', { sources: supportedSources });
      return supportedSources;

    } catch (error) {
      logger.error('UNM: Get supported sources failed', { error });
      return [];
    }
  }

  /**
   * 获取UNM版本信息
   */
  getVersion(): string {
    try {
      // 在这种情况下，require 是更合适的选择，因为我们需要同步获取版本信息
      const packageInfo = require('@unblockneteasemusic/server/package.json');
      return packageInfo.version || 'unknown';
    } catch (error) {
      logger.warn('UNM: Could not get version info', { error });
      return 'unknown';
    }
  }
}

// 导出单例实例
export const unmService = new UNMService();
