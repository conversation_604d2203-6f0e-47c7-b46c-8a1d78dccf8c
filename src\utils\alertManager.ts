import { logger } from './logger';
import { metricsCollector, AggregatedMetrics } from './metricsCollector';
import { enhancedCacheService } from '../services/enhancedCacheService';
import { enhancedApiHealthService } from '../services/enhancedApiHealthService';

/**
 * 告警级别
 */
export enum AlertLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

/**
 * 告警类型
 */
export enum AlertType {
  ERROR_RATE = 'error_rate',
  RESPONSE_TIME = 'response_time',
  API_FAILURE = 'api_failure',
  CACHE_FAILURE = 'cache_failure',
  MEMORY_USAGE = 'memory_usage',
  SYSTEM_HEALTH = 'system_health',
  CUSTOM = 'custom'
}

/**
 * 告警规则
 */
export interface AlertRule {
  id: string;
  name: string;
  type: AlertType;
  level: AlertLevel;
  threshold: number;
  duration: number; // 持续时间（毫秒）
  enabled: boolean;
  description: string;
  condition: (metrics: any) => boolean;
  message: (metrics: any) => string;
}

/**
 * 告警事件
 */
export interface AlertEvent {
  id: string;
  ruleId: string;
  ruleName: string;
  type: AlertType;
  level: AlertLevel;
  message: string;
  timestamp: number;
  resolved: boolean;
  resolvedAt?: number;
  duration?: number;
  metadata?: any;
}

/**
 * 告警通知配置
 */
export interface AlertNotificationConfig {
  webhook?: {
    url: string;
    headers?: Record<string, string>;
  } | undefined;
  email?: {
    to: string[];
    subject: string;
  } | undefined;
  slack?: {
    webhook: string;
    channel: string;
  } | undefined;
}

/**
 * 智能告警管理器
 */
export class AlertManager {
  private rules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, AlertEvent> = new Map();
  private alertHistory: AlertEvent[] = [];
  private checkInterval: NodeJS.Timeout | null = null;
  private notificationConfig: AlertNotificationConfig = {};
  
  private readonly CHECK_INTERVAL = 30000; // 30秒检查一次
  private readonly MAX_HISTORY_SIZE = 1000;

  constructor() {
    this.initializeDefaultRules();
    this.loadNotificationConfig();
    this.startAlertChecking();
    
    logger.info('Alert manager initialized');
  }

  /**
   * 初始化默认告警规则
   */
  private initializeDefaultRules(): void {
    // 错误率告警
    this.addRule({
      id: 'high_error_rate',
      name: 'High Error Rate',
      type: AlertType.ERROR_RATE,
      level: AlertLevel.ERROR,
      threshold: 0.05, // 5%
      duration: 300000, // 5分钟
      enabled: true,
      description: 'API error rate exceeds 5%',
      condition: (metrics: AggregatedMetrics) => metrics.errorRate > 0.05,
      message: (metrics: AggregatedMetrics) => 
        `High error rate detected: ${(metrics.errorRate * 100).toFixed(2)}% (threshold: 5%)`
    });

    // 响应时间告警
    this.addRule({
      id: 'slow_response_time',
      name: 'Slow Response Time',
      type: AlertType.RESPONSE_TIME,
      level: AlertLevel.WARNING,
      threshold: 3000, // 3秒
      duration: 180000, // 3分钟
      enabled: true,
      description: 'Average response time exceeds 3 seconds',
      condition: (metrics: AggregatedMetrics) => metrics.averageResponseTime > 3000,
      message: (metrics: AggregatedMetrics) => 
        `Slow response time detected: ${metrics.averageResponseTime.toFixed(0)}ms (threshold: 3000ms)`
    });

    // API失败告警
    this.addRule({
      id: 'api_failure',
      name: 'API Failure',
      type: AlertType.API_FAILURE,
      level: AlertLevel.CRITICAL,
      threshold: 0.9, // 90%失败率
      duration: 60000, // 1分钟
      enabled: true,
      description: 'API failure rate exceeds 90%',
      condition: (metrics: AggregatedMetrics) => metrics.errorRate > 0.9,
      message: (metrics: AggregatedMetrics) => 
        `Critical API failure: ${(metrics.errorRate * 100).toFixed(2)}% error rate`
    });

    // 内存使用告警
    this.addRule({
      id: 'high_memory_usage',
      name: 'High Memory Usage',
      type: AlertType.MEMORY_USAGE,
      level: AlertLevel.WARNING,
      threshold: 0.8, // 80%
      duration: 300000, // 5分钟
      enabled: true,
      description: 'Memory usage exceeds 80%',
      condition: (_metrics: any) => {
        const memUsage = process.memoryUsage();
        return (memUsage.heapUsed / memUsage.heapTotal) > 0.8;
      },
      message: (_metrics: any) => {
        const memUsage = process.memoryUsage();
        const percentage = ((memUsage.heapUsed / memUsage.heapTotal) * 100).toFixed(2);
        return `High memory usage: ${percentage}% (${Math.round(memUsage.heapUsed / 1024 / 1024)}MB used)`;
      }
    });

    // 缓存失败告警
    this.addRule({
      id: 'cache_failure',
      name: 'Cache Failure',
      type: AlertType.CACHE_FAILURE,
      level: AlertLevel.ERROR,
      threshold: 0.5, // 50%命中率
      duration: 300000, // 5分钟
      enabled: true,
      description: 'Cache hit rate below 50%',
      condition: (_metrics: any) => {
        const cacheStats = enhancedCacheService.getStats();
        return cacheStats.hitRate < 0.5 && cacheStats.totalRequests > 10;
      },
      message: (_metrics: any) => {
        const cacheStats = enhancedCacheService.getStats();
        return `Low cache hit rate: ${(cacheStats.hitRate * 100).toFixed(2)}% (threshold: 50%)`;
      }
    });

    logger.info('Default alert rules initialized', {
      ruleCount: this.rules.size
    });
  }

  /**
   * 加载通知配置
   */
  private loadNotificationConfig(): void {
    const webhookConfig = process.env.ALERT_WEBHOOK_URL ? {
      url: process.env.ALERT_WEBHOOK_URL,
      headers: {
        'Content-Type': 'application/json'
      }
    } : undefined;

    const emailConfig = process.env.ALERT_EMAIL_TO ? {
      to: process.env.ALERT_EMAIL_TO.split(','),
      subject: 'UNM API Alert'
    } : undefined;

    const slackConfig = process.env.ALERT_SLACK_WEBHOOK ? {
      webhook: process.env.ALERT_SLACK_WEBHOOK,
      channel: process.env.ALERT_SLACK_CHANNEL || '#alerts'
    } : undefined;

    this.notificationConfig = {
      webhook: webhookConfig,
      email: emailConfig,
      slack: slackConfig
    };
  }

  /**
   * 开始告警检查
   */
  private startAlertChecking(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(() => {
      this.checkAlerts();
    }, this.CHECK_INTERVAL);

    logger.info('Alert checking started');
  }

  /**
   * 检查所有告警规则
   */
  private async checkAlerts(): Promise<void> {
    try {
      const metrics = metricsCollector.getAggregatedMetrics();
      const systemMetrics = metricsCollector.getSystemMetrics();
      const cacheStats = enhancedCacheService.getStats();
      const apiHealth = enhancedApiHealthService.getHealthCheckResult();

      const checkData = {
        ...metrics,
        system: systemMetrics,
        cache: cacheStats,
        api: apiHealth
      };

      for (const rule of this.rules.values()) {
        if (!rule.enabled) continue;

        try {
          const isTriggered = rule.condition(checkData);
          await this.processRuleResult(rule, isTriggered, checkData);
        } catch (error) {
          logger.error('Error checking alert rule', {
            ruleId: rule.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

    } catch (error) {
      logger.error('Error during alert checking', { error });
    }
  }

  /**
   * 处理规则检查结果
   */
  private async processRuleResult(rule: AlertRule, isTriggered: boolean, metrics: any): Promise<void> {
    const existingAlert = this.activeAlerts.get(rule.id);

    if (isTriggered) {
      if (!existingAlert) {
        // 创建新告警
        const alert: AlertEvent = {
          id: `${rule.id}_${Date.now()}`,
          ruleId: rule.id,
          ruleName: rule.name,
          type: rule.type,
          level: rule.level,
          message: rule.message(metrics),
          timestamp: Date.now(),
          resolved: false,
          metadata: {
            metrics: this.sanitizeMetrics(metrics),
            threshold: rule.threshold
          }
        };

        this.activeAlerts.set(rule.id, alert);
        this.alertHistory.push(alert);
        
        // 限制历史记录大小
        if (this.alertHistory.length > this.MAX_HISTORY_SIZE) {
          this.alertHistory.shift();
        }

        await this.sendNotification(alert);

        logger.warn('Alert triggered', {
          ruleId: rule.id,
          ruleName: rule.name,
          level: rule.level,
          message: alert.message
        });
      }
    } else {
      if (existingAlert && !existingAlert.resolved) {
        // 解决告警
        existingAlert.resolved = true;
        existingAlert.resolvedAt = Date.now();
        existingAlert.duration = existingAlert.resolvedAt - existingAlert.timestamp;

        this.activeAlerts.delete(rule.id);

        await this.sendResolutionNotification(existingAlert);

        logger.info('Alert resolved', {
          ruleId: rule.id,
          ruleName: rule.name,
          duration: existingAlert.duration
        });
      }
    }
  }

  /**
   * 发送告警通知
   */
  private async sendNotification(alert: AlertEvent): Promise<void> {
    try {
      // Webhook通知
      if (this.notificationConfig.webhook) {
        await this.sendWebhookNotification(alert);
      }

      // 其他通知方式可以在这里添加
      // if (this.notificationConfig.email) {
      //   await this.sendEmailNotification(alert);
      // }

    } catch (error) {
      logger.error('Error sending alert notification', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(alert: AlertEvent): Promise<void> {
    if (!this.notificationConfig.webhook) return;

    const payload = {
      type: 'alert',
      alert: {
        id: alert.id,
        rule: alert.ruleName,
        level: alert.level,
        message: alert.message,
        timestamp: new Date(alert.timestamp).toISOString(),
        metadata: alert.metadata
      },
      service: 'UNM API',
      environment: process.env.NODE_ENV || 'development'
    };

    try {
      const response = await fetch(this.notificationConfig.webhook.url, {
        method: 'POST',
        headers: this.notificationConfig.webhook.headers || {},
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Webhook request failed: ${response.status}`);
      }

      logger.debug('Webhook notification sent', {
        alertId: alert.id,
        webhookUrl: this.notificationConfig.webhook.url
      });

    } catch (error) {
      logger.error('Failed to send webhook notification', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送解决通知
   */
  private async sendResolutionNotification(alert: AlertEvent): Promise<void> {
    if (!this.notificationConfig.webhook) return;

    const payload = {
      type: 'resolution',
      alert: {
        id: alert.id,
        rule: alert.ruleName,
        level: alert.level,
        message: `RESOLVED: ${alert.message}`,
        timestamp: new Date(alert.timestamp).toISOString(),
        resolvedAt: new Date(alert.resolvedAt!).toISOString(),
        duration: alert.duration
      },
      service: 'UNM API',
      environment: process.env.NODE_ENV || 'development'
    };

    try {
      await fetch(this.notificationConfig.webhook.url, {
        method: 'POST',
        headers: this.notificationConfig.webhook.headers || {},
        body: JSON.stringify(payload)
      });

    } catch (error) {
      logger.error('Failed to send resolution notification', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 清理敏感指标数据
   */
  private sanitizeMetrics(metrics: any): any {
    // 移除敏感信息，只保留必要的指标
    return {
      totalRequests: metrics.totalRequests,
      successRate: metrics.successRate,
      errorRate: metrics.errorRate,
      averageResponseTime: metrics.averageResponseTime,
      timestamp: metrics.lastUpdated
    };
  }

  /**
   * 添加告警规则
   */
  addRule(rule: AlertRule): void {
    this.rules.set(rule.id, rule);
    logger.info('Alert rule added', {
      ruleId: rule.id,
      ruleName: rule.name,
      type: rule.type,
      level: rule.level
    });
  }

  /**
   * 移除告警规则
   */
  removeRule(ruleId: string): boolean {
    const removed = this.rules.delete(ruleId);
    if (removed) {
      // 同时移除相关的活跃告警
      this.activeAlerts.delete(ruleId);
      logger.info('Alert rule removed', { ruleId });
    }
    return removed;
  }

  /**
   * 获取所有告警规则
   */
  getRules(): AlertRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): AlertEvent[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * 获取告警历史
   */
  getAlertHistory(limit?: number): AlertEvent[] {
    return limit ? this.alertHistory.slice(-limit) : this.alertHistory;
  }

  /**
   * 手动触发告警检查
   */
  async triggerCheck(): Promise<void> {
    await this.checkAlerts();
  }

  /**
   * 停止告警检查
   */
  stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      logger.info('Alert checking stopped');
    }
  }
}

// 创建全局告警管理器实例
export const alertManager = new AlertManager();
