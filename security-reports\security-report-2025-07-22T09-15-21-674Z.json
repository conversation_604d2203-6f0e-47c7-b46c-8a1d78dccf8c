{"timestamp": "2025/7/22 17:15:21", "target": "http://localhost:5678", "overallScore": 81, "riskLevel": "中低风险", "summary": {"totalTests": 93, "passed": 63, "failed": 1, "warnings": 10, "info": 19}, "severitySummary": {"critical": 0, "high": 0, "medium": 5, "low": 6}, "categories": {"basic": {"summary": {"total": 31, "pass": 27, "fail": 1, "warning": 1, "info": 2}, "severitySummary": {"critical": 0, "high": 0, "medium": 2, "low": 0}, "highRiskIssues": [{"category": "HTTP方法", "test": "TRACE方法", "status": "FAIL", "message": "TRACE方法未被禁用，可能存在XST攻击风险", "severity": "MEDIUM"}], "allResults": [{"category": "连接性", "test": "基础连接", "status": "PASS", "message": "服务器正常响应"}, {"category": "HTTP方法", "test": "GET方法", "status": "PASS", "message": "方法正常工作"}, {"category": "HTTP方法", "test": "OPTIONS方法", "status": "PASS", "message": "方法正常工作"}, {"category": "HTTP方法", "test": "TRACE方法", "status": "FAIL", "message": "TRACE方法未被禁用，可能存在XST攻击风险", "severity": "MEDIUM"}, {"category": "输入验证", "test": "SQL注入防护", "status": "PASS", "message": "SQL注入payload被正确处理"}, {"category": "输入验证", "test": "SQL注入防护", "status": "PASS", "message": "SQL注入payload被正确处理"}, {"category": "输入验证", "test": "SQL注入防护", "status": "PASS", "message": "SQL注入payload被正确处理"}, {"category": "输入验证", "test": "SQL注入防护", "status": "PASS", "message": "SQL注入payload被正确处理"}, {"category": "输入验证", "test": "XSS防护", "status": "PASS", "message": "XSS payload被正确处理"}, {"category": "输入验证", "test": "XSS防护", "status": "PASS", "message": "XSS payload被正确处理"}, {"category": "输入验证", "test": "XSS防护", "status": "PASS", "message": "XSS payload被正确处理"}, {"category": "输入验证", "test": "XSS防护", "status": "PASS", "message": "XSS payload被正确处理"}, {"category": "输入验证", "test": "路径遍历防护", "status": "PASS", "message": "路径遍历payload被正确拒绝"}, {"category": "输入验证", "test": "路径遍历防护", "status": "PASS", "message": "路径遍历payload被正确拒绝"}, {"category": "输入验证", "test": "路径遍历防护", "status": "PASS", "message": "路径遍历payload被正确拒绝"}, {"category": "输入验证", "test": "路径遍历防护", "status": "PASS", "message": "路径遍历payload被正确拒绝"}, {"category": "参数安全", "test": "参数污染", "status": "INFO", "message": "测试重复参数处理", "details": {"status": 429, "data": {"code": 429, "message": "Too many requests, please try again later", "data": null, "timestamp": "2025-07-22T09:15:17.002Z"}}}, {"category": "头部安全", "test": "X-Forwarded-For头部注入", "status": "PASS", "message": "恶意头部被正常处理"}, {"category": "头部安全", "test": "User-Agent头部注入", "status": "PASS", "message": "恶意头部被正常处理"}, {"category": "头部安全", "test": "Referer头部注入", "status": "PASS", "message": "恶意头部被正常处理"}, {"category": "头部安全", "test": "X-Real-IP头部注入", "status": "PASS", "message": "恶意头部被正常处理"}, {"category": "频率限制", "test": "并发请求限制", "status": "PASS", "message": "频率限制正常工作"}, {"category": "信息泄露", "test": "错误信息泄露", "status": "PASS", "message": "错误信息被正确处理"}, {"category": "安全头部", "test": "x-content-type-options头部", "status": "PASS", "message": "安全头部存在: nosniff"}, {"category": "安全头部", "test": "x-frame-options头部", "status": "PASS", "message": "安全头部存在: SAMEORIGIN"}, {"category": "安全头部", "test": "x-xss-protection头部", "status": "PASS", "message": "安全头部存在: 0"}, {"category": "安全头部", "test": "strict-transport-security头部", "status": "PASS", "message": "安全头部存在: max-age=15552000; includeSubDomains"}, {"category": "安全头部", "test": "content-security-policy头部", "status": "WARNING", "message": "缺少重要的安全头部", "severity": "MEDIUM"}, {"category": "安全头部", "test": "referrer-policy头部", "status": "PASS", "message": "安全头部存在: no-referrer"}, {"category": "CORS", "test": "CORS配置", "status": "PASS", "message": "CORS配置相对安全"}, {"category": "缓存安全", "test": "Host头部操作", "status": "INFO", "message": "测试Host头部操作的影响", "details": {"status": 429}}]}, "api": {"score": 74, "criticalIssues": [], "highIssues": [], "mediumIssues": [], "allResults": [{"category": "API枚举", "test": "端点测试: /api/v1/search", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /api/v1/songs/123/url", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /api/v1/songs/123/lyrics", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /api/v1/albums/123/picture", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /api/v1/unm/songs/123/url", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /api/v1/unm/info", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /api/v1/cache/stats", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点发现: /health", "status": "INFO", "message": "端点可访问 (200)"}, {"category": "API枚举", "test": "端点测试: /info", "status": "INFO", "message": "状态码: 429"}, {"category": "API枚举", "test": "端点测试: /sources", "status": "INFO", "message": "状态码: 429"}, {"category": "参数注入", "test": "NoSQL注入", "status": "PASS", "message": "NoSQL注入payload被正确处理"}, {"category": "参数注入", "test": "NoSQL注入", "status": "PASS", "message": "NoSQL注入payload被正确处理"}, {"category": "参数注入", "test": "NoSQL注入", "status": "PASS", "message": "NoSQL注入payload被正确处理"}, {"category": "参数注入", "test": "NoSQL注入", "status": "PASS", "message": "NoSQL注入payload被正确处理"}, {"category": "参数注入", "test": "命令注入", "status": "PASS", "message": "命令注入payload被正确处理"}, {"category": "参数注入", "test": "命令注入", "status": "PASS", "message": "命令注入payload被正确处理"}, {"category": "参数注入", "test": "命令注入", "status": "PASS", "message": "命令注入payload被正确处理"}, {"category": "参数注入", "test": "命令注入", "status": "PASS", "message": "命令注入payload被正确处理"}, {"category": "参数注入", "test": "命令注入", "status": "PASS", "message": "命令注入payload被正确处理"}, {"category": "业务逻辑", "test": "参数边界测试", "status": "PASS", "message": "大limit值被正确拒绝"}, {"category": "业务逻辑", "test": "负数参数测试", "status": "PASS", "message": "负数参数被正确拒绝"}, {"category": "业务逻辑", "test": "空参数测试", "status": "PASS", "message": "空参数被正确拒绝"}, {"category": "缓存安全", "test": "缓存键操作", "status": "PASS", "message": "恶意缓存键被正确拒绝"}, {"category": "缓存安全", "test": "缓存键操作", "status": "PASS", "message": "恶意缓存键被正确拒绝"}, {"category": "缓存安全", "test": "缓存键操作", "status": "PASS", "message": "恶意缓存键被正确拒绝"}, {"category": "缓存安全", "test": "缓存键操作", "status": "PASS", "message": "恶意缓存键被正确拒绝"}, {"category": "缓存安全", "test": "缓存管理权限", "status": "PASS", "message": "缓存管理接口有适当保护"}, {"category": "文件上传", "test": "恶意文件上传: shell.php", "status": "PASS", "message": "恶意文件被正确拒绝"}, {"category": "文件上传", "test": "恶意文件上传: script.js", "status": "PASS", "message": "恶意文件被正确拒绝"}, {"category": "文件上传", "test": "恶意文件上传: test.exe", "status": "PASS", "message": "恶意文件被正确拒绝"}, {"category": "API版本", "test": "版本枚举: v0", "status": "PASS", "message": "未授权版本被正确拒绝"}, {"category": "API版本", "test": "版本枚举: v2", "status": "PASS", "message": "未授权版本被正确拒绝"}, {"category": "API版本", "test": "版本枚举: v1.1", "status": "PASS", "message": "未授权版本被正确拒绝"}, {"category": "API版本", "test": "版本枚举: beta", "status": "PASS", "message": "未授权版本被正确拒绝"}, {"category": "API版本", "test": "版本枚举: admin", "status": "PASS", "message": "未授权版本被正确拒绝"}, {"category": "API版本", "test": "版本枚举: internal", "status": "PASS", "message": "未授权版本被正确拒绝"}, {"category": "响应操作", "test": "JSONP注入", "status": "PASS", "message": "JSONP回调被正确处理"}, {"category": "响应操作", "test": "内容类型操作", "status": "PASS", "message": "内容类型保持一致"}, {"category": "时序攻击", "test": "响应时间分析", "status": "PASS", "message": "响应时间相对一致"}, {"category": "资源耗尽", "test": "并发请求测试", "status": "PASS", "message": "并发请求被适当限制"}, {"category": "资源耗尽", "test": "性能统计", "status": "INFO", "message": "50个请求耗时52ms，成功0个，限制50个"}, {"category": "数据泄露", "test": "调试信息泄露", "status": "PASS", "message": "无调试信息泄露"}, {"category": "数据泄露", "test": "路径信息泄露", "status": "PASS", "message": "无内部路径泄露"}]}, "network": {"score": 72, "criticalIssues": 0, "highIssues": 0, "mediumIssues": 3, "lowIssues": 6, "allResults": [{"category": "端口扫描", "test": "开放端口统计", "status": "PASS", "message": "端口暴露面较小"}, {"category": "HTTP协议", "test": "HTTP/1.0支持", "status": "WARNING", "message": "HTTP/1.0协议被支持，可能存在安全风险", "severity": "LOW"}, {"category": "HTTP协议", "test": "HTTP管道化", "status": "INFO", "message": "HTTP管道化正常工作"}, {"category": "HTTP协议", "test": "大请求头处理", "status": "WARNING", "message": "可能允许过大的请求头", "severity": "MEDIUM"}, {"category": "TLS安全", "test": "HTTPS支持", "status": "WARNING", "message": "服务未启用HTTPS", "severity": "MEDIUM"}, {"category": "网络拓扑", "test": "路由跟踪", "status": "INFO", "message": "本地服务，跳过路由跟踪"}, {"category": "性能测试", "test": "响应时间", "status": "PASS", "message": "平均响应时间: 1.00ms"}, {"category": "性能测试", "test": "并发处理", "status": "INFO", "message": "20个并发请求耗时18ms，成功20个"}, {"category": "性能测试", "test": "并发稳定性", "status": "PASS", "message": "并发处理稳定"}, {"category": "协议攻击", "test": "慢速HTTP攻击", "status": "WARNING", "message": "可能容易受到慢速HTTP攻击", "severity": "MEDIUM"}, {"category": "协议攻击", "test": "HTTP请求走私", "status": "PASS", "message": "HTTP请求走私被正确防护"}, {"category": "防火墙过滤", "test": "恶意UA过滤: sqlmap/1.0", "status": "WARNING", "message": "恶意User-Agent未被过滤", "severity": "LOW"}, {"category": "防火墙过滤", "test": "恶意UA过滤: Nikto/2.1.6", "status": "WARNING", "message": "恶意User-Agent未被过滤", "severity": "LOW"}, {"category": "防火墙过滤", "test": "恶意UA过滤: <PERSON><PERSON><PERSON>", "status": "WARNING", "message": "恶意User-Agent未被过滤", "severity": "LOW"}, {"category": "防火墙过滤", "test": "恶意UA过滤: OpenVAS", "status": "WARNING", "message": "恶意User-Agent未被过滤", "severity": "LOW"}, {"category": "防火墙过滤", "test": "恶意UA过滤: w3af.org", "status": "WARNING", "message": "恶意User-Agent未被过滤", "severity": "LOW"}, {"category": "防火墙过滤", "test": "IP头部过滤: X-Forwarded-For", "status": "INFO", "message": "IP头部被正常处理"}, {"category": "防火墙过滤", "test": "IP头部过滤: X-Real-IP", "status": "INFO", "message": "IP头部被正常处理"}, {"category": "防火墙过滤", "test": "IP头部过滤: X-Originating-IP", "status": "INFO", "message": "IP头部被正常处理"}]}}, "recommendations": ["🌐 加强网络层安全配置", "🔒 实施HTTPS和强TLS配置", "🛡️ 部署Web应用防火墙(WAF)", "📊 建立安全监控和日志分析", "🔄 定期进行安全评估和渗透测试", "👥 加强安全意识培训", "📋 制定安全事件响应计划"], "criticalIssues": [{"category": "HTTP方法", "test": "TRACE方法", "status": "FAIL", "message": "TRACE方法未被禁用，可能存在XST攻击风险", "severity": "MEDIUM"}]}