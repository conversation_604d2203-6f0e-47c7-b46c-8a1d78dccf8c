# 📖 API 接口文档

UNM API Server 提供两套API接口：传统兼容API和现代RESTful API。

## 🔗 基础信息

- **Base URL**: `http://localhost:3000`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 🎯 传统兼容API

为了保持向后兼容，我们提供了与原版完全兼容的API接口。

### 搜索音乐

**接口地址**: `GET /api.php`

**参数说明**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| types | string | ✅ | 操作类型，固定为 `search` | search |
| source | string | ❌ | 音源名称 | netease |
| name | string | ✅ | 搜索关键词 | 周杰伦 |
| count | number | ❌ | 返回数量，默认20，最大100 | 10 |
| pages | number | ❌ | 页码，默认1 | 1 |

**请求示例**:
```bash
curl "http://localhost:3000/api.php?types=search&source=netease&name=周杰伦&count=10&pages=1"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "418602084",
      "name": "青花瓷",
      "artist": "周杰伦",
      "album": "我很忙",
      "pic_id": "109951163076136830",
      "url_id": "418602084",
      "lyric_id": "418602084",
      "source": "netease"
    }
  ],
  "timestamp": "2025-01-23T10:30:00.000Z"
}
```

### 获取音乐播放链接

**接口地址**: `GET /api.php`

**参数说明**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| types | string | ✅ | 操作类型，固定为 `url` | url |
| source | string | ❌ | 音源名称 | netease |
| id | string | ✅ | 歌曲ID | 418602084 |
| br | number | ❌ | 音质，支持128/192/320/740/999 | 320 |

**请求示例**:
```bash
curl "http://localhost:3000/api.php?types=url&source=netease&id=418602084&br=320"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "url": "https://music.example.com/song.mp3",
    "br": 320,
    "size": 8547234,
    "type": "mp3"
  },
  "timestamp": "2025-01-23T10:30:00.000Z"
}
```

### 获取专辑图片

**接口地址**: `GET /api.php`

**参数说明**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| types | string | ✅ | 操作类型，固定为 `pic` | pic |
| source | string | ❌ | 音源名称 | netease |
| id | string | ✅ | 图片ID | 109951163076136830 |
| size | number | ❌ | 图片尺寸，默认300 | 500 |

**请求示例**:
```bash
curl "http://localhost:3000/api.php?types=pic&source=netease&id=109951163076136830&size=500"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "url": "https://music.example.com/album.jpg",
    "size": 500
  },
  "timestamp": "2025-01-23T10:30:00.000Z"
}
```

### 获取歌词

**接口地址**: `GET /api.php`

**参数说明**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| types | string | ✅ | 操作类型，固定为 `lyric` | lyric |
| source | string | ❌ | 音源名称 | netease |
| id | string | ✅ | 歌曲ID | 418602084 |

**请求示例**:
```bash
curl "http://localhost:3000/api.php?types=lyric&source=netease&id=418602084"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "lyric": "[00:00.00]青花瓷 - 周杰伦\n[00:20.60]素胚勾勒出青花笔锋浓转淡\n[00:25.30]瓶身描绘的牡丹一如你初妆...",
    "tlyric": "[00:00.00]Blue and White Porcelain - Jay Chou\n[00:20.60]Plain embryo outlines blue and white brush strokes..."
  },
  "timestamp": "2025-01-23T10:30:00.000Z"
}
```

## 🚀 现代RESTful API

现代化的RESTful API接口，提供更好的语义化和标准化。

### 搜索音乐

**接口地址**: `GET /api/v1/search`

**参数说明**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| keyword | string | ✅ | 搜索关键词 | 周杰伦 |
| source | string | ❌ | 音源名称 | netease |
| limit | number | ❌ | 返回数量，默认20，最大100 | 10 |
| offset | number | ❌ | 偏移量，默认0 | 0 |

**请求示例**:
```bash
curl "http://localhost:3000/api/v1/search?keyword=周杰伦&source=netease&limit=10&offset=0"
```

### 获取音乐播放链接

**接口地址**: `GET /api/v1/songs/:id/url`

**路径参数**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| id | string | ✅ | 歌曲ID | 418602084 |

**查询参数**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| source | string | ❌ | 音源名称 | netease |
| quality | number | ❌ | 音质，支持128/192/320/740/999 | 320 |

**请求示例**:
```bash
curl "http://localhost:3000/api/v1/songs/418602084/url?source=netease&quality=320"
```

### 获取专辑图片

**接口地址**: `GET /api/v1/songs/:id/picture`

**路径参数**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| id | string | ✅ | 歌曲ID | 418602084 |

**查询参数**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| source | string | ❌ | 音源名称 | netease |
| size | number | ❌ | 图片尺寸，默认300 | 500 |

**请求示例**:
```bash
curl "http://localhost:3000/api/v1/songs/418602084/picture?source=netease&size=500"
```

### 获取歌词

**接口地址**: `GET /api/v1/songs/:id/lyrics`

**路径参数**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| id | string | ✅ | 歌曲ID | 418602084 |

**查询参数**:

| 参数 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| source | string | ❌ | 音源名称 | netease |

**请求示例**:
```bash
curl "http://localhost:3000/api/v1/songs/418602084/lyrics?source=netease"
```

## 🔧 系统API

### 健康检查

**接口地址**: `GET /health`

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-23T10:30:00.000Z",
  "uptime": 3600,
  "version": "1.0.0"
}
```

### 系统信息

**接口地址**: `GET /info`

**响应示例**:
```json
{
  "name": "UNM API Server",
  "version": "1.0.0",
  "description": "UnblockNeteaseMusic API Server",
  "author": "UNM API Server Contributors",
  "repository": "https://github.com/your-username/unm-api-server",
  "supportedSources": ["netease", "qq", "kuwo", "kugou", "xiami"],
  "endpoints": {
    "search": "/api.php?types=search",
    "url": "/api.php?types=url",
    "picture": "/api.php?types=pic",
    "lyric": "/api.php?types=lyric"
  }
}
```

### 缓存管理

**获取缓存统计**: `GET /cache/stats`

**清空缓存**: `DELETE /cache`

**响应示例**:
```json
{
  "stats": {
    "hits": 1250,
    "misses": 180,
    "hitRate": 0.874,
    "size": 45,
    "maxSize": 1000
  }
}
```

### 性能指标

**接口地址**: `GET /metrics`

**响应示例**:
```json
{
  "requests": {
    "total": 1430,
    "successful": 1380,
    "failed": 50,
    "successRate": 0.965
  },
  "response": {
    "averageTime": 245,
    "p95Time": 580,
    "p99Time": 1200
  },
  "cache": {
    "hitRate": 0.874,
    "size": 45
  },
  "system": {
    "uptime": 3600,
    "memory": {
      "used": 128,
      "total": 512
    },
    "cpu": {
      "usage": 15.6
    }
  }
}
```

## 🎵 支持的音源

| 音源 | 标识符 | 描述 | 状态 |
|------|--------|------|------|
| 网易云音乐 | `netease` | 网易云音乐官方API | ✅ 稳定 |
| QQ音乐 | `qq` | QQ音乐官方API | ✅ 稳定 |
| 酷我音乐 | `kuwo` | 酷我音乐API | ✅ 稳定 |
| 酷狗音乐 | `kugou` | 酷狗音乐API | ⚠️ 部分支持 |
| 虾米音乐 | `xiami` | 虾米音乐API | ❌ 已停服 |
| 百度音乐 | `baidu` | 百度音乐API | ⚠️ 部分支持 |
| 咪咕音乐 | `migu` | 咪咕音乐API | ✅ 稳定 |
| JOOX音乐 | `joox` | JOOX音乐API | ⚠️ 部分支持 |
| YouTube Music | `youtube` | YouTube Music API | ⚠️ 部分支持 |
| 哔哩哔哩 | `bilibili` | 哔哩哔哩音频API | ⚠️ 部分支持 |
| Python网易云 | `pyncmd` | Python网易云音乐API | ✅ 稳定 |

## ❌ 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必需参数 |
| 401 | 未授权访问 | 检查API密钥或认证信息 |
| 403 | 禁止访问 | 检查IP白名单或访问权限 |
| 404 | 资源未找到 | 检查歌曲ID或音源是否正确 |
| 429 | 请求过于频繁 | 降低请求频率或等待限制解除 |
| 500 | 服务器内部错误 | 联系管理员或查看服务器日志 |
| 502 | 网关错误 | 检查上游服务状态 |
| 503 | 服务不可用 | 等待服务恢复或使用其他音源 |

## 📝 使用注意事项

1. **频率限制**: 默认每15分钟最多100次请求
2. **缓存机制**: 搜索结果缓存5分钟，音乐链接缓存30分钟
3. **音源降级**: 当主音源不可用时，自动尝试其他音源
4. **参数验证**: 所有参数都会进行严格验证
5. **错误处理**: 统一的错误响应格式
6. **日志记录**: 所有请求都会被记录用于监控和调试

## 🔍 调试技巧

### 启用调试日志

```bash
# 设置日志级别为debug
export LOG_LEVEL=debug
npm start
```

### 查看详细错误信息

在开发环境中，错误响应会包含详细的错误堆栈信息：

```json
{
  "code": 500,
  "message": "Internal server error",
  "data": null,
  "timestamp": "2025-01-23T10:30:00.000Z",
  "error": "Error: Connection timeout\n    at HttpService.get (/src/services/httpService.ts:95:15)"
}
```

### 测试API连通性

```bash
# 测试健康检查
curl http://localhost:3000/health

# 测试基础搜索功能
curl "http://localhost:3000/api.php?types=search&source=netease&name=test&count=1"
```
