import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

/**
 * User-Agent过滤中间件
 * 过滤恶意的User-Agent，防止自动化攻击工具
 */

// 恶意User-Agent模式列表
const maliciousUserAgents = [
  // 扫描工具
  'sqlmap', 'nikto', 'nessus', 'openvas', 'w3af', 'burp', 'nmap',
  'masscan', 'zap', 'skipfish', 'wpscan', 'dirb', 'dirbuster',
  'gobuster', 'ffuf', 'wfuzz', 'hydra', 'medusa', 'john',
  
  // 爬虫和机器人
  'scrapy', 'crawler', 'spider', 'bot', 'scraper',
  
  // 攻击工具
  'metasploit', 'exploit', 'payload', 'shellcode',
  
  // 其他可疑模式
  'test', 'scan', 'probe', 'hack', 'attack'
];

// 可疑User-Agent模式（记录但不阻止）
const suspiciousUserAgents = [
  'curl', 'wget', 'python', 'java', 'go-http-client',
  'postman', 'insomnia', 'httpie'
];

export const userAgentFilter = (req: Request, res: Response, next: NextFunction): void | Response => {
  const userAgent = req.get('User-Agent')?.toLowerCase() || '';
  
  // 检查是否为空User-Agent
  if (!userAgent || userAgent.trim() === '') {
    logger.warn('Empty User-Agent detected', {
      ip: req.ip,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
    
    // 根据配置决定是否阻止空User-Agent
    if (process.env.BLOCK_EMPTY_USER_AGENT === 'true') {
      return res.status(403).json({
        code: 403,
        message: 'Forbidden - User-Agent header is required',
        data: null,
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // 检查恶意User-Agent
  const isMalicious = maliciousUserAgents.some(pattern => 
    userAgent.includes(pattern.toLowerCase())
  );
  
  if (isMalicious) {
    logger.warn('Malicious User-Agent detected', {
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
    
    // 根据配置决定处理方式
    if (process.env.BLOCK_MALICIOUS_USER_AGENT !== 'false') {
      return res.status(403).json({
        code: 403,
        message: 'Forbidden - Malicious User-Agent detected',
        data: {
          reason: 'Security policy violation',
          blocked_pattern: 'Automated scanning tool detected'
        },
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // 检查可疑User-Agent（仅记录）
  const isSuspicious = suspiciousUserAgents.some(pattern => 
    userAgent.includes(pattern.toLowerCase())
  );
  
  if (isSuspicious) {
    logger.info('Suspicious User-Agent detected', {
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  // 检查User-Agent长度
  const maxUserAgentLength = parseInt(process.env.MAX_USER_AGENT_LENGTH || '512');
  if (req.get('User-Agent') && req.get('User-Agent')!.length > maxUserAgentLength) {
    logger.warn('Oversized User-Agent detected', {
      userAgent: req.get('User-Agent')?.substring(0, 100) + '...',
      length: req.get('User-Agent')!.length,
      maxLength: maxUserAgentLength,
      ip: req.ip,
      url: req.url,
      timestamp: new Date().toISOString()
    });
    
    return res.status(400).json({
      code: 400,
      message: 'Bad Request - User-Agent header too long',
      data: {
        current_length: req.get('User-Agent')!.length,
        max_length: maxUserAgentLength
      },
      timestamp: new Date().toISOString()
    });
  }
  
  // 记录正常的User-Agent（仅调试模式）
  if (config.logLevel === 'debug' && !isMalicious && !isSuspicious) {
    logger.debug('User-Agent check passed', {
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      url: req.url
    });
  }
  
  next();
};
