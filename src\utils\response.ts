import { Response } from 'express';
import { APIResponse } from './../types/api';

/**
 * 发送成功响应
 */
export const sendSuccess = <T>(
  res: Response,
  data: T,
  message = 'Success',
  statusCode = 200,
): void => {
  const response: APIResponse<T> = {
    code: statusCode,
    message,
    data,
    timestamp: new Date().toISOString(),
  };
  
  res.status(statusCode).json(response);
};

/**
 * 发送错误响应
 */
export const sendError = (
  res: Response,
  message: string,
  statusCode = 500,
  error?: string,
): void => {
  const response: APIResponse<null> = {
    code: statusCode,
    message,
    data: null,
    timestamp: new Date().toISOString(),
  };
  
  // 在开发环境中包含详细错误信息
  if (error && process.env.NODE_ENV === 'development') {
    (response as any).error = error;
  }
  
  res.status(statusCode).json(response);
};

/**
 * 发送验证错误响应
 */
export const sendValidationError = (
  res: Response,
  errors: string[],
): void => {
  sendError(res, `Validation failed: ${errors.join(', ')}`, 400);
};

/**
 * 发送未找到响应
 */
export const sendNotFound = (
  res: Response,
  resource = 'Resource',
): void => {
  sendError(res, `${resource} not found`, 404);
};

/**
 * 发送未授权响应
 */
export const sendUnauthorized = (
  res: Response,
  message = 'Unauthorized',
): void => {
  sendError(res, message, 401);
};

/**
 * 发送禁止访问响应
 */
export const sendForbidden = (
  res: Response,
  message = 'Forbidden',
): void => {
  sendError(res, message, 403);
};

/**
 * 发送频率限制响应
 */
export const sendRateLimitError = (
  res: Response,
  message = 'Too many requests, please try again later',
): void => {
  sendError(res, message, 429);
};

/**
 * 创建成功响应对象
 */
export const createSuccessResponse = <T>(
  data: T,
  message = 'Success',
  statusCode = 200,
): APIResponse<T> => {
  return {
    code: statusCode,
    message,
    data,
    timestamp: new Date().toISOString(),
  };
};

/**
 * 创建错误响应对象
 */
export const createErrorResponse = (
  message: string,
  statusCode = 500,
  error?: string,
): APIResponse<null> => {
  const response: APIResponse<null> = {
    code: statusCode,
    message,
    data: null,
    timestamp: new Date().toISOString(),
  };

  // 在开发环境中包含详细错误信息
  if (error && process.env.NODE_ENV === 'development') {
    (response as any).error = error;
  }

  return response;
};
