
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNM API Server 安全评估报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }
        .content { padding: 30px; }
        .score-card { display: flex; justify-content: space-around; margin: 20px 0; }
        .score-item { text-align: center; padding: 20px; border-radius: 10px; margin: 0 10px; flex: 1; }
        .score-high { background: #d4edda; color: #155724; }
        .score-medium { background: #fff3cd; color: #856404; }
        .score-low { background: #f8d7da; color: #721c24; }
        .section { margin: 30px 0; padding: 20px; border-left: 4px solid #667eea; background: #f8f9fa; }
        .issue-critical { color: #dc3545; font-weight: bold; }
        .issue-high { color: #fd7e14; font-weight: bold; }
        .issue-medium { color: #ffc107; font-weight: bold; }
        .issue-low { color: #28a745; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .chart { width: 100%; height: 300px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .status-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 UNM API Server 安全评估报告</h1>
            <p>目标: http://localhost:5678</p>
            <p>生成时间: 2025/7/22 17:15:21</p>
        </div>
        
        <div class="content">
            <div class="score-card">
                <div class="score-item score-high">
                    <h2>81/100</h2>
                    <p>总体安全评分</p>
                </div>
                <div class="score-item score-high">
                    <h2>中低风险</h2>
                    <p>风险等级</p>
                </div>
                <div class="score-item">
                    <h2>93</h2>
                    <p>总测试项目</p>
                </div>
            </div>

            <div class="section">
                <h2>📊 测试统计</h2>
                <table>
                    <tr><th>状态</th><th>数量</th><th>百分比</th></tr>
                    <tr><td class="status-pass">✅ 通过</td><td>63</td><td>68%</td></tr>
                    <tr><td class="status-fail">❌ 失败</td><td>1</td><td>1%</td></tr>
                    <tr><td class="status-warning">⚠️ 警告</td><td>10</td><td>11%</td></tr>
                    <tr><td class="status-info">📋 信息</td><td>19</td><td>20%</td></tr>
                </table>
            </div>

            <div class="section">
                <h2>🚨 风险等级分布</h2>
                <table>
                    <tr><th>风险等级</th><th>数量</th><th>描述</th></tr>
                    <tr><td class="issue-critical">🔴 严重</td><td>0</td><td>需要立即修复的安全漏洞</td></tr>
                    <tr><td class="issue-high">🟠 高危</td><td>0</td><td>可能导致系统被攻破的问题</td></tr>
                    <tr><td class="issue-medium">🟡 中危</td><td>5</td><td>可能被利用的安全弱点</td></tr>
                    <tr><td class="issue-low">🟢 低危</td><td>6</td><td>安全配置改进建议</td></tr>
                </table>
            </div>

            
            <div class="section">
                <h2>🚨 严重安全问题</h2>
                <ul>
                    <li class="issue-critical">[HTTP方法] TRACE方法: TRACE方法未被禁用，可能存在XST攻击风险</li>
                </ul>
            </div>
            

            <div class="section">
                <h2>📋 分类测试结果</h2>
                <h3>🔐 基础安全测试</h3>
                <p>评分: 93/100</p>
                
                <h3>🔌 API安全测试</h3>
                <p>评分: 74/100</p>
                
                <h3>🌐 网络安全测试</h3>
                <p>评分: 72/100</p>
            </div>

            <div class="recommendations">
                <h2>💡 安全改进建议</h2>
                <ul>
                    <li>🌐 加强网络层安全配置</li><li>🔒 实施HTTPS和强TLS配置</li><li>🛡️ 部署Web应用防火墙(WAF)</li><li>📊 建立安全监控和日志分析</li><li>🔄 定期进行安全评估和渗透测试</li><li>👥 加强安全意识培训</li><li>📋 制定安全事件响应计划</li>
                </ul>
            </div>

            <div class="section">
                <h2>📝 详细说明</h2>
                <p><strong>测试范围：</strong>本次安全评估涵盖了Web应用安全、API安全、网络安全等多个维度。</p>
                <p><strong>测试方法：</strong>采用自动化安全扫描和手工渗透测试相结合的方式。</p>
                <p><strong>风险评估：</strong>基于OWASP Top 10和行业最佳实践进行风险评级。</p>
                <p><strong>建议优先级：</strong>建议优先修复严重和高危问题，然后逐步改进中低危问题。</p>
            </div>
        </div>
    </div>
</body>
</html>