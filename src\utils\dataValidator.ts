import { MusicSearchResult, MusicUrl, MusicPicture, MusicLyric } from '../types/music';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number; // 数据质量评分 0-100
}

export interface DataQualityMetrics {
  totalValidated: number;
  validCount: number;
  invalidCount: number;
  averageScore: number;
  commonErrors: Map<string, number>;
}

/**
 * 数据验证框架
 * 验证音乐数据的完整性和真实性
 */
export class DataValidator {
  private static instance: DataValidator;
  private metrics: DataQualityMetrics = {
    totalValidated: 0,
    validCount: 0,
    invalidCount: 0,
    averageScore: 0,
    commonErrors: new Map(),
  };

  private constructor() {}

  public static getInstance(): DataValidator {
    if (!DataValidator.instance) {
      DataValidator.instance = new DataValidator();
    }
    return DataValidator.instance;
  }

  /**
   * 验证搜索结果数据
   */
  validateSearchResults(results: MusicSearchResult[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!Array.isArray(results)) {
      errors.push('Search results must be an array');
      return { isValid: false, errors, warnings, score: 0 };
    }

    if (results.length === 0) {
      warnings.push('Empty search results');
      score -= 10;
    }

    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      if (result) {
        const itemErrors = this.validateSearchResultItem(result, i);
        errors.push(...itemErrors);

        // 每个无效项目扣分
        if (itemErrors.length > 0) {
          score -= Math.min(20, itemErrors.length * 5);
        }
      }
    }

    // 检查重复项
    const duplicates = this.findDuplicateResults(results);
    if (duplicates.length > 0) {
      warnings.push(`Found ${duplicates.length} duplicate results`);
      score -= duplicates.length * 2;
    }

    const isValid = errors.length === 0;
    this.updateMetrics(isValid, Math.max(0, score));

    return {
      isValid,
      errors,
      warnings,
      score: Math.max(0, score),
    };
  }

  /**
   * 验证单个搜索结果项
   */
  private validateSearchResultItem(result: MusicSearchResult, index: number): string[] {
    const errors: string[] = [];

    if (!result.id || typeof result.id !== 'string') {
      errors.push(`Item ${index}: Invalid or missing ID`);
    }

    if (!result.name || typeof result.name !== 'string' || result.name.trim().length === 0) {
      errors.push(`Item ${index}: Invalid or missing name`);
    }

    if (!result.artist) {
      errors.push(`Item ${index}: Missing artist information`);
    } else if (Array.isArray(result.artist)) {
      if (result.artist.length === 0) {
        errors.push(`Item ${index}: Empty artist array`);
      }
    } else if (typeof result.artist !== 'string' || result.artist.trim().length === 0) {
      errors.push(`Item ${index}: Invalid artist format`);
    }

    if (!result.source || typeof result.source !== 'string') {
      errors.push(`Item ${index}: Invalid or missing source`);
    }

    // 检查可疑的模拟数据模式
    if (this.isSuspiciousData(result)) {
      errors.push(`Item ${index}: Suspicious or simulated data detected`);
    }

    return errors;
  }

  /**
   * 验证音乐URL数据
   */
  validateMusicUrl(musicUrl: MusicUrl): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!musicUrl.url || typeof musicUrl.url !== 'string') {
      errors.push('Invalid or missing music URL');
      score = 0;
    } else {
      // 验证URL格式
      try {
        new URL(musicUrl.url);
      } catch {
        errors.push('Invalid URL format');
        score -= 50;
      }

      // 检查URL是否指向真实的音频文件
      if (!this.isValidAudioUrl(musicUrl.url)) {
        warnings.push('URL may not point to a valid audio file');
        score -= 20;
      }
    }

    if (typeof musicUrl.br !== 'number' || musicUrl.br <= 0) {
      warnings.push('Invalid or missing bitrate');
      score -= 10;
    }

    if (typeof musicUrl.size !== 'number' || musicUrl.size < 0) {
      warnings.push('Invalid file size');
      score -= 5;
    }

    const isValid = errors.length === 0;
    this.updateMetrics(isValid, Math.max(0, score));

    return {
      isValid,
      errors,
      warnings,
      score: Math.max(0, score),
    };
  }

  /**
   * 验证图片数据
   */
  validatePicture(picture: MusicPicture): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!picture.url || typeof picture.url !== 'string') {
      errors.push('Invalid or missing picture URL');
      score = 0;
    } else {
      try {
        new URL(picture.url);
      } catch {
        errors.push('Invalid picture URL format');
        score -= 50;
      }

      if (!this.isValidImageUrl(picture.url)) {
        warnings.push('URL may not point to a valid image file');
        score -= 20;
      }
    }

    const isValid = errors.length === 0;
    this.updateMetrics(isValid, Math.max(0, score));

    return {
      isValid,
      errors,
      warnings,
      score: Math.max(0, score),
    };
  }

  /**
   * 验证歌词数据
   */
  validateLyrics(lyrics: MusicLyric): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 100;

    if (!lyrics.lyric || typeof lyrics.lyric !== 'string') {
      errors.push('Invalid or missing lyrics');
      score = 0;
    } else {
      if (lyrics.lyric.trim().length === 0) {
        warnings.push('Empty lyrics');
        score -= 30;
      }

      // 检查是否是占位符或模拟歌词
      if (this.isSuspiciousLyrics(lyrics.lyric)) {
        errors.push('Suspicious or placeholder lyrics detected');
        score -= 50;
      }
    }

    const isValid = errors.length === 0;
    this.updateMetrics(isValid, Math.max(0, score));

    return {
      isValid,
      errors,
      warnings,
      score: Math.max(0, score),
    };
  }

  /**
   * 检查是否是可疑的模拟数据
   */
  private isSuspiciousData(result: MusicSearchResult): boolean {
    const suspiciousPatterns = [
      /test|测试|demo|sample|example/i,
      /^(song|music|track)\s*\d+$/i,
      /^(artist|singer)\s*\d+$/i,
      /placeholder|占位符/i,
      /mock|fake|dummy/i,
    ];

    const textToCheck = `${result.name} ${result.artist}`.toLowerCase();
    return suspiciousPatterns.some(pattern => pattern.test(textToCheck));
  }

  /**
   * 检查是否是有效的音频URL
   */
  private isValidAudioUrl(url: string): boolean {
    const audioExtensions = ['.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a'];
    const lowerUrl = url.toLowerCase();
    
    return audioExtensions.some(ext => lowerUrl.includes(ext)) ||
           lowerUrl.includes('audio') ||
           lowerUrl.includes('music') ||
           lowerUrl.includes('stream');
  }

  /**
   * 检查是否是有效的图片URL
   */
  private isValidImageUrl(url: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
    const lowerUrl = url.toLowerCase();
    
    return imageExtensions.some(ext => lowerUrl.includes(ext)) ||
           lowerUrl.includes('image') ||
           lowerUrl.includes('picture') ||
           lowerUrl.includes('photo');
  }

  /**
   * 检查是否是可疑的歌词
   */
  private isSuspiciousLyrics(lyrics: string): boolean {
    const suspiciousPatterns = [
      /^(lyrics|歌词)\s*(not\s*found|未找到|暂无)/i,
      /^(test|测试|demo|sample)\s*lyrics/i,
      /placeholder|占位符/i,
      /lorem\s*ipsum/i,
      /^[a-z\s]*$/i, // 只有小写字母和空格，可能是占位符
    ];

    return suspiciousPatterns.some(pattern => pattern.test(lyrics.trim()));
  }

  /**
   * 查找重复的搜索结果
   */
  private findDuplicateResults(results: MusicSearchResult[]): MusicSearchResult[] {
    const seen = new Set<string>();
    const duplicates: MusicSearchResult[] = [];

    for (const result of results) {
      const key = `${result.name}-${result.artist}`;
      if (seen.has(key)) {
        duplicates.push(result);
      } else {
        seen.add(key);
      }
    }

    return duplicates;
  }

  /**
   * 更新验证指标
   */
  private updateMetrics(isValid: boolean, score: number): void {
    this.metrics.totalValidated++;
    
    if (isValid) {
      this.metrics.validCount++;
    } else {
      this.metrics.invalidCount++;
    }

    // 更新平均分数
    const totalScore = this.metrics.averageScore * (this.metrics.totalValidated - 1) + score;
    this.metrics.averageScore = totalScore / this.metrics.totalValidated;
  }

  /**
   * 获取数据质量指标
   */
  getQualityMetrics(): DataQualityMetrics {
    return { ...this.metrics };
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.metrics = {
      totalValidated: 0,
      validCount: 0,
      invalidCount: 0,
      averageScore: 0,
      commonErrors: new Map(),
    };
  }
}

// 导出单例实例
export const dataValidator = DataValidator.getInstance();
