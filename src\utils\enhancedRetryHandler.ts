import { logger } from './logger';

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition: (error: any) => boolean;
  onRetry?: (error: any, attempt: number) => void;
  timeout?: number;
}

/**
 * 重试统计信息
 */
export interface RetryStats {
  totalAttempts: number;
  successfulAttempts: number;
  failedAttempts: number;
  averageDelay: number;
  lastError?: any;
}

/**
 * 默认重试配置
 */
const DEFAULT_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error: any) => {
    // 默认重试条件：网络错误、超时、5xx服务器错误
    if (error.code === 'ECONNRESET' || 
        error.code === 'ENOTFOUND' || 
        error.code === 'ECONNREFUSED' ||
        error.code === 'ETIMEDOUT') {
      return true;
    }
    
    if (error.response) {
      const status = error.response.status;
      // 重试5xx错误和429（太多请求）
      return status >= 500 || status === 429;
    }
    
    return false;
  }
};

/**
 * 智能重试处理器类
 */
export class EnhancedRetryHandler {
  private stats: Map<string, RetryStats> = new Map();
  
  /**
   * 创建智能重试函数
   */
  createRetry<T>(config: Partial<RetryConfig> = {}): (operation: () => Promise<T>, operationId?: string) => Promise<T> {
    const finalConfig = { ...DEFAULT_CONFIG, ...config };
    
    return async (operation: () => Promise<T>, operationId: string = 'unknown'): Promise<T> => {
      let lastError: any;
      const startTime = Date.now();
      let totalDelay = 0;
      
      // 初始化统计信息
      if (!this.stats.has(operationId)) {
        this.stats.set(operationId, {
          totalAttempts: 0,
          successfulAttempts: 0,
          failedAttempts: 0,
          averageDelay: 0
        });
      }
      
      const stats = this.stats.get(operationId)!;
      
      for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
        stats.totalAttempts++;
        
        try {
          // 设置超时
          const result = finalConfig.timeout 
            ? await this.withTimeout(operation(), finalConfig.timeout)
            : await operation();
          
          stats.successfulAttempts++;
          stats.averageDelay = totalDelay / attempt;
          
          // 记录成功日志
          if (attempt > 1) {
            logger.info('Operation succeeded after retry', {
              operationId,
              attempt,
              totalTime: Date.now() - startTime,
              totalDelay
            });
          }
          
          return result;
        } catch (error) {
          lastError = error;
          stats.failedAttempts++;
          stats.lastError = error;
          
          // 检查是否应该重试
          if (!finalConfig.retryCondition(error) || attempt === finalConfig.maxAttempts) {
            logger.error('Operation failed after all retries', {
              operationId,
              attempt,
              maxAttempts: finalConfig.maxAttempts,
              error: error instanceof Error ? error.message : String(error),
              totalTime: Date.now() - startTime
            });
            throw error;
          }
          
          // 计算延迟时间（指数退避）
          const delay = Math.min(
            finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1),
            finalConfig.maxDelay
          );
          
          // 添加随机抖动（±25%）
          const jitter = delay * 0.25 * (Math.random() * 2 - 1);
          const finalDelay = Math.max(0, delay + jitter);
          
          totalDelay += finalDelay;
          
          logger.warn('Operation failed, retrying', {
            operationId,
            attempt,
            maxAttempts: finalConfig.maxAttempts,
            delay: finalDelay,
            error: error instanceof Error ? error.message : String(error),
            retryable: finalConfig.retryCondition(error)
          });
          
          // 调用重试回调
          if (finalConfig.onRetry) {
            finalConfig.onRetry(error, attempt);
          }
          
          // 等待延迟时间
          await this.sleep(finalDelay);
        }
      }
      
      throw lastError;
    };
  }
  
  /**
   * 为操作添加超时
   */
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timeout after ${timeoutMs}ms`)), timeoutMs);
    });
    
    return Promise.race([promise, timeoutPromise]);
  }
  
  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 获取重试统计信息
   */
  getStats(operationId?: string): RetryStats | Map<string, RetryStats> {
    if (operationId) {
      return this.stats.get(operationId) || {
        totalAttempts: 0,
        successfulAttempts: 0,
        failedAttempts: 0,
        averageDelay: 0
      };
    }
    return new Map(this.stats);
  }
  
  /**
   * 重置统计信息
   */
  resetStats(operationId?: string): void {
    if (operationId) {
      this.stats.delete(operationId);
    } else {
      this.stats.clear();
    }
  }
  
  /**
   * 获取成功率
   */
  getSuccessRate(operationId: string): number {
    const stats = this.stats.get(operationId);
    if (!stats || stats.totalAttempts === 0) {
      return 0;
    }
    return stats.successfulAttempts / stats.totalAttempts;
  }
}

// 创建全局重试处理器实例
export const retryHandler = new EnhancedRetryHandler();

// 预定义的重试配置
export const HTTP_RETRY_CONFIG: Partial<RetryConfig> = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  timeout: 30000,
  retryCondition: (error: any) => {
    // HTTP请求特定的重试条件
    if (error.code === 'ECONNRESET' || 
        error.code === 'ENOTFOUND' || 
        error.code === 'ECONNREFUSED' ||
        error.code === 'ETIMEDOUT' ||
        error.code === 'ECONNABORTED') {
      return true;
    }
    
    if (error.response) {
      const status = error.response.status;
      // 重试5xx错误、429（太多请求）、408（请求超时）
      return status >= 500 || status === 429 || status === 408;
    }
    
    return false;
  }
};

export const DATABASE_RETRY_CONFIG: Partial<RetryConfig> = {
  maxAttempts: 5,
  baseDelay: 500,
  maxDelay: 5000,
  backoffFactor: 1.5,
  timeout: 10000,
  retryCondition: (error: any) => {
    // 数据库连接特定的重试条件
    return error.code === 'ECONNRESET' || 
           error.code === 'ECONNREFUSED' ||
           error.message?.includes('connection') ||
           error.message?.includes('timeout');
  }
};

// 便捷函数
export const withHttpRetry = retryHandler.createRetry(HTTP_RETRY_CONFIG);
export const withDatabaseRetry = retryHandler.createRetry(DATABASE_RETRY_CONFIG);

/**
 * 装饰器模式的重试函数
 */
export function retry(config: Partial<RetryConfig> = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const retryFn = retryHandler.createRetry(config);
    
    descriptor.value = async function (...args: any[]) {
      return retryFn(() => method.apply(this, args), `${target.constructor.name}.${propertyName}`);
    };
  };
}
