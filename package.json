{"name": "unm-api-server", "version": "1.0.0", "description": "UnblockNeteaseMusic API Server - Modern RESTful music API service", "main": "dist/server.js", "scripts": {"dev": "tsx watch --tsconfig ./tsconfig.json src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "validate-config": "tsx scripts/validate-config.ts", "fix-env-format": "tsx scripts/fix-env-format.ts", "compare-env-files": "tsx scripts/compare-env-files.ts", "check-hardcode": "grep -r \"localhost:\" src/ || echo 'No hardcoded localhost found'", "config-help": "echo 'Available config commands:' && echo '  npm run validate-config - Validate all configuration' && echo '  npm run fix-env-format - Fix environment file formats' && echo '  npm run compare-env-files - Compare environment files consistency' && echo '  npm run check-hardcode - Check for hardcoded values' && echo '  See docs/CONFIGURATION.md for full documentation'", "security-test": "tsx scripts/security-test.ts", "security-test-api": "tsx scripts/api-security-test.ts", "security-test-network": "tsx scripts/network-security-test.ts", "security-test-full": "tsx scripts/comprehensive-security-report.ts", "security-help": "echo 'Available security commands:' && echo '  npm run security-test - Basic security tests' && echo '  npm run security-test-api - API security tests' && echo '  npm run security-test-network - Network security tests' && echo '  npm run security-test-full - Comprehensive security assessment'", "start-production": "tsx scripts/start-production.ts", "stop-production": "tsx scripts/stop-production.ts", "monitor-production": "tsx scripts/monitor-production.ts", "production-help": "echo 'Available production commands:' && echo '  npm run start-production - Start production server' && echo '  npm run stop-production - Stop production server' && echo '  npm run monitor-production start - Start monitoring' && echo '  npm run monitor-production report - Generate monitoring report'", "validate-real-data": "tsx scripts/validate-real-data.ts", "data-help": "echo 'Available data validation commands:' && echo '  npm run validate-real-data - Validate that all data sources are real (no mock/test data)' && echo '  See logs/ directory for detailed validation reports'", "optimization-setup": "tsx scripts/optimization-setup.ts", "optimization-status": "tsx scripts/optimization-setup.ts status", "optimization-deps": "tsx scripts/optimization-setup.ts deps", "optimization-install": "tsx scripts/optimization-setup.ts install", "optimization-help": "echo 'Available optimization commands:' && echo '  npm run optimization-setup - Run complete optimization setup' && echo '  npm run optimization-status - Show task status' && echo '  npm run optimization-deps - Check dependencies' && echo '  npm run optimization-install - Install new dependencies' && echo '  See OPTIMIZATION_TASKS.md for detailed task documentation'", "generate-docs": "tsx scripts/generate-docs.ts", "validate-docs": "tsx scripts/generate-docs.ts validate", "check-doc-links": "tsx scripts/generate-docs.ts check-links", "docs-all": "tsx scripts/generate-docs.ts all", "docs-help": "echo 'Available documentation commands:' && echo '  npm run generate-docs - Generate documentation index' && echo '  npm run validate-docs - Validate documentation completeness' && echo '  npm run check-doc-links - Check documentation links' && echo '  npm run docs-all - Run all documentation tasks' && echo '  See docs/README.md for complete documentation guide'"}, "keywords": ["unblockneteasemusic", "music", "api", "server", "express", "typescript"], "author": "UNM API Server Contributors", "license": "MIT", "dependencies": {"@unblockneteasemusic/server": "^0.27.10", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.1.0", "helmet": "^7.1.0", "ioredis": "^5.6.1", "joi": "^17.11.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "winston": "^3.11.0"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.11", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "axios": "^1.10.0", "eslint": "^8.56.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}