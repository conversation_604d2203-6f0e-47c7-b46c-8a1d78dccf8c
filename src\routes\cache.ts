import { Router, Request, Response, NextFunction } from 'express';
import { cacheService } from '../services/cacheService';
import { clearCacheByPattern, warmupCache } from '../middleware/cache';
import { createSuccessResponse } from '../utils/response';
import { APIError } from '../types/common';
import { logger } from '../utils/logger';

const router = Router();

/**
 * 获取缓存统计信息
 * GET /api/v1/cache/stats
 */
router.get('/api/v1/cache/stats', async (req: Request, res: Response, next: NextFunction) => {
  try {
    logger.info('Cache stats request received', { ip: req.ip });

    const stats = cacheService.getStats();
    
    const response = createSuccessResponse({
      ...stats,
      cache_enabled: true,
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Get cache stats failed', { error });
    next(error);
  }
});

/**
 * 获取所有缓存键
 * GET /api/v1/cache/keys?pattern=search
 */
router.get('/api/v1/cache/keys', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { pattern, limit = '100' } = req.query;
    
    logger.info('Cache keys request received', { 
      pattern, 
      limit, 
      ip: req.ip, 
    });

    let keys = cacheService.getKeys();
    
    // 如果提供了模式，过滤键
    if (pattern && typeof pattern === 'string') {
      keys = keys.filter(key => key.includes(pattern));
    }
    
    // 限制返回数量
    const limitNum = parseInt(limit as string);
    if (limitNum > 0) {
      keys = keys.slice(0, limitNum);
    }
    
    const response = createSuccessResponse({
      keys,
      total: keys.length,
      pattern: pattern || null,
      limit: limitNum,
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Get cache keys failed', { error });
    next(error);
  }
});

/**
 * 获取特定缓存项
 * GET /api/v1/cache/item/:key
 */
router.get('/api/v1/cache/item/:key', async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    const { key } = req.params;
    
    logger.info('Cache item request received', { 
      key, 
      ip: req.ip, 
    });

    // URL解码缓存键
    if (!key) {
      return res.status(400).json({
        code: 400,
        message: 'Cache key is required',
        data: null,
        timestamp: new Date().toISOString(),
      });
    }

    const decodedKey = decodeURIComponent(key);
    const data = cacheService.get(decodedKey);
    
    if (data === null) {
      throw new APIError(404, 'Cache item not found or expired');
    }
    
    const response = createSuccessResponse({
      key: decodedKey,
      data,
      cached_at: new Date().toISOString(),
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Get cache item failed', { error });
    next(error);
  }
});

/**
 * 删除特定缓存项
 * DELETE /api/v1/cache/item/:key
 */
router.delete('/api/v1/cache/item/:key', async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    const { key } = req.params;
    
    logger.info('Cache item delete request received', { 
      key, 
      ip: req.ip, 
    });

    if (!key) {
      return res.status(400).json({
        code: 400,
        message: 'Cache key is required',
        data: null,
        timestamp: new Date().toISOString(),
      });
    }

    const decodedKey = decodeURIComponent(key);
    const deleted = cacheService.delete(decodedKey);
    
    const response = createSuccessResponse({
      key: decodedKey,
      deleted,
      message: deleted ? 'Cache item deleted successfully' : 'Cache item not found',
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Delete cache item failed', { error });
    next(error);
  }
});

/**
 * 按模式清除缓存
 * DELETE /api/v1/cache/pattern/:pattern
 */
router.delete('/api/v1/cache/pattern/:pattern', async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    const { pattern } = req.params;
    
    logger.info('Cache pattern clear request received', { 
      pattern, 
      ip: req.ip, 
    });

    if (!pattern) {
      return res.status(400).json({
        code: 400,
        message: 'Cache pattern is required',
        data: null,
        timestamp: new Date().toISOString(),
      });
    }

    const decodedPattern = decodeURIComponent(pattern);
    const clearedCount = clearCacheByPattern(decodedPattern);
    
    const response = createSuccessResponse({
      pattern: decodedPattern,
      cleared_count: clearedCount,
      message: `Cleared ${clearedCount} cache items matching pattern`,
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Clear cache by pattern failed', { error });
    next(error);
  }
});

/**
 * 清空所有缓存
 * DELETE /api/v1/cache/all
 */
router.delete('/api/v1/cache/all', async (req: Request, res: Response, next: NextFunction) => {
  try {
    logger.info('Cache clear all request received', { ip: req.ip });

    const statsBefore = cacheService.getStats();
    cacheService.clear();
    
    const response = createSuccessResponse({
      message: 'All cache cleared successfully',
      previous_items: statsBefore.totalItems,
      cleared_at: new Date().toISOString(),
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Clear all cache failed', { error });
    next(error);
  }
});

/**
 * 预热缓存
 * POST /api/v1/cache/warmup
 */
router.post('/api/v1/cache/warmup', async (req: Request, res: Response, next: NextFunction) => {
  try {
    logger.info('Cache warmup request received', { ip: req.ip });

    await warmupCache();
    
    const stats = cacheService.getStats();
    const response = createSuccessResponse({
      message: 'Cache warmup completed',
      current_stats: stats,
      warmed_up_at: new Date().toISOString(),
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Cache warmup failed', { error });
    next(error);
  }
});

/**
 * 设置缓存项 (用于测试)
 * POST /api/v1/cache/item
 * Body: { "key": "test_key", "data": {...}, "ttl": 300000 }
 */
router.post('/api/v1/cache/item', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { key, data, ttl } = req.body;
    
    if (!key || data === undefined) {
      throw new APIError(400, 'Key and data are required');
    }
    
    logger.info('Cache item set request received', { 
      key, 
      ttl, 
      ip: req.ip, 
    });

    cacheService.set(key, data, ttl);
    
    const response = createSuccessResponse({
      key,
      message: 'Cache item set successfully',
      ttl: ttl || 'default',
      set_at: new Date().toISOString(),
    });
    
    res.json(response);
  } catch (error) {
    logger.error('Set cache item failed', { error });
    next(error);
  }
});

export default router;
