import { Request, Response, NextFunction } from 'express';
import { APIError } from './../types/common';
import { logger } from './../utils/logger';
import { sendError } from './../utils/response';

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
  error: Error | APIError,
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // 记录错误日志
  logger.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // 如果响应已经发送，则交给默认错误处理器
  if (res.headersSent) {
    return next(error);
  }

  // 处理API错误
  if (error instanceof APIError) {
    sendError(res, error.message, error.statusCode, error.details);
    return;
  }

  // 处理CORS错误
  if (error.message === 'Not allowed by CORS') {
    sendError(res, 'CORS policy violation', 403);
    return;
  }

  // 处理JSON解析错误
  if (error instanceof SyntaxError && 'body' in error) {
    sendError(res, 'Invalid JSON format', 400);
    return;
  }

  // 处理其他未知错误
  const message = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : error.message;
    
  sendError(res, message, 500, error.stack);
};
