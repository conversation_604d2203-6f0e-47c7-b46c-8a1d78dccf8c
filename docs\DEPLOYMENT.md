# 🚀 部署指南

本文档详细介绍了UNM API Server的各种部署方式和配置选项。

## 📋 部署前准备

### 系统要求

- **操作系统**: Linux (推荐 Ubuntu 20.04+), macOS, Windows
- **Node.js**: 18.0.0 或更高版本
- **内存**: 最低 512MB，推荐 1GB+
- **存储**: 最低 1GB 可用空间
- **网络**: 稳定的互联网连接

### 依赖服务 (可选)

- **Redis**: 用于分布式缓存 (推荐 6.0+)
- **Nginx**: 用于反向代理和负载均衡
- **Docker**: 用于容器化部署

## 🔧 环境配置

### 1. 环境变量配置

复制环境配置模板：

```bash
cp .env.template .env
```

编辑 `.env` 文件：

```bash
# 基础配置
NODE_ENV=production
PORT=3000
SERVER_HOST=0.0.0.0

# CORS配置
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# 缓存配置
CACHE_TTL=300
ENABLE_CACHE=true
CACHE_MAX_SIZE=1000
CACHE_CLEANUP_INTERVAL=300000

# Redis配置 (可选)
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 频率限制配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MESSAGE=Too many requests, please try again later

# 日志配置
LOG_LEVEL=info
LOG_FILE=

# 音乐服务配置
DEFAULT_SOURCE=netease
SUPPORTED_SOURCES=netease,kuwo,xiami,baidu,kugou,migu,joox,youtube,bilibili,pyncmd
STABLE_SOURCES=netease,kuwo,pyncmd

# 安全配置
ENABLE_HTTPS=false
HTTPS_PORT=443
SSL_KEY_PATH=
SSL_CERT_PATH=
MAX_HEADER_SIZE=8192

# API配置
API_VERSION=v1
API_BASE_PATH=/api
MAX_SEARCH_LIMIT=100
MAX_BATCH_SIZE=50
DEFAULT_SEARCH_LIMIT=20
DEFAULT_SEARCH_OFFSET=0
```

### 2. 生产环境优化配置

```bash
# 性能优化
NODE_OPTIONS="--max-old-space-size=1024"
UV_THREADPOOL_SIZE=16

# 进程管理
PM2_INSTANCES=max
PM2_EXEC_MODE=cluster

# 监控配置
METRICS_ENABLED=true
ALERT_ERROR_RATE_THRESHOLD=0.05
ALERT_RESPONSE_TIME_THRESHOLD=3000
```

## 🐳 Docker 部署

### 1. 使用预构建镜像

```bash
# 拉取镜像
docker pull your-registry/unm-api-server:latest

# 运行容器
docker run -d \
  --name unm-api-server \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e PORT=3000 \
  -v $(pwd)/logs:/app/logs \
  your-registry/unm-api-server:latest
```

### 2. 使用 Docker Compose

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  unm-api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - unm-api
    restart: unless-stopped

volumes:
  redis_data:
```

启动服务：

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f unm-api

# 停止服务
docker-compose down
```

### 3. 自定义构建

```bash
# 构建镜像
docker build -t unm-api-server:latest .

# 多阶段构建 (优化镜像大小)
docker build -f Dockerfile.prod -t unm-api-server:prod .
```

## 🖥️ 传统部署

### 1. 直接部署

```bash
# 克隆项目
git clone https://github.com/your-username/unm-api-server.git
cd unm-api-server

# 安装依赖
npm ci --only=production

# 构建项目
npm run build

# 启动服务
npm start
```

### 2. PM2 部署

安装 PM2：

```bash
npm install -g pm2
```

创建 `ecosystem.config.js` 文件：

```javascript
module.exports = {
  apps: [{
    name: 'unm-api-server',
    script: 'dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: 'logs/combined.log',
    out_file: 'logs/out.log',
    error_file: 'logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

启动服务：

```bash
# 启动生产环境
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 status

# 查看日志
pm2 logs unm-api-server

# 重启服务
pm2 restart unm-api-server

# 停止服务
pm2 stop unm-api-server
```

### 3. Systemd 服务

创建服务文件 `/etc/systemd/system/unm-api-server.service`：

```ini
[Unit]
Description=UNM API Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/unm-api-server
ExecStart=/usr/bin/node dist/server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重载配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start unm-api-server

# 开机自启
sudo systemctl enable unm-api-server

# 查看状态
sudo systemctl status unm-api-server
```

## 🌐 反向代理配置

### Nginx 配置

创建 `/etc/nginx/sites-available/unm-api-server` 文件：

```nginx
upstream unm_api {
    server 127.0.0.1:3000;
    # 如果有多个实例
    # server 127.0.0.1:3001;
    # server 127.0.0.1:3002;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # 日志配置
    access_log /var/log/nginx/unm-api-access.log;
    error_log /var/log/nginx/unm-api-error.log;
    
    # 限制请求大小
    client_max_body_size 1M;
    
    # 代理配置
    location / {
        proxy_pass http://unm_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://unm_api/health;
        access_log off;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用配置：

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/unm-api-server /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### Apache 配置

创建虚拟主机配置：

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # 代理配置
    ProxyPreserveHost On
    ProxyPass / http://127.0.0.1:3000/
    ProxyPassReverse / http://127.0.0.1:3000/
    
    # 安全头部
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    
    # 日志配置
    CustomLog /var/log/apache2/unm-api-access.log combined
    ErrorLog /var/log/apache2/unm-api-error.log
</VirtualHost>
```

## 🔒 SSL/TLS 配置

### 使用 Let's Encrypt

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 自签名证书 (仅用于测试)

```bash
# 生成私钥
openssl genrsa -out private.key 2048

# 生成证书
openssl req -new -x509 -key private.key -out certificate.crt -days 365
```

## 📊 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
mkdir -p /var/log/unm-api-server

# 配置日志轮转
sudo tee /etc/logrotate.d/unm-api-server << EOF
/var/log/unm-api-server/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload unm-api-server
    endscript
}
EOF
```

### 2. 监控配置

使用 PM2 监控：

```bash
# 安装 PM2 Plus
pm2 install pm2-server-monit

# 连接到 PM2 Plus
pm2 plus
```

使用 Prometheus + Grafana：

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'unm-api-server'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :3000
   
   # 杀死进程
   sudo kill -9 <PID>
   ```

2. **权限问题**
   ```bash
   # 修改文件权限
   sudo chown -R www-data:www-data /opt/unm-api-server
   sudo chmod -R 755 /opt/unm-api-server
   ```

3. **内存不足**
   ```bash
   # 增加交换空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 性能优化

1. **Node.js 优化**
   ```bash
   # 增加内存限制
   export NODE_OPTIONS="--max-old-space-size=2048"
   
   # 启用 V8 优化
   export NODE_OPTIONS="--optimize-for-size"
   ```

2. **系统优化**
   ```bash
   # 增加文件描述符限制
   echo "* soft nofile 65536" >> /etc/security/limits.conf
   echo "* hard nofile 65536" >> /etc/security/limits.conf
   
   # 优化网络参数
   echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
   sysctl -p
   ```

## 📋 部署检查清单

- [ ] 环境变量配置完成
- [ ] 依赖服务 (Redis) 正常运行
- [ ] 防火墙规则配置正确
- [ ] SSL证书配置完成
- [ ] 反向代理配置正确
- [ ] 日志目录权限正确
- [ ] 监控系统配置完成
- [ ] 备份策略制定完成
- [ ] 健康检查正常
- [ ] 性能测试通过
- [ ] 安全扫描通过

## 🔄 更新和维护

### 滚动更新

```bash
# 使用 PM2 进行零停机更新
pm2 reload unm-api-server

# 使用 Docker 进行更新
docker-compose pull
docker-compose up -d
```

### 备份策略

```bash
# 备份配置文件
tar -czf backup-$(date +%Y%m%d).tar.gz .env logs/ ecosystem.config.js

# 定期备份脚本
#!/bin/bash
BACKUP_DIR="/backup/unm-api-server"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz /opt/unm-api-server
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete
```
