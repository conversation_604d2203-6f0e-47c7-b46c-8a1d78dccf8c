#!/usr/bin/env tsx

/**
 * 真实数据验证脚本
 * 验证项目中是否还存在模拟数据、硬编码数据或测试数据
 */

import * as fs from 'fs';
import * as path from 'path';
import { SystemMonitor } from '../src/utils/systemMonitor';
import { apiHealthService } from '../src/services/apiHealthService';
import { dataValidator } from '../src/utils/dataValidator';

interface ValidationIssue {
  file: string;
  line: number;
  type: 'mock_data' | 'hardcoded' | 'test_data' | 'suspicious';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface ValidationReport {
  timestamp: string;
  totalFiles: number;
  issuesFound: number;
  issues: ValidationIssue[];
  systemCheck: {
    diskDataReal: boolean;
    networkDataReal: boolean;
    apiHealthWorking: boolean;
  };
  recommendations: string[];
}

class RealDataValidator {
  private issues: ValidationIssue[] = [];
  private readonly SUSPICIOUS_PATTERNS = [
    // 模拟数据模式 - 排除注释和文档
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*)(假设|假定|模拟|mock|fake|dummy)(?!.*-->)/gi, type: 'mock_data' as const, severity: 'high' as const },
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*)(测试.*数据|test.*data|demo.*data|sample.*data)(?!.*-->)/gi, type: 'test_data' as const, severity: 'medium' as const },

    // 硬编码模式 - 排除文档和配置说明
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*|echo.*)(localhost|127\.0\.0\.1)(?!.*-->)/g, type: 'hardcoded' as const, severity: 'medium' as const },
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*)(周杰伦|邓紫棋|林俊杰|薛之谦|毛不易)(?!.*-->)/g, type: 'hardcoded' as const, severity: 'high' as const },
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*)(418602084|109951163076136830)(?!.*-->)/g, type: 'hardcoded' as const, severity: 'high' as const },

    // 可疑数据模式 - 只检查实际代码中的硬编码值
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*)(diskTotal.*=.*100.*\*.*1024.*\*.*1024.*\*.*1024)(?!.*-->)/g, type: 'suspicious' as const, severity: 'critical' as const },
    { pattern: /(?<!\/\/.*|\/\*.*|\*.*|#.*|<!--.*)(connections.*:.*0.*\/\/.*简化版)(?!.*-->)/g, type: 'suspicious' as const, severity: 'critical' as const },
  ];

  private readonly EXCLUDE_PATTERNS = [
    /node_modules/,
    /\.git/,
    /dist/,
    /build/,
    /coverage/,
    /\.log$/,
    /\.lock$/,
    /package-lock\.json$/,
  ];

  async validateProject(): Promise<ValidationReport> {
    console.log('🔍 开始验证项目中的真实数据使用情况...\n');

    // 扫描文件
    await this.scanDirectory('.');

    // 系统检查
    const systemCheck = await this.performSystemChecks();

    // 生成建议
    const recommendations = this.generateRecommendations();

    const report: ValidationReport = {
      timestamp: new Date().toISOString(),
      totalFiles: this.getTotalFilesScanned(),
      issuesFound: this.issues.length,
      issues: this.issues,
      systemCheck,
      recommendations
    };

    return report;
  }

  private async scanDirectory(dirPath: string): Promise<void> {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      // 跳过排除的路径
      if (this.EXCLUDE_PATTERNS.some(pattern => pattern.test(fullPath))) {
        continue;
      }

      if (entry.isDirectory()) {
        await this.scanDirectory(fullPath);
      } else if (entry.isFile()) {
        await this.scanFile(fullPath);
      }
    }
  }

  private async scanFile(filePath: string): Promise<void> {
    // 只扫描代码文件
    const codeExtensions = ['.ts', '.js', '.tsx', '.jsx', '.html', '.css', '.json', '.md', '.env'];
    const ext = path.extname(filePath);
    
    if (!codeExtensions.includes(ext)) {
      return;
    }

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n');

      lines.forEach((line, index) => {
        this.SUSPICIOUS_PATTERNS.forEach(({ pattern, type, severity }) => {
          const matches = line.match(pattern);
          if (matches) {
            matches.forEach(match => {
              this.issues.push({
                file: filePath,
                line: index + 1,
                type,
                description: `发现${this.getTypeDescription(type)}: "${match}"`,
                severity
              });
            });
          }
        });
      });

    } catch (error) {
      console.warn(`无法读取文件 ${filePath}:`, error);
    }
  }

  private async performSystemChecks(): Promise<ValidationReport['systemCheck']> {
    console.log('🔧 执行系统检查...');

    const systemMonitor = SystemMonitor.getInstance();
    
    // 检查磁盘数据是否真实
    let diskDataReal = false;
    try {
      const diskUsage = await systemMonitor.getDiskUsage();
      // 如果有真实的磁盘数据，应该有多个分区或者不是固定的100GB
      diskDataReal = diskUsage.length > 0 && 
        !diskUsage.every(disk => disk.total === 100 * 1024 * 1024 * 1024);
    } catch (error) {
      console.warn('磁盘检查失败:', error);
    }

    // 检查网络数据是否真实
    let networkDataReal = false;
    try {
      const networkConnections = await systemMonitor.getNetworkConnections();
      // 如果有真实的网络数据，连接数应该大于0
      networkDataReal = networkConnections.total > 0;
    } catch (error) {
      console.warn('网络检查失败:', error);
    }

    // 检查API健康检查是否工作
    let apiHealthWorking = false;
    try {
      const healthStatus = apiHealthService.getHealthStatus();
      apiHealthWorking = healthStatus.apis.length > 0;
    } catch (error) {
      console.warn('API健康检查失败:', error);
    }

    return {
      diskDataReal,
      networkDataReal,
      apiHealthWorking
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const criticalIssues = this.issues.filter(issue => issue.severity === 'critical');
    const highIssues = this.issues.filter(issue => issue.severity === 'high');

    if (criticalIssues.length > 0) {
      recommendations.push('🚨 发现严重问题：存在明显的模拟数据，需要立即修复');
    }

    if (highIssues.length > 0) {
      recommendations.push('⚠️ 发现高优先级问题：存在硬编码数据，建议尽快修复');
    }

    const mockDataIssues = this.issues.filter(issue => issue.type === 'mock_data');
    if (mockDataIssues.length > 0) {
      recommendations.push('🔄 替换所有模拟数据为真实的系统监控数据');
    }

    const hardcodedIssues = this.issues.filter(issue => issue.type === 'hardcoded');
    if (hardcodedIssues.length > 0) {
      recommendations.push('⚙️ 将硬编码值移至环境变量或配置文件');
    }

    const testDataIssues = this.issues.filter(issue => issue.type === 'test_data');
    if (testDataIssues.length > 0) {
      recommendations.push('🧪 使用动态获取的真实数据替换测试数据');
    }

    if (this.issues.length === 0) {
      recommendations.push('✅ 未发现明显的模拟数据或硬编码问题');
    }

    return recommendations;
  }

  private getTypeDescription(type: ValidationIssue['type']): string {
    switch (type) {
      case 'mock_data': return '模拟数据';
      case 'hardcoded': return '硬编码数据';
      case 'test_data': return '测试数据';
      case 'suspicious': return '可疑数据';
      default: return '未知类型';
    }
  }

  private getTotalFilesScanned(): number {
    // 简化实现，返回唯一文件数
    const uniqueFiles = new Set(this.issues.map(issue => issue.file));
    return uniqueFiles.size;
  }

  generateReport(report: ValidationReport): void {
    console.log('\n📊 真实数据验证报告');
    console.log('='.repeat(60));
    console.log(`⏰ 验证时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}`);
    console.log(`📁 扫描文件: ${report.totalFiles} 个`);
    console.log(`🔍 发现问题: ${report.issuesFound} 个`);
    console.log('');

    // 系统检查结果
    console.log('🔧 系统检查结果:');
    console.log(`   磁盘数据真实性: ${report.systemCheck.diskDataReal ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   网络数据真实性: ${report.systemCheck.networkDataReal ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   API健康检查: ${report.systemCheck.apiHealthWorking ? '✅ 工作正常' : '❌ 未工作'}`);
    console.log('');

    // 按严重程度分组显示问题
    const groupedIssues = this.groupIssuesBySeverity(report.issues);
    
    Object.entries(groupedIssues).forEach(([severity, issues]) => {
      if (issues.length > 0) {
        const icon = this.getSeverityIcon(severity as ValidationIssue['severity']);
        console.log(`${icon} ${severity.toUpperCase()} (${issues.length} 个问题):`);
        
        issues.slice(0, 10).forEach(issue => { // 只显示前10个
          console.log(`   📄 ${issue.file}:${issue.line} - ${issue.description}`);
        });
        
        if (issues.length > 10) {
          console.log(`   ... 还有 ${issues.length - 10} 个类似问题`);
        }
        console.log('');
      }
    });

    // 建议
    console.log('💡 修复建议:');
    report.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });
    console.log('');

    // 保存报告
    const reportFile = `logs/real-data-validation-${new Date().toISOString().split('T')[0]}.json`;
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    console.log(`📄 详细报告已保存: ${reportFile}`);
  }

  private groupIssuesBySeverity(issues: ValidationIssue[]): Record<string, ValidationIssue[]> {
    return issues.reduce((groups, issue) => {
      if (!groups[issue.severity]) {
        groups[issue.severity] = [];
      }
      groups[issue.severity].push(issue);
      return groups;
    }, {} as Record<string, ValidationIssue[]>);
  }

  private getSeverityIcon(severity: ValidationIssue['severity']): string {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '🔶';
      case 'low': return '💡';
      default: return '❓';
    }
  }
}

// 主函数
async function main() {
  const validator = new RealDataValidator();
  
  try {
    const report = await validator.validateProject();
    validator.generateReport(report);
    
    // 根据问题严重程度设置退出码
    const criticalIssues = report.issues.filter(issue => issue.severity === 'critical');
    if (criticalIssues.length > 0) {
      process.exit(1); // 有严重问题时退出码为1
    }
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { RealDataValidator };
