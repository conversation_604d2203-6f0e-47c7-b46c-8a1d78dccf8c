# 🎵 UNM API Server

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Express.js](https://img.shields.io/badge/Express.js-4.18+-lightgrey.svg)](https://expressjs.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于 [@unblockneteasemusic/server](https://github.com/UnblockNeteaseMusic/server) 的现代化音乐API后端服务，提供音乐搜索、播放链接获取、专辑图片和歌词等功能。

## ✨ 特性

- 🎯 **多音源支持** - 支持网易云、QQ音乐、酷我、咪咕等多个音源
- 🚀 **现代化架构** - TypeScript + Express.js + 分层架构设计
- 🔒 **安全防护** - CORS、频率限制、安全头部、输入验证
- ⚡ **高性能缓存** - 多层缓存机制，支持内存和Redis缓存
- 📊 **监控告警** - 健康检查、指标监控、日志记录
- 🐳 **容器化部署** - Docker支持，一键部署
- 🔄 **智能降级** - 多音源自动降级，提高可用性
- 📚 **完整文档** - 详细的API文档和部署指南

## 🚀 快速开始

### 环境要求

- Node.js 18.0.0 或更高版本
- npm 或 yarn 包管理器
- Redis (可选，用于分布式缓存)

### 安装

```bash
# 克隆项目
git clone https://github.com/your-username/unm-api-server.git
cd unm-api-server

# 安装依赖
npm install

# 复制环境配置文件
cp .env.template .env

# 启动开发服务器
npm run dev
```

### Docker 部署

```bash
# 构建镜像
docker build -t unm-api-server .

# 运行容器
docker run -p 3000:3000 unm-api-server

# 或使用 docker-compose
docker-compose up -d
```

## 📖 API 使用示例

### 搜索音乐

```bash
# 传统API格式
curl "http://localhost:3000/api.php?types=search&source=netease&name=周杰伦&count=10"

# RESTful API格式
curl "http://localhost:3000/api/v1/search?keyword=周杰伦&source=netease&limit=10"
```

### 获取音乐播放链接

```bash
# 传统API格式
curl "http://localhost:3000/api.php?types=url&source=netease&id=418602084&br=320"

# RESTful API格式
curl "http://localhost:3000/api/v1/songs/418602084/url?source=netease&quality=320"
```

### 获取专辑图片

```bash
# 传统API格式
curl "http://localhost:3000/api.php?types=pic&source=netease&id=109951163076136830&size=500"

# RESTful API格式
curl "http://localhost:3000/api/v1/songs/418602084/picture?size=500"
```

### 获取歌词

```bash
# 传统API格式
curl "http://localhost:3000/api.php?types=lyric&source=netease&id=418602084"

# RESTful API格式
curl "http://localhost:3000/api/v1/songs/418602084/lyrics"
```

## 🏗️ 项目架构

```mermaid
graph TB
    A[Client Request] --> B[Express Server]
    B --> C[Security Middleware]
    C --> D[Rate Limiting]
    D --> E[Request Validation]
    E --> F[Route Handler]
    F --> G[Service Layer]
    G --> H[Cache Service]
    G --> I[Music Service]
    G --> J[HTTP Service]
    I --> K[UNM Core]
    J --> L[External APIs]
    H --> M[Memory Cache]
    H --> N[Redis Cache]
```

## 📁 项目结构

```
src/
├── app.ts                    # Express应用主文件
├── server.ts                 # 服务器启动文件
├── config/                   # 配置文件
│   ├── environment.ts        # 环境配置
│   └── sources.ts           # 音源配置
├── routes/                   # 路由层
│   ├── music.ts             # 音乐API路由
│   ├── health.ts            # 健康检查路由
│   └── info.ts              # 系统信息路由
├── services/                 # 服务层
│   ├── musicService.ts      # 音乐服务
│   ├── cacheService.ts      # 缓存服务
│   ├── httpService.ts       # HTTP客户端服务
│   └── unmService.ts        # UNM核心服务
├── middleware/               # 中间件
│   ├── cors.ts              # CORS中间件
│   ├── rateLimit.ts         # 频率限制中间件
│   └── errorHandler.ts      # 错误处理中间件
├── utils/                    # 工具函数
│   ├── logger.ts            # 日志工具
│   ├── response.ts          # 响应格式化
│   └── validation.ts        # 参数验证
└── types/                    # 类型定义
    ├── api.ts               # API类型
    ├── music.ts             # 音乐类型
    └── common.ts            # 通用类型
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `NODE_ENV` | 运行环境 | `development` | 否 |
| `PORT` | 服务端口 | `3000` | 否 |
| `CORS_ORIGIN` | CORS允许的源 | `*` | 否 |
| `CACHE_TTL` | 缓存过期时间(秒) | `300` | 否 |
| `RATE_LIMIT_MAX` | 频率限制最大请求数 | `100` | 否 |
| `DEFAULT_SOURCE` | 默认音源 | `netease` | 否 |
| `LOG_LEVEL` | 日志级别 | `info` | 否 |

### 支持的音源

- `netease` - 网易云音乐
- `qq` - QQ音乐
- `kuwo` - 酷我音乐
- `kugou` - 酷狗音乐
- `xiami` - 虾米音乐
- `baidu` - 百度音乐
- `migu` - 咪咕音乐
- `joox` - JOOX音乐
- `youtube` - YouTube Music
- `bilibili` - 哔哩哔哩
- `pyncmd` - Python网易云音乐

## 📊 监控和健康检查

### 健康检查端点

```bash
# 基础健康检查
curl http://localhost:3000/health

# 详细系统信息
curl http://localhost:3000/info

# 缓存状态
curl http://localhost:3000/cache/stats

# 性能指标
curl http://localhost:3000/metrics
```

### 日志查看

```bash
# 查看实时日志
npm run logs

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/combined.log
```

## 🔒 安全特性

- **CORS保护** - 配置跨域访问策略
- **频率限制** - 防止API滥用
- **安全头部** - Helmet中间件提供安全头部
- **输入验证** - 严格的参数验证和清理
- **错误处理** - 统一的错误处理，避免信息泄露
- **日志记录** - 详细的访问和错误日志

## 📚 完整文档

### 📖 核心文档
- [📖 API接口文档](docs/API.md) - 详细的API使用说明和示例
- [🚀 部署指南](docs/DEPLOYMENT.md) - Docker、传统部署、反向代理配置
- [💻 开发指南](docs/DEVELOPMENT.md) - 开发环境搭建和贡献指南
- [🏗️ 架构文档](docs/ARCHITECTURE.md) - 技术架构和设计原理
- [🔒 安全指南](docs/SECURITY.md) - 安全配置和最佳实践
- [🔧 故障排除](docs/TROUBLESHOOTING.md) - 常见问题和解决方案

### 📋 项目文档
- [📝 更新日志](CHANGELOG.md) - 版本更新记录
- [📋 需求文档](requirements.md) - 项目需求和验收标准
- [🎯 任务文档](tasks.md) - 开发任务分解和进度
- [🏗️ 设计文档](design.md) - 详细设计说明

### 🔧 配置文档
- [⚙️ 环境配置](.env.template) - 环境变量配置模板
- [🐳 Docker配置](Dockerfile) - 容器化部署配置
- [📦 包配置](package.json) - 项目依赖和脚本

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [开发指南](docs/DEVELOPMENT.md) 了解如何参与项目开发。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [UnblockNeteaseMusic](https://github.com/UnblockNeteaseMusic) - 核心音乐解锁功能
- [GD Studio](https://muisc-api.gdstudio.xyz/) - 外部音乐API服务支持
- 所有贡献者和用户的支持

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [故障排除文档](docs/TROUBLESHOOTING.md)
2. 搜索 [已有Issues](https://github.com/your-username/unm-api-server/issues)
3. 创建新的 [Issue](https://github.com/your-username/unm-api-server/issues/new)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
