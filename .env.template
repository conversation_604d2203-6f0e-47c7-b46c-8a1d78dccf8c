# UNM API 优化配置
# 复制此文件为 .env 并根据实际情况修改配置

# 基础配置
NODE_ENV=development
PORT=5678
SERVER_HOST=localhost

# 安全配置
ENABLE_HTTPS=false
HTTPS_PORT=443
SSL_KEY_PATH=./ssl/private-key.pem
SSL_CERT_PATH=./ssl/certificate.pem
MAX_HEADER_SIZE=8192
ENABLE_TRACE_BLOCK=true

# 缓存配置
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_L1_TTL=300
CACHE_L2_TTL=1800

# 监控配置
METRICS_ENABLED=true
ALERT_ERROR_RATE_THRESHOLD=0.05
ALERT_RESPONSE_TIME_THRESHOLD=3000
ALERT_WEBHOOK_URL=

# 重试配置
RETRY_MAX_ATTEMPTS=3
RETRY_BASE_DELAY=1000
RETRY_MAX_DELAY=10000
RETRY_BACKOFF_FACTOR=2

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 音乐API配置
MUSIC_API_URL=https://music-api.gdstudio.xyz/api.php
HTTP_TIMEOUT=10000

# CORS配置
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# 频率限制配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MESSAGE=Too many requests, please try again later

# 缓存配置
CACHE_TTL=300
ENABLE_CACHE=true
CACHE_MAX_SIZE=1000
CACHE_CLEANUP_INTERVAL=300000

# 音乐服务配置
DEFAULT_SOURCE=netease
SUPPORTED_SOURCES=netease,qq,xiami,baidu,kugou,kuwo,migu,joox,youtube,bilibili,pyncmd
STABLE_SOURCES=netease,kuwo,pyncmd

# API配置
API_VERSION=v1
API_BASE_PATH=/api
MAX_SEARCH_LIMIT=100
MAX_BATCH_SIZE=50
DEFAULT_SEARCH_LIMIT=20
DEFAULT_SEARCH_OFFSET=0

# UNM配置
UNM_DEFAULT_SOURCES=pyncmd,netease,kuwo
UNM_SUPPORTED_QUALITIES=128,192,320,740,999
UNM_DEFAULT_QUALITY=999
UNM_SUPPORTED_SIZES=300,500
UNM_DEFAULT_SIZE=300

# 安全配置
HELMET_ENABLED=true
REQUEST_SIZE_LIMIT=10mb

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# 静态文件配置
STATIC_PATH=/
STATIC_MAX_AGE=86400000

# 健康检查配置
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# 预热配置
WARMUP_QUERIES=
WARMUP_ENABLED=false
