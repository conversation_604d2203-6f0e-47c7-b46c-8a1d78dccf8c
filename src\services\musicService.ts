/**
 *  特别感谢 GD Studio 提供的音乐API服务！
 *  项目依赖于外部音乐API: https://muisc-api.gdstudio.xyz/api.php 
 */
import { SearchParams, UrlParams, PictureParams, LyricParams } from './../types/music';
import { SearchResult, MusicUrl, AlbumPicture, Lyrics } from './../types/api';
import { logger } from './../utils/logger';
import { APIError } from './../types/common';
import { isValidSource, getDefaultSources } from './../config/sources';
import { httpService } from './httpService';
import { unmService } from './unmService';
import { metricsCollector } from '../utils/metricsCollector';

/**
 * 音乐服务类
 */
export class MusicService {
  /**
   * 搜索音乐
   */
  async search(params: SearchParams): Promise<SearchResult[]> {
    const startTime = Date.now();
    let success = false;

    try {
      logger.info('Searching music:', params);

      // 验证音源
      if (!isValidSource(params.source)) {
        throw new APIError(400, `Invalid source: ${params.source}`);
      }

      // 调用外部音乐API进行搜索
      const response = await httpService.search({
        source: params.source,
        name: params.keyword,
        count: params.limit,
        pages: Math.floor(params.offset / params.limit) + 1,
      });

      // 处理API响应 - 外部API直接返回数组格式
      let results: SearchResult[] = [];

      if (Array.isArray(response)) {
        // 直接返回数组格式
        results = response;
      } else if (response && response.code === 200 && response.data) {
        // 包装格式
        results = response.data;
      } else if (response && Array.isArray(response.data)) {
        // 其他包装格式
        results = response.data;
      } else {
        throw new APIError(500, 'Invalid search response format');
      }

      success = true;
      logger.info(`Search completed, found ${results.length} results`);
      return results;
    } catch (error) {
      logger.error('Search failed:', error);
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError(500, 'Search service error');
    } finally {
      // 记录指标
      const duration = Date.now() - startTime;
      metricsCollector.recordApiCall({
        source: params.source,
        endpoint: 'search',
        method: 'GET',
        statusCode: success ? 200 : 500,
        duration,
        success
      });
    }
  }

  /**
   * 获取音乐链接
   */
  async getMusicUrl(params: UrlParams): Promise<MusicUrl> {
    try {
      logger.info('Getting music URL:', params);

      // 验证音源
      if (!isValidSource(params.source)) {
        throw new APIError(400, `Invalid source: ${params.source}`);
      }

      // 调用外部音乐API获取音乐链接
      const response = await httpService.getMusicUrl({
        source: params.source,
        id: params.id,
        br: params.quality,
      });

      // 处理API响应 - 外部API可能直接返回对象或包装格式
      let musicData: any = null;

      if (response && response.url) {
        // 直接返回对象格式
        musicData = response;
      } else if (response && response.code === 200 && response.data) {
        // 包装格式
        musicData = response.data;
      } else {
        // 尝试使用默认音源
        const defaultSources = getDefaultSources();
        for (const source of defaultSources) {
          try {
            const fallbackResponse = await httpService.getMusicUrl({
              source: source,
              id: params.id,
              br: params.quality,
            });

            if (fallbackResponse && (fallbackResponse.url || (fallbackResponse.data && fallbackResponse.data.url))) {
              logger.info(`Used fallback source: ${source}`);
              musicData = fallbackResponse.url ? fallbackResponse : fallbackResponse.data;
              break;
            }
          } catch (fallbackError) {
            logger.debug(`Fallback source ${source} failed:`, fallbackError);
          }
        }

        // 如果外部API都失败了，尝试使用UNM服务
        if (!musicData) {
          try {
            logger.info('Trying UNM service as final fallback');
            const unmResult = await unmService.getMusicUrl(params.id, getDefaultSources(), params.quality);
            if (unmResult && unmResult.url) {
              logger.info(`UNM service succeeded with source: ${unmResult.source}`);
              musicData = unmResult;
            }
          } catch (unmError) {
            logger.debug('UNM service also failed:', unmError);
          }
        }
      }

      if (!musicData || !musicData.url) {
        throw new APIError(404, 'No music URL found');
      }

      logger.info('Music URL retrieved successfully');
      return {
        url: musicData.url,
        br: musicData.br || params.quality,
        size: musicData.size || 0,
        type: musicData.type || 'mp3',
      };
    } catch (error) {
      logger.error('Get music URL failed:', error);
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError(500, 'Music URL service error');
    }
  }

  /**
   * 获取专辑图
   */
  async getPicture(params: PictureParams): Promise<AlbumPicture> {
    try {
      logger.info('Getting album picture:', params);

      // 验证音源
      if (!isValidSource(params.source)) {
        throw new APIError(400, `Invalid source: ${params.source}`);
      }

      // 调用外部音乐API获取专辑图
      const response = await httpService.getPicture({
        source: params.source,
        id: params.id,
        size: params.size,
      });

      // 处理API响应 - 外部API可能直接返回对象或包装格式
      let pictureData: any = null;

      if (response && response.url) {
        // 直接返回对象格式
        pictureData = response;
      } else if (response && response.code === 200 && response.data) {
        // 包装格式
        pictureData = response.data;
      } else {
        throw new APIError(500, 'Picture API returned error');
      }

      if (!pictureData || !pictureData.url) {
        throw new APIError(404, 'No picture URL found');
      }

      logger.info('Album picture retrieved successfully');
      return {
        url: pictureData.url,
      };
    } catch (error) {
      logger.error('Get picture failed:', error);
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError(500, 'Picture service error');
    }
  }

  /**
   * 获取歌词
   */
  async getLyric(params: LyricParams): Promise<Lyrics> {
    try {
      logger.info('Getting lyrics:', params);

      // 验证音源
      if (!isValidSource(params.source)) {
        throw new APIError(400, `Invalid source: ${params.source}`);
      }

      // 调用外部音乐API获取歌词
      const response = await httpService.getLyric({
        source: params.source,
        id: params.id,
      });

      // 处理API响应 - 外部API可能直接返回对象或包装格式
      let lyricData: any = null;

      if (response && (response.lyric !== undefined || response.tlyric !== undefined)) {
        // 直接返回对象格式
        lyricData = response;
      } else if (response && response.code === 200 && response.data) {
        // 包装格式
        lyricData = response.data;
      } else {
        // 如果没有歌词数据，返回默认歌词
        lyricData = { lyric: '[00:00.000] 暂无歌词\n', tlyric: '' };
      }

      const lyrics: Lyrics = {
        lyric: lyricData?.lyric || '[00:00.000] 暂无歌词\n',
        tlyric: lyricData?.tlyric || '',
      };

      logger.info('Lyrics retrieved successfully');
      return lyrics;
    } catch (error) {
      logger.error('Get lyrics failed:', error);
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError(500, 'Lyrics service error');
    }
  }
}

// 导出单例实例
export const musicService = new MusicService();
