import { Router, Request, Response } from 'express';
import { createSuccessResponse } from './../utils/response';
import { logger } from './../utils/logger';
import { HealthStatus } from './../types/api';

const router = Router();

/**
 * 健康检查端点
 * GET /health
 */
router.get('/health', (_req: Request, res: Response) => {
  try {
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();
    
    const healthStatus: HealthStatus = {
      status: 'healthy',
      uptime: Math.floor(uptime),
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      memory_usage: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    };

    logger.debug('Health check requested', healthStatus);
    
    const response = createSuccessResponse(healthStatus);
    res.json(response);
  } catch (error) {
    logger.error('Health check failed:', error);
    
    const errorStatus: HealthStatus = {
      status: 'unhealthy',
      uptime: Math.floor(process.uptime()),
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      memory_usage: 0,
    };
    
    const response = createSuccessResponse(errorStatus);
    res.status(503).json(response);
  }
});

export default router;
