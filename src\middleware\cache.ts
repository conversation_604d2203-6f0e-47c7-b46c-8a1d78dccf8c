import { Request, Response, NextFunction } from 'express';
import { cacheService } from '../services/cacheService';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

/**
 * 缓存中间件选项
 */
interface CacheOptions {
  ttl?: number;
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request) => boolean;
  skipCache?: boolean;
}

/**
 * 生成缓存键
 */
function generateCacheKey(req: Request): string {
  const method = req.method;
  const path = req.path;
  const query = JSON.stringify(req.query);
  return `${method}:${path}:${query}`;
}

/**
 * 缓存中间件工厂函数
 */
export function cacheMiddleware(options: CacheOptions = {}) {
  return (req: Request, res: Response, next: NextFunction) => {
    // 检查是否启用缓存
    if (!config.enableCache || options.skipCache) {
      return next();
    }

    // 检查缓存条件
    if (options.condition && !options.condition(req)) {
      return next();
    }

    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next();
    }

    try {
      // 生成缓存键
      const cacheKey = options.keyGenerator ? options.keyGenerator(req) : generateCacheKey(req);
      
      // 尝试从缓存获取数据
      const cachedData = cacheService.get(cacheKey);
      
      if (cachedData) {
        logger.debug('Cache hit for request', { 
          path: req.path, 
          cacheKey,
          userAgent: req.get('User-Agent')?.substring(0, 50),
        });
        
        // 设置缓存头
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey,
        });
        
        return res.json(cachedData);
      }

      // 缓存未命中，继续处理请求
      logger.debug('Cache miss for request', { 
        path: req.path, 
        cacheKey, 
      });

      // 拦截响应
      const originalJson = res.json;
      res.json = function(data: any) {
        try {
          // 只缓存成功的响应
          if (res.statusCode === 200 && data && data.code === 200) {
            const ttl = options.ttl || config.cacheTTL * 1000;
            cacheService.set(cacheKey, data, ttl);
            
            logger.debug('Response cached', { 
              path: req.path, 
              cacheKey, 
              ttl,
              dataSize: JSON.stringify(data).length,
            });
          }
          
          // 设置缓存头
          res.set({
            'X-Cache': 'MISS',
            'X-Cache-Key': cacheKey,
          });
          
        } catch (error) {
          logger.error('Cache storage failed', { 
            path: req.path, 
            cacheKey, 
            error, 
          });
        }
        
        // 调用原始的json方法
        return originalJson.call(this, data);
      };

      next();
      
    } catch (error) {
      logger.error('Cache middleware error', { 
        path: req.path, 
        error, 
      });
      next();
    }
  };
}

/**
 * 音乐搜索缓存中间件
 */
export const searchCacheMiddleware = cacheMiddleware({
  ttl: config.cacheSearchTTL * 1000,
  keyGenerator: (req) => {
    const { source, q, limit, offset } = req.query;
    return `search:${source}:${q}:${limit}:${offset}`;
  },
  condition: (req) => {
    // 只有当查询参数存在时才缓存
    return !!(req.query.q && req.query.source);
  },
});

/**
 * 音乐URL缓存中间件
 */
export const musicUrlCacheMiddleware = cacheMiddleware({
  ttl: config.cacheMusicUrlTTL * 1000,
  keyGenerator: (req) => {
    const { id } = req.params;
    const { source, quality } = req.query;
    return `music_url:${source}:${id}:${quality}`;
  },
});

/**
 * 歌词缓存中间件
 */
export const lyricsCacheMiddleware = cacheMiddleware({
  ttl: config.cacheLyricsTTL * 1000,
  keyGenerator: (req) => {
    const { id } = req.params;
    const { source } = req.query;
    return `lyrics:${source}:${id}`;
  },
});

/**
 * 专辑图缓存中间件
 */
export const pictureCacheMiddleware = cacheMiddleware({
  ttl: config.cachePictureTTL * 1000,
  keyGenerator: (req) => {
    const { id } = req.params;
    const { source, size } = req.query;
    return `picture:${source}:${id}:${size}`;
  },
});

/**
 * UNM缓存中间件
 */
export const unmCacheMiddleware = cacheMiddleware({
  ttl: config.cacheUnmTTL * 1000,
  keyGenerator: (req) => {
    const { id } = req.params;
    const { sources, quality } = req.query;
    return `unm:${sources}:${id}:${quality}`;
  },
});

/**
 * 清除特定模式的缓存
 */
export function clearCacheByPattern(pattern: string): number {
  try {
    const keys = cacheService.getKeys();
    let clearedCount = 0;
    
    for (const key of keys) {
      if (key.includes(pattern)) {
        cacheService.delete(key);
        clearedCount++;
      }
    }
    
    logger.info('Cache cleared by pattern', { pattern, clearedCount });
    return clearedCount;
  } catch (error) {
    logger.error('Clear cache by pattern failed', { pattern, error });
    return 0;
  }
}

/**
 * 预热缓存 - 为常用查询预先加载数据
 */
export async function warmupCache(): Promise<void> {
  try {
    if (!config.warmupEnabled) {
      logger.debug('Cache warmup is disabled');
      return;
    }

    logger.info('Starting cache warmup...');

    // 使用配置的预热查询列表
    for (const query of config.warmupQueries) {
      const cacheKey = `search:${config.defaultSource}:${query}:${config.defaultSearchLimit}:${config.defaultSearchOffset}`;
      // 这里可以调用实际的搜索API来预热缓存
      logger.debug('Warmup cache for query', { query, cacheKey });
    }

    logger.info('Cache warmup completed');
  } catch (error) {
    logger.error('Cache warmup failed', { error });
  }
}
