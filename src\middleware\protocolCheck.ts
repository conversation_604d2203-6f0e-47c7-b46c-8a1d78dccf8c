import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

/**
 * HTTP协议版本检查中间件
 * 禁用不安全的HTTP/1.0协议，防止协议降级攻击
 */
export const protocolCheck = (req: Request, res: Response, next: NextFunction): void | Response => {
  // 检查HTTP版本
  if (req.httpVersion === '1.0') {
    logger.warn('HTTP/1.0 protocol attempted', {
      httpVersion: req.httpVersion,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date().toISOString()
    });
    
    return res.status(426).json({
      code: 426,
      message: 'Upgrade Required - HTTP/1.1 or higher required for security reasons',
      data: {
        current_version: req.httpVersion,
        required_version: '1.1+',
        upgrade_header: 'HTTP/1.1'
      },
      timestamp: new Date().toISOString()
    });
  }
  
  // 记录HTTP版本信息（仅调试模式）
  if (config.logLevel === 'debug') {
    logger.debug('HTTP protocol version', {
      httpVersion: req.httpVersion,
      method: req.method,
      url: req.url
    });
  }
  
  next();
};
