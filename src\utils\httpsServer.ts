import https from 'https';
import fs from 'fs';
import path from 'path';
import { logger } from './logger';
import { config } from '../config/environment';
import { Application } from 'express';

/**
 * HTTPS服务器配置和启动
 */

export interface HTTPSConfig {
  enabled: boolean;
  port: number;
  keyPath: string;
  certPath: string;
  redirectHttp: boolean;
}

/**
 * 生成自签名证书（仅用于开发环境）
 */
export async function generateSelfSignedCert(): Promise<{ keyPath: string; certPath: string }> {
  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);
  
  const certDir = path.join(process.cwd(), 'certs');
  const keyPath = path.join(certDir, 'key.pem');
  const certPath = path.join(certDir, 'cert.pem');
  
  // 创建证书目录
  if (!fs.existsSync(certDir)) {
    fs.mkdirSync(certDir, { recursive: true });
  }
  
  // 检查证书是否已存在
  if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
    logger.info('Self-signed certificate already exists');
    return { keyPath, certPath };
  }
  
  try {
    logger.info('Generating self-signed certificate for development...');
    
    const command = `openssl req -x509 -newkey rsa:4096 -keyout "${keyPath}" -out "${certPath}" -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`;
    
    await execAsync(command);
    
    logger.info('Self-signed certificate generated successfully', {
      keyPath,
      certPath,
    });
    
    return { keyPath, certPath };
  } catch (error) {
    logger.error('Failed to generate self-signed certificate', { error });
    throw new Error('Certificate generation failed');
  }
}

/**
 * 创建HTTPS服务器
 */
export async function createHTTPSServer(app: Application): Promise<https.Server | null> {
  const httpsEnabled = process.env.ENABLE_HTTPS === 'true';
  
  if (!httpsEnabled) {
    logger.info('HTTPS is disabled');
    return null;
  }
  
  const httpsPort = parseInt(process.env.HTTPS_PORT || '443');
  let keyPath = process.env.SSL_KEY_PATH;
  let certPath = process.env.SSL_CERT_PATH;
  
  // 开发环境自动生成自签名证书
  if (config.nodeEnv === 'development' && (!keyPath || !certPath)) {
    try {
      const generated = await generateSelfSignedCert();
      keyPath = generated.keyPath;
      certPath = generated.certPath;
    } catch (error) {
      logger.error('Failed to setup HTTPS for development', { error });
      return null;
    }
  }
  
  // 检查证书文件
  if (!keyPath || !certPath) {
    logger.error('HTTPS enabled but SSL certificate paths not configured');
    return null;
  }
  
  if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
    logger.error('SSL certificate files not found', { keyPath, certPath });
    return null;
  }
  
  try {
    const options: https.ServerOptions = {
      key: fs.readFileSync(keyPath),
      cert: fs.readFileSync(certPath),
      // 增强的安全配置
      secureProtocol: 'TLSv1_2_method',
      // 使用更安全的加密套件
      ciphers: [
        'ECDHE-RSA-AES128-GCM-SHA256',
        'ECDHE-RSA-AES256-GCM-SHA384',
        'ECDHE-RSA-AES128-SHA256',
        'ECDHE-RSA-AES256-SHA384',
        'ECDHE-ECDSA-AES128-GCM-SHA256',
        'ECDHE-ECDSA-AES256-GCM-SHA384',
        '!aNULL', '!eNULL', '!EXPORT', '!DES', '!RC4', '!MD5', '!PSK', '!SRP', '!CAMELLIA',
      ].join(':'),
      honorCipherOrder: true,
      // 禁用不安全的SSL/TLS版本
      secureOptions: require('constants').SSL_OP_NO_SSLv2 |
                     require('constants').SSL_OP_NO_SSLv3 |
                     require('constants').SSL_OP_NO_TLSv1 |
                     require('constants').SSL_OP_NO_TLSv1_1,
      // 启用ECDH
      ecdhCurve: 'auto',
      // 会话配置
      sessionTimeout: 300, // 5分钟
      // 客户端证书验证（可选）
      requestCert: false,
      rejectUnauthorized: false,
    };
    
    const httpsServer = https.createServer(options, app);
    
    httpsServer.listen(httpsPort, () => {
      logger.info(`🔒 HTTPS Server running on port ${httpsPort}`);
      logger.info(`🔗 HTTPS URL: https://localhost:${httpsPort}`);
    });
    
    httpsServer.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`HTTPS port ${httpsPort} is already in use`);
      } else {
        logger.error('HTTPS server error', { error });
      }
    });
    
    return httpsServer;
  } catch (error) {
    logger.error('Failed to create HTTPS server', { error });
    return null;
  }
}

/**
 * HTTP到HTTPS重定向中间件
 */
export const httpsRedirect = (req: any, res: any, next: any) => {
  const httpsEnabled = process.env.ENABLE_HTTPS === 'true';
  const redirectEnabled = process.env.HTTPS_REDIRECT === 'true';
  
  if (httpsEnabled && redirectEnabled && config.nodeEnv === 'production') {
    if (!req.secure && req.get('x-forwarded-proto') !== 'https') {
      const httpsPort = process.env.HTTPS_PORT || '443';
      const redirectUrl = httpsPort === '443' 
        ? `https://${req.get('host')}${req.url}`
        : `https://${req.get('host')}:${httpsPort}${req.url}`;
      
      logger.info('Redirecting HTTP to HTTPS', {
        originalUrl: req.url,
        redirectUrl,
        ip: req.ip,
      });
      
      return res.redirect(301, redirectUrl);
    }
  }
  
  next();
};
