# UNM API Server Environment Configuration Example
# Copy this file to .env and modify values as needed

# Server Configuration
NODE_ENV=development
PORT=5678
SERVER_NAME="UNM API Server"
SERVER_VERSION="1.0.0"
SERVER_AUTHOR="UNM API Server Contributors"
SERVER_DESCRIPTION="UnblockNeteaseMusic API Server - Modern RESTful music API service"

# External Music API Configuration
MUSIC_API_URL=https://music-api.gdstudio.xyz/api.php
HTTP_TIMEOUT=10000

# User Agent Configuration (Random selection for different platforms)
# Multiple User-Agents separated by | character
USER_AGENTS="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36|Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36|Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36|Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36|Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1"

# CORS Configuration
CORS_ORIGIN=*
CORS_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_HEADERS="Content-Type,Authorization,X-Requested-With"

# Cache Configuration
CACHE_TTL=300
ENABLE_CACHE=true
CACHE_MAX_SIZE=1000
CACHE_CLEANUP_INTERVAL=300000
CACHE_SEARCH_TTL=600
CACHE_MUSIC_URL_TTL=1800
CACHE_LYRICS_TTL=3600
CACHE_PICTURE_TTL=86400
CACHE_UNM_TTL=1200

# Rate Limiting Configuration
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MESSAGE="Too many requests, please try again later"

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=

# Music Service Configuration
DEFAULT_SOURCE=netease
SUPPORTED_SOURCES="netease,kuwo,xiami,baidu,kugou,migu,joox,youtube,bilibili,pyncmd"
STABLE_SOURCES="netease,kuwo,pyncmd"

# API Configuration
API_VERSION=v1
API_BASE_PATH=/api
MAX_SEARCH_LIMIT=100
MAX_BATCH_SIZE=50
DEFAULT_SEARCH_LIMIT=20
DEFAULT_SEARCH_OFFSET=0

# UNM Configuration
UNM_DEFAULT_SOURCES="pyncmd,netease,kuwo"
UNM_SUPPORTED_QUALITIES="128,192,320,740,999"
UNM_DEFAULT_QUALITY=999
UNM_SUPPORTED_SIZES="300,500"
UNM_DEFAULT_SIZE=300

# Security Configuration
HELMET_ENABLED=true
REQUEST_SIZE_LIMIT=10mb
MAX_HEADER_SIZE=8192
BLOCK_EMPTY_USER_AGENT=false
BLOCK_MALICIOUS_USER_AGENT=true
MAX_USER_AGENT_LENGTH=512
BLOCK_SUSPICIOUS_REQUESTS=false
REQUEST_TIMEOUT=30000

# Slow Down Protection
SLOW_DOWN_WINDOW=900000
SLOW_DOWN_DELAY_AFTER=100
SLOW_DOWN_DELAY_MS=500
SLOW_DOWN_MAX_DELAY=20000

# HTTPS Configuration
ENABLE_HTTPS=false
HTTPS_PORT=443
HTTPS_REDIRECT=true
SSL_KEY_PATH=
SSL_CERT_PATH=

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES="image/jpeg,image/png,image/gif"

# Static Files Configuration
STATIC_PATH=/public
STATIC_MAX_AGE=86400000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Warmup Configuration
WARMUP_QUERIES="周杰伦,邓紫棋,林俊杰,薛之谦,毛不易"
WARMUP_ENABLED=false

# Error Messages Configuration
ERROR_INVALID_PARAMS="Invalid parameters"
ERROR_MISSING_PARAM="Required parameter is missing"
ERROR_INVALID_SOURCE="Invalid source parameter"
ERROR_INVALID_QUALITY="Invalid quality parameter"
ERROR_INVALID_SIZE="Invalid size parameter"
ERROR_SONG_NOT_FOUND="No matching song found"
ERROR_URL_NOT_FOUND="No music URL found"
ERROR_PICTURE_NOT_FOUND="No picture URL found"
ERROR_LYRICS_NOT_FOUND="No lyrics found"
ERROR_SERVICE_UNAVAILABLE="Service temporarily unavailable"
ERROR_TIMEOUT="Request timeout"
ERROR_NETWORK_ERROR="Network error"
ERROR_UNKNOWN="Unknown error occurred"

# Success Messages Configuration
SUCCESS_DEFAULT="Success"
SUCCESS_SEARCH_COMPLETED="Search completed successfully"
SUCCESS_URL_RETRIEVED="Music URL retrieved successfully"
SUCCESS_PICTURE_RETRIEVED="Picture retrieved successfully"
SUCCESS_LYRICS_RETRIEVED="Lyrics retrieved successfully"
SUCCESS_CACHE_CLEARED="Cache cleared successfully"
SUCCESS_WARMUP_COMPLETED="Cache warmup completed"

# Validation Configuration
MIN_SEARCH_LENGTH=1
MAX_SEARCH_LENGTH=100
MIN_ID_LENGTH=1
MAX_ID_LENGTH=50
ID_PATTERN=^[a-zA-Z0-9_-]+$
NUMERIC_ID_PATTERN=^[0-9]+$

# UnblockNeteaseMusic Configuration
ENABLE_FLAC=true
ENABLE_LOCAL_VIP=true
SELECT_MAX_BR=true
BLOCK_ADS=true
DISABLE_UPGRADE_CHECK=true
FOLLOW_SOURCE_ORDER=true
JSON_LOG=true
NO_CACHE=false
MIN_BR=128000

# Optional: Music Source Cookies
JOOX_COOKIE=
MIGU_COOKIE=
QQ_COOKIE=
YOUTUBE_KEY=
NETEASE_COOKIE=

# Optional: Proxy Configuration
PROXY_URL=
FORCE_HOST=
CNRELAY=

# Optional: Authentication
TOKEN=
