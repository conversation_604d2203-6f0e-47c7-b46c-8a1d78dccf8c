import cors from 'cors';
import { config } from './../config/environment';

// CORS配置选项
const corsOptions: cors.CorsOptions = {
  origin: (origin, callback) => {
    // 允许所有来源或指定来源
    if (config.corsOrigin === '*') {
      callback(null, true);
    } else {
      const allowedOrigins = config.corsOrigin.split(',').map(o => o.trim());
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    }
  },
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
  ],
  credentials: false,
  maxAge: 86400, // 24小时
  optionsSuccessStatus: 200,
};

// 导出CORS中间件
export const corsMiddleware = cors(corsOptions);
