import axios from 'axios';
import { logger } from './../utils/logger';
import { APIError } from './../types/common';
import { getRandomUserAgent } from './../utils/userAgent';
import { apiHealthService } from './apiHealthService';
import { dataValidator } from '../utils/dataValidator';
import { withHttpRetry } from '../utils/enhancedRetryHandler';

/**
 * HTTP客户端服务类
 * 用于调用外部音乐API
 */
export class HttpService {
  private client: any;

  constructor() {
    // 使用推荐的API URL而不是固定的配置
    const recommendedApiUrl = apiHealthService.getRecommendedApi();

    this.client = axios.create({
      baseURL: recommendedApiUrl,
      timeout: 10000, // 10秒超时
      headers: {
        'User-Agent': getRandomUserAgent(),
        'Accept': 'application/json',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config: any) => {
        // 每次请求使用随机User-Agent
        config.headers['User-Agent'] = getRandomUserAgent();

        logger.debug('HTTP Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
          userAgent: config.headers['User-Agent'],
        });
        return config;
      },
      (error: any) => {
        logger.error('HTTP Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: any) => {
        logger.debug('HTTP Response:', {
          status: response.status,
          url: response.config.url,
          dataSize: JSON.stringify(response.data).length,
        });
        return response;
      },
      (error: any) => {
        logger.error('HTTP Response Error:', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * 发送GET请求到外部音乐API（带增强重试机制）
   */
  async get<T = any>(params: Record<string, any>): Promise<T> {
    // 生成操作ID用于统计
    const operationId = `http_get_${params.types || 'unknown'}`;

    try {
      // 使用增强重试机制包装HTTP请求
      const result = await withHttpRetry(async () => {
        const response = await this.client.get('', { params });

        // 检查响应格式
        if (response.data && typeof response.data === 'object') {
          return response.data;
        }

        throw new APIError(500, 'Invalid response format from music API');
      }, operationId);

      return result as T;
    } catch (error: any) {
      if (error.response) {
        // 服务器响应了错误状态码
        throw new APIError(
          error.response.status,
          `Music API error: ${error.response.statusText}`
        );
      } else if (error.request) {
        // 请求发送了但没有收到响应
        throw new APIError(503, 'Music API service unavailable');
      } else if (error.message) {
        // 请求配置错误
        throw new APIError(500, `Request configuration error: ${error.message}`);
      }

      // 其他类型的错误
      throw new APIError(500, 'Unknown error occurred while calling music API');
    }
  }

  /**
   * 搜索音乐
   */
  async search(params: {
    source: string;
    name: string;
    count?: number;
    pages?: number;
  }): Promise<any> {
    const result = await this.get({
      types: 'search',
      source: params.source,
      name: params.name,
      count: params.count || 20,
      pages: params.pages || 1,
    });

    // 验证搜索结果数据
    if (result && Array.isArray(result)) {
      const validation = dataValidator.validateSearchResults(result);
      if (!validation.isValid) {
        logger.warn('Search results validation failed', {
          errors: validation.errors,
          warnings: validation.warnings,
          score: validation.score
        });
      }
    }

    return result;
  }

  /**
   * 获取音乐链接
   */
  async getMusicUrl(params: {
    source: string;
    id: string;
    br?: number;
  }): Promise<any> {
    const result = await this.get({
      types: 'url',
      source: params.source,
      id: params.id,
      br: params.br || 999,
    });

    // 验证音乐URL数据
    if (result && result.url) {
      const validation = dataValidator.validateMusicUrl(result);
      if (!validation.isValid) {
        logger.warn('Music URL validation failed', {
          errors: validation.errors,
          warnings: validation.warnings,
          score: validation.score
        });
      }
    }

    return result;
  }

  /**
   * 获取专辑图
   */
  async getPicture(params: {
    source: string;
    id: string;
    size?: number;
  }): Promise<any> {
    return this.get({
      types: 'pic',
      source: params.source,
      id: params.id,
      size: params.size || 300,
    });
  }

  /**
   * 获取歌词
   */
  async getLyric(params: {
    source: string;
    id: string;
  }): Promise<any> {
    return this.get({
      types: 'lyric',
      source: params.source,
      id: params.id,
    });
  }
}

// 导出单例实例
export const httpService = new HttpService();
