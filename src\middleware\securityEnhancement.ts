import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 禁用不安全的HTTP方法
 * 防止TRACE方法的XST攻击
 */
export const traceMethodBlock = (req: Request, res: Response, next: NextFunction): void | Response => {
  if (req.method === 'TRACE') {
    logger.warn('TRACE method blocked', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date().toISOString(),
    });
    
    return res.status(405).json({
      code: 405,
      message: 'Method Not Allowed',
      data: null,
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

/**
 * HTTP协议版本检查
 * 禁用不安全的HTTP/1.0协议
 */
export const protocolVersionCheck = (req: Request, res: Response, next: NextFunction): void | Response => {
  if (req.httpVersion === '1.0') {
    logger.warn('HTTP/1.0 protocol blocked', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      httpVersion: req.httpVersion,
      timestamp: new Date().toISOString(),
    });
    
    return res.status(426).json({
      code: 426,
      message: 'Upgrade Required - HTTP/1.1 or higher required',
      data: null,
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

/**
 * 限制允许的HTTP方法
 * 只允许GET, POST, PUT, DELETE, OPTIONS方法
 */
export const allowedMethodsOnly = (req: Request, res: Response, next: NextFunction): void | Response => {
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
  
  if (!allowedMethods.includes(req.method)) {
    logger.warn('Disallowed HTTP method', {
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date().toISOString(),
    });
    
    return res.status(405).json({
      code: 405,
      message: `Method ${req.method} Not Allowed`,
      data: {
        allowedMethods,
      },
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

/**
 * 安全响应头设置
 * 添加额外的安全响应头
 */
export const securityHeaders = (_req: Request, res: Response, next: NextFunction): void => {
  // 防止点击劫持
  res.setHeader('X-Frame-Options', 'DENY');
  
  // 防止MIME类型嗅探
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // XSS保护
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // 引用策略
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // 权限策略
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  next();
};
