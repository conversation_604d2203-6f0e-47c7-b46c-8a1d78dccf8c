# 🏗️ UNM API Server 设计文档

## 📋 设计概述

| 属性 | 值 |
|------|-----|
| **项目名称** | UNM API Server |
| **设计版本** | 1.0.0 |
| **设计日期** | 2025-01-23 |
| **设计负责人** | 技术负责人 |
| **技术架构** | 分层架构 + RESTful API |
| **设计原则** | 高内聚、低耦合、可扩展、安全优先 |

## 🎯 设计目标

### 核心设计目标
1. **高性能**: API响应时间 < 3秒，支持1000+并发用户
2. **高可用**: 系统可用性 > 99.5%，支持多音源降级
3. **高安全**: 完整的安全防护体系，通过安全审计
4. **易维护**: 清晰的代码结构，完整的文档和测试
5. **易扩展**: 模块化设计，支持水平和垂直扩展

### 设计原则
- **分层架构**: 清晰的职责分离
- **依赖注入**: 松耦合的组件设计
- **错误优先**: 完善的错误处理机制
- **缓存优先**: 多层缓存提升性能
- **安全优先**: 全面的安全防护措施
- **监控优先**: 完整的监控和日志系统

## 🏛️ 系统架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    UNM API 后端服务器架构                    │
├─────────────────────────────────────────────────────────────┤
│  API层 (Express.js Routes)                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  主API路由  │ │  健康检查   │ │  系统信息   │           │
│  │ (/api.php)  │ │ (/health)   │ │  (/info)    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  中间件层 (Middleware)                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  CORS配置   │ │  频率限制   │ │  错误处理   │           │
│  │             │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  服务层 (Business Logic)                                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  音乐搜索   │ │  资源获取   │ │  缓存管理   │           │
│  │  服务       │ │  服务       │ │  服务       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  集成层 (@unblockneteasemusic/server)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   网易云    │ │    QQ音乐   │ │   酷我音乐  │           │
│  │   音源      │ │    音源     │ │    音源     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈架构

**后端技术栈**
- **运行环境**: Node.js 18+
- **Web框架**: Express.js 4.18+
- **类型系统**: TypeScript 5.0+
- **核心库**: @unblockneteasemusic/server
- **HTTP客户端**: Axios
- **日志**: Winston
- **缓存**: Node-cache

**中间件技术栈**
- **安全**: Helmet, CORS
- **日志**: Morgan
- **频率限制**: Express-rate-limit
- **压缩**: Compression
- **解析**: Body-parser

**部署技术栈**
- **容器化**: Docker + Docker Compose
- **环境管理**: Dotenv + 环境变量
- **进程管理**: PM2 (可选)
- **反向代理**: Nginx (可选)
- **SSL/TLS**: Let's Encrypt (可选)

### 🔄 数据流架构

```mermaid
sequenceDiagram
    participant C as 客户端
    participant N as Nginx
    participant E as Express
    participant M as 中间件
    participant S as 服务层
    participant Cache as 缓存
    participant UNM as UNM核心
    participant API as 外部API

    C->>N: HTTP请求
    N->>E: 转发请求
    E->>M: 安全检查
    M->>M: CORS验证
    M->>M: 频率限制
    M->>M: 参数验证
    M->>S: 调用服务
    S->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>S: 返回缓存数据
    else 缓存未命中
        S->>UNM: 调用UNM核心
        UNM->>API: 调用外部API
        API-->>UNM: 返回数据
        UNM-->>S: 返回处理结果
        S->>Cache: 存储缓存
    end
    S-->>M: 返回结果
    M-->>E: 格式化响应
    E-->>N: HTTP响应
    N-->>C: 返回结果
```

### 🏗️ 分层架构详解

#### 1. 表示层 (Presentation Layer)
**职责**: 处理HTTP请求和响应，参数验证，格式化输出
- **路由层**: 定义API端点和路由规则
- **中间件**: 安全、验证、日志、错误处理
- **响应格式化**: 统一的JSON响应格式

#### 2. 业务逻辑层 (Business Logic Layer)
**职责**: 实现核心业务逻辑，协调各种服务
- **音乐服务**: 搜索、链接获取、图片、歌词
- **缓存服务**: 多层缓存管理
- **HTTP服务**: 外部API调用封装
- **UNM服务**: 核心库封装

#### 3. 数据访问层 (Data Access Layer)
**职责**: 管理外部数据源访问，缓存策略
- **UNM核心库**: @unblockneteasemusic/server
- **外部API**: 各音源的API接口
- **缓存存储**: 内存缓存 + Redis

#### 4. 基础设施层 (Infrastructure Layer)
**职责**: 提供横切关注点，如日志、监控、配置
- **配置管理**: 环境变量和配置文件
- **日志系统**: Winston结构化日志
- **监控系统**: 健康检查和性能指标
- **工具函数**: 通用工具和辅助函数

## 📊 数据模型设计

### API响应数据模型

```typescript
// 标准API响应格式
interface APIResponse<T = any> {
  code: number;        // HTTP状态码 (200=成功)
  message: string;     // 人类可读状态信息
  data: T | null;      // 响应数据或null
  timestamp: string;   // ISO格式时间戳
}

// 搜索结果数据模型
interface SearchResult {
  id: string;          // 曲目ID (track_id)
  name: string;        // 歌曲名称
  artist: string[];    // 歌手列表
  album: string;       // 专辑名称
  pic_id: string;      // 专辑图ID
  url_id: string;      // URL ID (已废弃，保持兼容性)
  lyric_id: string;    // 歌词ID
  source: string;      // 音乐源标识
}

// 音乐链接数据模型
interface MusicUrl {
  url: string;         // 音乐播放链接
  br: number;          // 实际音质 (kbps)
  size: number;        // 文件大小 (KB)
  type?: string;       // 文件格式 (mp3/flac)
}

// 专辑图数据模型
interface AlbumPicture {
  url: string;         // 图片链接
}

// 歌词数据模型
interface Lyrics {
  lyric: string;       // LRC格式原语种歌词
  tlyric?: string;     // LRC格式中文翻译歌词
}

// 服务器信息数据模型
interface ServerInfo {
  name: string;            // 服务名称
  version: string;         // 服务版本
  author: string;          // 作者信息
  description: string;     // 服务描述
  supported_sources: string[]; // 支持的音源列表
  stable_sources: string[];    // 稳定音源列表
}

// 健康检查数据模型
interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  uptime: number;      // 运行时间 (秒)
  timestamp: string;   // 检查时间
  version: string;     // 服务版本
  memory_usage: number; // 内存使用量 (MB)
  cpu_usage?: number;  // CPU使用率 (%)
}
```

### 内部数据模型

```typescript
// API请求参数模型
interface APIParams {
  types: 'search' | 'url' | 'pic' | 'lyric';
  source?: string;     // 音乐源
  name?: string;       // 搜索关键字
  id?: string;         // 资源ID
  count?: number;      // 页面长度
  pages?: number;      // 页码
  br?: number;         // 音质
  size?: number;       // 图片尺寸
}

// 音源配置模型
interface MusicSource {
  id: string;          // 音源标识
  name: string;        // 音源名称
  enabled: boolean;    // 是否启用
  priority: number;    // 优先级 (1-10)
  stable: boolean;     // 是否为稳定音源
}

// 缓存数据模型
interface CacheItem<T> {
  key: string;         // 缓存键
  data: T;            // 缓存数据
  timestamp: number;   // 缓存时间戳
  ttl: number;        // 生存时间 (秒)
}

// 错误响应模型
interface ErrorResponse {
  code: number;        // 错误状态码
  message: string;     // 错误信息
  error?: string;      // 详细错误描述
  timestamp: string;   // 错误时间
}
```

## API接口设计

### 主API端点设计

**端点**: `/api.php`  
**方法**: GET  
**描述**: 兼容api.md规范的统一API接口

```typescript
// 查询参数验证
interface APIQueryParams {
  types: 'search' | 'url' | 'pic' | 'lyric';
  source?: string;     // 默认: netease
  name?: string;       // search类型必需
  id?: string;         // url/pic/lyric类型必需
  count?: string;      // 默认: "20"
  pages?: string;      // 默认: "1"
  br?: string;         // 默认: "999"
  size?: string;       // 默认: "300"
}

// 使用示例 - 以下为API调用格式示例
GET /api.php?types=search&source=netease&name=示例歌手&count=20&pages=1
GET /api.php?types=url&source=netease&id=示例歌曲ID&br=320
GET /api.php?types=pic&source=netease&id=示例图片ID&size=500
GET /api.php?types=lyric&source=netease&id=示例歌曲ID
```

### 系统API端点设计

```typescript
// 健康检查API
GET /health
响应: APIResponse<HealthStatus>

// 服务信息API
GET /info
响应: APIResponse<ServerInfo>

// 支持的音源列表API
GET /sources
响应: APIResponse<{stable: string[], all: string[]}>
```

## 服务层设计

### 音乐服务封装

```typescript
// 音乐服务接口
interface MusicService {
  search(params: SearchParams): Promise<SearchResult[]>;
  getMusicUrl(params: UrlParams): Promise<MusicUrl>;
  getPicture(params: PictureParams): Promise<AlbumPicture>;
  getLyric(params: LyricParams): Promise<Lyrics>;
}

// 搜索参数
interface SearchParams {
  source: string;
  keyword: string;
  limit: number;
  offset: number;
}

// URL获取参数
interface UrlParams {
  source: string;
  id: string;
  quality: number;
}

// 图片获取参数
interface PictureParams {
  source: string;
  id: string;
  size: number;
}

// 歌词获取参数
interface LyricParams {
  source: string;
  id: string;
}
```

### 缓存服务设计

```typescript
// 缓存服务接口
interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  clear(): Promise<void>;
}

// 缓存配置
interface CacheConfig {
  defaultTTL: number;      // 默认TTL (秒)
  maxKeys: number;         // 最大缓存键数量
  checkPeriod: number;     // 清理检查周期 (秒)
}
```

## 中间件设计

### CORS配置

```typescript
// CORS配置
const corsOptions = {
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false,
  maxAge: 86400 // 24小时
};
```

### 频率限制配置

```typescript
// 频率限制配置
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100,                  // 最大请求数
  message: {
    code: 429,
    message: 'Too many requests, please try again later.',
    data: null,
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false
};
```

### 错误处理中间件

```typescript
// 错误处理中间件
interface ErrorHandler {
  (error: Error, req: Request, res: Response, next: NextFunction): void;
}

// 错误类型定义
class APIError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}
```

## 项目结构设计

```
src/
├── app.ts                    # Express应用主文件
├── server.ts                 # 服务器启动文件
├── config/
│   ├── database.ts          # 数据库配置 (如需要)
│   ├── cache.ts             # 缓存配置
│   └── sources.ts           # 音源配置
├── routes/
│   ├── index.ts             # 路由入口
│   ├── api.ts               # 主API路由
│   ├── health.ts            # 健康检查路由
│   └── info.ts              # 系统信息路由
├── services/
│   ├── musicService.ts      # 音乐服务
│   ├── cacheService.ts      # 缓存服务
│   ├── searchService.ts     # 搜索服务
│   ├── urlService.ts        # URL获取服务
│   ├── pictureService.ts    # 图片服务
│   └── lyricService.ts      # 歌词服务
├── middleware/
│   ├── cors.ts              # CORS中间件
│   ├── rateLimit.ts         # 频率限制中间件
│   ├── errorHandler.ts      # 错误处理中间件
│   ├── logger.ts            # 日志中间件
│   └── validation.ts        # 参数验证中间件
├── types/
│   ├── api.ts               # API类型定义
│   ├── music.ts             # 音乐相关类型
│   └── common.ts            # 通用类型定义
├── utils/
│   ├── logger.ts            # 日志工具
│   ├── response.ts          # 响应格式化工具
│   ├── validation.ts        # 验证工具
│   └── constants.ts         # 常量定义
└── tests/                   # 测试文件
    ├── unit/                # 单元测试
    ├── integration/         # 集成测试
    └── fixtures/            # 测试数据
```

## 安全设计

### 输入验证

```typescript
// 参数验证规则
const validationRules = {
  types: ['search', 'url', 'pic', 'lyric'],
  source: /^[a-zA-Z_]+$/,
  name: /^.{1,100}$/,
  id: /^[a-zA-Z0-9_-]+$/,
  count: { min: 1, max: 100 },
  pages: { min: 1, max: 1000 },
  br: [128, 192, 320, 740, 999],
  size: [300, 500]
};
```

### 安全头配置

```typescript
// Helmet安全配置
const helmetConfig = {
  contentSecurityPolicy: false, // API服务不需要CSP
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" },
  dnsPrefetchControl: true,
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: { policy: "no-referrer" },
  xssFilter: true
};
```

## 性能优化设计

### 缓存策略

```typescript
// 多层缓存设计
interface CacheStrategy {
  memory: {
    search: number;      // 搜索结果缓存时间
    url: number;         // 音乐链接缓存时间
    pic: number;         // 图片链接缓存时间
    lyric: number;       // 歌词缓存时间
  };
  http: {
    maxAge: number;      // HTTP缓存时间
    staleWhileRevalidate: number; // 过期后重新验证时间
  };
}

const cacheStrategy: CacheStrategy = {
  memory: {
    search: 300,         // 5分钟
    url: 1800,          // 30分钟
    pic: 3600,          // 1小时
    lyric: 3600         // 1小时
  },
  http: {
    maxAge: 300,        // 5分钟
    staleWhileRevalidate: 600 // 10分钟
  }
};
```

### 响应优化

```typescript
// 响应压缩配置
const compressionConfig = {
  level: 6,              // 压缩级别
  threshold: 1024,       // 最小压缩大小
  filter: (req: Request, res: Response) => {
    return /json|text|javascript|css/.test(res.get('Content-Type') || '');
  }
};
```

## 部署设计

### Docker配置

```dockerfile
# 多阶段构建
FROM node:18-alpine AS base
WORKDIR /app

# 依赖安装阶段
FROM base AS deps
COPY package*.json ./
RUN npm ci --only=production

# 构建阶段
FROM base AS build
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 生产运行阶段
FROM base AS runtime
COPY --from=deps /app/node_modules ./node_modules
COPY --from=build /app/dist ./dist
COPY package*.json ./

EXPOSE 3000
USER node
CMD ["npm", "start"]
```

### 环境配置

```typescript
// 环境变量配置
interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  CORS_ORIGIN: string;
  CACHE_TTL: number;
  RATE_LIMIT_WINDOW: number;
  RATE_LIMIT_MAX: number;
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
  ENABLE_CACHE: boolean;
  DEFAULT_SOURCE: string;
  REDIS_ENABLED: boolean;
  REDIS_HOST: string;
  REDIS_PORT: number;
  ENABLE_HTTPS: boolean;
  SSL_KEY_PATH: string;
  SSL_CERT_PATH: string;
}
```

## 🔄 缓存架构设计

### 多层缓存策略

```mermaid
graph LR
    A[请求] --> B{L1缓存<br/>内存}
    B -->|命中| C[返回结果]
    B -->|未命中| D{L2缓存<br/>Redis}
    D -->|命中| E[回填L1] --> C
    D -->|未命中| F[外部API]
    F --> G[缓存结果] --> C
```

### 缓存键设计

```typescript
// 缓存键命名规范
const cacheKey = {
  search: (source: string, keyword: string, page: number) =>
    `search:${source}:${hashString(keyword)}:${page}`,
  url: (source: string, id: string, quality: number) =>
    `url:${source}:${id}:${quality}`,
  picture: (source: string, id: string, size: number) =>
    `pic:${source}:${id}:${size}`,
  lyric: (source: string, id: string) =>
    `lyric:${source}:${id}`
};

// 缓存配置策略
interface CacheConfig {
  search: { ttl: 300, maxSize: 1000 };    // 5分钟，1000条
  url: { ttl: 1800, maxSize: 500 };       // 30分钟，500条
  picture: { ttl: 3600, maxSize: 200 };   // 1小时，200条
  lyric: { ttl: 7200, maxSize: 300 };     // 2小时，300条
}
```

## 🔒 安全架构设计

### 安全防护层次

```mermaid
graph TB
    A[客户端请求] --> B[网络层安全]
    B --> C[应用层安全]
    C --> D[数据层安全]

    B --> B1[HTTPS/TLS]
    B --> B2[防火墙]
    B --> B3[DDoS防护]

    C --> C1[CORS策略]
    C --> C2[频率限制]
    C --> C3[输入验证]
    C --> C4[安全头部]

    D --> D1[数据加密]
    D --> D2[访问控制]
    D --> D3[审计日志]
```

### 威胁检测配置

```typescript
// 威胁检测模式
interface ThreatDetection {
  sqlInjection: RegExp[];      // SQL注入模式
  xssPatterns: RegExp[];       // XSS攻击模式
  pathTraversal: RegExp[];     // 路径遍历模式
  commandInjection: RegExp[];  // 命令注入模式
}

// 安全响应策略
interface SecurityResponse {
  temporaryBlock: (ip: string, duration: number) => void;
  alertNotification: (threat: ThreatEvent) => void;
  securityLog: (event: SecurityEvent) => void;
}
```

## 📊 监控设计

### 监控指标体系

```typescript
// 监控指标定义
interface MonitoringMetrics {
  // 业务指标
  business: {
    requestCount: number;        // 请求总数
    successRate: number;         // 成功率
    averageResponseTime: number; // 平均响应时间
    cacheHitRate: number;       // 缓存命中率
  };

  // 系统指标
  system: {
    cpuUsage: number;           // CPU使用率
    memoryUsage: number;        // 内存使用率
    diskUsage: number;          // 磁盘使用率
    networkIO: number;          // 网络IO
  };

  // 应用指标
  application: {
    activeConnections: number;   // 活跃连接数
    queueLength: number;        // 队列长度
    errorRate: number;          // 错误率
    gcFrequency: number;        // 垃圾回收频率
  };
}
```

## 🚀 扩展性设计

### 水平扩展架构

```mermaid
graph TB
    A[负载均衡器] --> B[API实例1]
    A --> C[API实例2]
    A --> D[API实例3]

    B --> E[Redis集群]
    C --> E
    D --> E

    B --> F[外部API]
    C --> F
    D --> F
```

### 微服务拆分策略

```typescript
// 服务拆分边界
interface ServiceBoundaries {
  searchService: {
    responsibilities: ['音乐搜索', '结果聚合', '搜索缓存'];
    apis: ['/api/v1/search'];
  };

  mediaService: {
    responsibilities: ['音乐链接', '专辑图片', '歌词获取'];
    apis: ['/api/v1/songs/:id/url', '/api/v1/songs/:id/picture', '/api/v1/songs/:id/lyrics'];
  };

  cacheService: {
    responsibilities: ['缓存管理', '缓存策略', '缓存统计'];
    apis: ['/api/v1/cache/*'];
  };

  monitoringService: {
    responsibilities: ['健康检查', '性能监控', '告警通知'];
    apis: ['/health', '/metrics', '/info'];
  };
}
```

---

这个设计文档提供了完整的UNM API Server架构设计，包括分层架构、数据模型、缓存策略、安全防护、监控体系和扩展性设计，确保项目实施的技术规范性、安全性和可维护性。
