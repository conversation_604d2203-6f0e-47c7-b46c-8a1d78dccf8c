# 🔒 安全指南

本文档详细介绍了 UNM API Server 的安全配置、最佳实践和安全防护措施。

## 🛡️ 安全概览

UNM API Server 采用多层安全防护策略，从网络层到应用层提供全面的安全保护。

### 安全原则

1. **纵深防御** - 多层安全防护
2. **最小权限** - 最小化访问权限
3. **安全默认** - 默认安全配置
4. **持续监控** - 实时安全监控
5. **快速响应** - 快速安全事件响应

## 🔐 认证和授权

### API密钥认证 (可选)

```typescript
// 环境变量配置
API_KEY_ENABLED=true
API_KEYS=key1,key2,key3

// 中间件实现
export const apiKeyAuth = (req: Request, res: Response, next: NextFunction) => {
  if (!config.apiKeyEnabled) {
    return next();
  }
  
  const apiKey = req.headers['x-api-key'] || req.query.apikey;
  
  if (!apiKey || !config.validApiKeys.includes(apiKey as string)) {
    return res.status(401).json({
      code: 401,
      message: 'Invalid or missing API key',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};
```

### JWT认证 (高级功能)

```typescript
import jwt from 'jsonwebtoken';

export const jwtAuth = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({
      code: 401,
      message: 'Access token required',
      data: null
    });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({
      code: 401,
      message: 'Invalid access token',
      data: null
    });
  }
};
```

## 🚫 访问控制

### CORS 配置

```typescript
// 生产环境 CORS 配置
const corsOptions: CorsOptions = {
  origin: (origin, callback) => {
    // 允许的域名列表
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || [];
    
    // 允许无 origin 的请求 (如移动应用)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin) || allowedOrigins.includes('*')) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'X-API-Key'
  ],
  credentials: true,
  maxAge: 86400 // 24小时
};

app.use(cors(corsOptions));
```

### IP白名单

```typescript
export const ipWhitelist = (req: Request, res: Response, next: NextFunction) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const allowedIPs = process.env.ALLOWED_IPS?.split(',') || [];
  
  if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
    logger.warn('Blocked request from unauthorized IP', { ip: clientIP });
    return res.status(403).json({
      code: 403,
      message: 'Access denied from this IP address',
      data: null
    });
  }
  
  next();
};
```

## 🛡️ 安全头部配置

### Helmet 安全头部

```typescript
import helmet from 'helmet';

app.use(helmet({
  // 内容安全策略
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.example.com"],
      mediaSrc: ["'self'", "https:"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  
  // HTTP严格传输安全
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true
  },
  
  // 禁用 X-Powered-By 头部
  hidePoweredBy: true,
  
  // 防止点击劫持
  frameguard: { action: 'deny' },
  
  // 防止 MIME 类型嗅探
  noSniff: true,
  
  // XSS 保护
  xssFilter: true,
  
  // 引用策略
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  
  // 权限策略
  permissionsPolicy: {
    features: {
      geolocation: ["'none'"],
      microphone: ["'none'"],
      camera: ["'none'"],
      payment: ["'none'"],
      usb: ["'none'"]
    }
  }
}));
```

### 自定义安全头部

```typescript
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // 服务器信息隐藏
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  
  // 自定义安全头部
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  // API版本信息
  res.setHeader('X-API-Version', process.env.API_VERSION || '1.0.0');
  
  next();
};
```

## 🚦 频率限制

### 基础频率限制

```typescript
import rateLimit from 'express-rate-limit';

// 全局频率限制
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100次请求
  message: {
    code: 429,
    message: 'Too many requests, please try again later',
    data: null,
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.ip || 'unknown';
  }
});

// API特定频率限制
const apiLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 20, // 最多20次API请求
  message: {
    code: 429,
    message: 'API rate limit exceeded',
    data: null
  }
});

app.use(globalLimiter);
app.use('/api', apiLimiter);
```

### 高级频率限制

```typescript
import slowDown from 'express-slow-down';

// 慢速攻击防护
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15分钟
  delayAfter: 50, // 50次请求后开始延迟
  delayMs: 500, // 每次增加500ms延迟
  maxDelayMs: 20000, // 最大延迟20秒
  skipFailedRequests: true,
  skipSuccessfulRequests: false
});

// 基于用户的频率限制
const userLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: (req) => {
    // VIP用户更高限制
    if (req.user?.tier === 'vip') return 100;
    if (req.user?.tier === 'premium') return 50;
    return 20; // 普通用户
  },
  keyGenerator: (req) => {
    return req.user?.id || req.ip;
  }
});
```

## 🔍 输入验证和清理

### 参数验证

```typescript
import Joi from 'joi';

// 搜索参数验证
const searchSchema = Joi.object({
  keyword: Joi.string()
    .min(1)
    .max(100)
    .pattern(/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/)
    .required()
    .messages({
      'string.pattern.base': 'Keyword contains invalid characters',
      'string.min': 'Keyword must be at least 1 character',
      'string.max': 'Keyword must not exceed 100 characters'
    }),
  
  source: Joi.string()
    .valid('netease', 'qq', 'kuwo', 'kugou', 'xiami', 'baidu', 'migu')
    .default('netease'),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20),
  
  offset: Joi.number()
    .integer()
    .min(0)
    .default(0)
});

export const validateSearchParams = (req: Request, res: Response, next: NextFunction) => {
  const { error, value } = searchSchema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      code: 400,
      message: 'Invalid parameters',
      data: {
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      }
    });
  }
  
  req.query = value;
  next();
};
```

### SQL注入防护

```typescript
// 输入清理函数
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // 移除HTML标签
    .replace(/['"]/g, '') // 移除引号
    .replace(/[;]/g, '') // 移除分号
    .replace(/--/g, '') // 移除SQL注释
    .trim();
};

// XSS防护
export const escapeHtml = (unsafe: string): string => {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};
```

## 🔐 数据加密

### 敏感数据加密

```typescript
import crypto from 'crypto';

export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key: Buffer;
  
  constructor() {
    this.key = crypto.scryptSync(process.env.ENCRYPTION_KEY!, 'salt', 32);
  }
  
  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key);
    cipher.setAAD(Buffer.from('additional-data'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }
  
  decrypt(encryptedData: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedData.split(':');
    
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipher(this.algorithm, this.key);
    decipher.setAAD(Buffer.from('additional-data'));
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

### 密码哈希

```typescript
import bcrypt from 'bcrypt';

export class PasswordService {
  private readonly saltRounds = 12;
  
  async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, this.saltRounds);
  }
  
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash);
  }
}
```

## 🚨 安全监控

### 威胁检测

```typescript
export class SecurityMonitor {
  private suspiciousPatterns = [
    // SQL注入模式
    /union.*select/gi,
    /select.*from/gi,
    /insert.*into/gi,
    /delete.*from/gi,
    
    // XSS模式
    /<script[^>]*>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    
    // 路径遍历
    /\.\.\//g,
    /\.\.\\/g,
    
    // 命令注入
    /;\s*(ls|dir|cat|type|whoami)/gi
  ];
  
  detectThreats(input: string): string[] {
    const threats: string[] = [];
    
    this.suspiciousPatterns.forEach((pattern, index) => {
      if (pattern.test(input)) {
        threats.push(`Pattern ${index + 1} detected`);
      }
    });
    
    return threats;
  }
  
  logSecurityEvent(event: SecurityEvent): void {
    logger.warn('Security event detected', {
      type: event.type,
      ip: event.ip,
      userAgent: event.userAgent,
      payload: event.payload,
      timestamp: new Date().toISOString()
    });
    
    // 发送告警
    if (event.severity === 'high') {
      this.sendAlert(event);
    }
  }
  
  private sendAlert(event: SecurityEvent): void {
    // 发送邮件、短信或Webhook通知
    // 实现具体的告警逻辑
  }
}
```

### 安全日志

```typescript
export const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: 'logs/security.log',
      level: 'warn'
    }),
    new winston.transports.File({
      filename: 'logs/security-error.log',
      level: 'error'
    })
  ]
});

// 安全事件记录中间件
export const securityAudit = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // 记录请求信息
  securityLogger.info('Request received', {
    ip: req.ip,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  
  // 监听响应
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    securityLogger.info('Request completed', {
      ip: req.ip,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString()
    });
  });
  
  next();
};
```

## 🔧 HTTPS/TLS 配置

### SSL证书配置

```typescript
import https from 'https';
import fs from 'fs';

// HTTPS服务器配置
export const createHTTPSServer = (app: Express) => {
  if (!process.env.ENABLE_HTTPS) {
    return null;
  }
  
  const options = {
    key: fs.readFileSync(process.env.SSL_KEY_PATH!),
    cert: fs.readFileSync(process.env.SSL_CERT_PATH!),
    
    // TLS配置
    secureProtocol: 'TLSv1_2_method',
    ciphers: [
      'ECDHE-RSA-AES256-GCM-SHA512',
      'DHE-RSA-AES256-GCM-SHA512',
      'ECDHE-RSA-AES256-GCM-SHA384',
      'DHE-RSA-AES256-GCM-SHA384'
    ].join(':'),
    honorCipherOrder: true
  };
  
  const httpsServer = https.createServer(options, app);
  
  httpsServer.listen(process.env.HTTPS_PORT || 443, () => {
    logger.info(`HTTPS Server running on port ${process.env.HTTPS_PORT || 443}`);
  });
  
  return httpsServer;
};
```

### HTTP重定向到HTTPS

```typescript
export const httpsRedirect = (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'production' && !req.secure) {
    return res.redirect(301, `https://${req.headers.host}${req.url}`);
  }
  next();
};
```

## 📋 安全检查清单

### 部署前安全检查

- [ ] 更新所有依赖包到最新版本
- [ ] 配置强密码和密钥
- [ ] 启用HTTPS/TLS加密
- [ ] 配置防火墙规则
- [ ] 设置适当的CORS策略
- [ ] 启用安全头部
- [ ] 配置频率限制
- [ ] 实施输入验证
- [ ] 配置安全日志
- [ ] 设置监控告警
- [ ] 进行安全扫描测试
- [ ] 制定安全事件响应计划

### 运行时安全监控

- [ ] 监控异常请求模式
- [ ] 检查错误日志
- [ ] 监控系统资源使用
- [ ] 检查安全告警
- [ ] 定期更新安全配置
- [ ] 进行安全审计

## 🚨 安全事件响应

### 事件分类

1. **低风险** - 单次可疑请求
2. **中风险** - 重复可疑行为
3. **高风险** - 明确的攻击行为
4. **严重** - 系统被入侵

### 响应流程

1. **检测** - 自动检测安全事件
2. **分析** - 评估威胁级别
3. **响应** - 执行相应的防护措施
4. **恢复** - 恢复正常服务
5. **总结** - 分析事件原因和改进措施

### 自动响应措施

```typescript
export class SecurityResponse {
  async handleSecurityEvent(event: SecurityEvent): Promise<void> {
    switch (event.severity) {
      case 'low':
        // 记录日志
        securityLogger.warn('Low severity security event', event);
        break;
        
      case 'medium':
        // 临时限制IP
        await this.temporaryIPBlock(event.ip, 300); // 5分钟
        break;
        
      case 'high':
        // 长期限制IP并发送告警
        await this.blockIP(event.ip, 3600); // 1小时
        await this.sendAlert(event);
        break;
        
      case 'critical':
        // 立即阻断并通知管理员
        await this.blockIP(event.ip, 86400); // 24小时
        await this.emergencyAlert(event);
        break;
    }
  }
}
```

## 📚 安全资源

### 安全工具

- **Helmet.js** - Express安全中间件
- **Express Rate Limit** - 频率限制
- **Joi** - 输入验证
- **bcrypt** - 密码哈希
- **jsonwebtoken** - JWT认证

### 安全扫描

```bash
# 依赖漏洞扫描
npm audit

# 安全扫描
npm install -g nsp
nsp check

# 代码安全分析
npm install -g eslint-plugin-security
```

### 安全测试

```bash
# 运行安全测试
npm run security-test

# API安全测试
npm run security-test-api

# 网络安全测试
npm run security-test-network
```

---

安全是一个持续的过程，需要定期更新和改进安全措施。
