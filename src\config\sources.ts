import { MusicSource, SourcesConfig } from './../types/music';
import { config } from './environment';

// 音源名称映射
const sourceNames: Record<string, string> = {
  'netease': '网易云音乐',
  'qq': 'QQ音乐',
  'xiami': '虾米音乐',
  'baidu': '百度音乐',
  'kugou': '酷狗音乐',
  'kuwo': '酷我音乐',
  'migu': '咪咕音乐',
  'joox': 'JOOX音乐',
  'youtube': 'YouTube Music',
  'bilibili': '哔哩哔哩',
  'pyncmd': 'Python网易云音乐',
};

// 根据配置生成音源配置
export const musicSources: MusicSource[] = config.supportedSources.map((sourceId, index) => ({
  id: sourceId,
  name: sourceNames[sourceId] || sourceId.toUpperCase(),
  enabled: true,
  priority: index + 1,
  stable: config.stableSources.includes(sourceId),
}));

// 获取音源配置
export const getSourcesConfig = (): SourcesConfig => {
  const enabledSources = musicSources.filter(source => source.enabled);
  const stableSources = enabledSources.filter(source => source.stable);
  
  return {
    stable: stableSources.map(source => source.id),
    all: enabledSources.map(source => source.id),
  };
};

// 验证音源是否支持
export const isValidSource = (source: string): boolean => {
  return musicSources.some(s => s.id === source && s.enabled);
};

// 获取默认音源列表
export const getDefaultSources = (): string[] => {
  return config.stableSources.length > 0 ? config.stableSources : config.supportedSources.slice(0, 3);
};



// 根据优先级排序音源
export const getSortedSources = (): string[] => {
  return musicSources
    .filter(source => source.enabled)
    .sort((a, b) => a.priority - b.priority)
    .map(source => source.id);
};

// 获取所有支持的音源
export const getAllSources = (): string[] => {
  return [...config.supportedSources];
};

// 获取稳定音源列表
export const getStableSources = (): string[] => {
  return [...config.stableSources];
};
