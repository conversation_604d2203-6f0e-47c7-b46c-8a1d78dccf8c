import { logger } from '../utils/logger';
import { config } from '../config/environment';

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

/**
 * 缓存统计信息
 */
interface CacheStats {
  totalItems: number;
  totalHits: number;
  totalMisses: number;
  hitRate: number;
  memoryUsage: number;
}

/**
 * 内存缓存服务类
 */
export class CacheService {
  private cache: Map<string, CacheItem<any>>;
  private defaultTTL: number;
  private maxSize: number;
  private cleanupInterval: NodeJS.Timeout | null;
  private stats: {
    hits: number;
    misses: number;
  };

  constructor(defaultTTL: number = config.cacheTTL * 1000, maxSize: number = config.cacheMaxSize) {
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
    this.maxSize = maxSize;
    this.cleanupInterval = null;
    this.stats = {
      hits: 0,
      misses: 0,
    };

    // 启动定期清理过期缓存
    this.startCleanupInterval();

    logger.info('Cache service initialized', {
      defaultTTL: this.defaultTTL,
      maxSize: this.maxSize,
    });
  }

  /**
   * 设置缓存
   */
  set<T>(key: string, data: T, ttl?: number): void {
    try {
      // 如果缓存已满，删除最旧的项目
      if (this.cache.size >= this.maxSize) {
        this.evictOldest();
      }

      const now = Date.now();
      const item: CacheItem<T> = {
        data,
        timestamp: now,
        ttl: ttl || this.defaultTTL,
        accessCount: 0,
        lastAccessed: now,
      };

      this.cache.set(key, item);
      
      logger.debug('Cache set', { key, ttl: item.ttl });
    } catch (error) {
      logger.error('Cache set failed', { key, error });
    }
  }

  /**
   * 获取缓存
   */
  get<T>(key: string): T | null {
    try {
      const item = this.cache.get(key);
      
      if (!item) {
        this.stats.misses++;
        logger.debug('Cache miss', { key });
        return null;
      }

      const now = Date.now();
      
      // 检查是否过期
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        this.stats.misses++;
        logger.debug('Cache expired', { key, age: now - item.timestamp });
        return null;
      }

      // 更新访问统计
      item.accessCount++;
      item.lastAccessed = now;
      this.stats.hits++;
      
      logger.debug('Cache hit', { key, accessCount: item.accessCount });
      return item.data as T;
    } catch (error) {
      logger.error('Cache get failed', { key, error });
      this.stats.misses++;
      return null;
    }
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    try {
      const result = this.cache.delete(key);
      logger.debug('Cache delete', { key, success: result });
      return result;
    } catch (error) {
      logger.error('Cache delete failed', { key, error });
      return false;
    }
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    try {
      const size = this.cache.size;
      this.cache.clear();
      this.stats.hits = 0;
      this.stats.misses = 0;
      logger.info('Cache cleared', { previousSize: size });
    } catch (error) {
      logger.error('Cache clear failed', { error });
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    
    // 估算内存使用量 (简单估算)
    const memoryUsage = this.cache.size * 1024; // 假设每个缓存项平均1KB
    
    return {
      totalItems: this.cache.size,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage,
    };
  }

  /**
   * 获取所有缓存键
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    try {
      const now = Date.now();
      let expiredCount = 0;
      
      for (const [key, item] of this.cache.entries()) {
        if (now - item.timestamp > item.ttl) {
          this.cache.delete(key);
          expiredCount++;
        }
      }
      
      if (expiredCount > 0) {
        logger.debug('Cache cleanup completed', { 
          expiredCount, 
          remainingItems: this.cache.size, 
        });
      }
    } catch (error) {
      logger.error('Cache cleanup failed', { error });
    }
  }

  /**
   * 驱逐最旧的缓存项
   */
  private evictOldest(): void {
    try {
      let oldestKey: string | null = null;
      let oldestTime = Date.now();
      
      for (const [key, item] of this.cache.entries()) {
        if (item.lastAccessed < oldestTime) {
          oldestTime = item.lastAccessed;
          oldestKey = key;
        }
      }
      
      if (oldestKey) {
        this.cache.delete(oldestKey);
        logger.debug('Cache evicted oldest item', { key: oldestKey });
      }
    } catch (error) {
      logger.error('Cache eviction failed', { error });
    }
  }

  /**
   * 启动定期清理
   */
  private startCleanupInterval(): void {
    // 使用配置的清理间隔
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, config.cacheCleanupInterval);
  }

  /**
   * 停止缓存服务
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
    logger.info('Cache service destroyed');
  }
}

// 导出单例实例
export const cacheService = new CacheService();
