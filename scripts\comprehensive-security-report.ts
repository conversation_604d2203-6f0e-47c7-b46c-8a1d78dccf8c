#!/usr/bin/env tsx

/**
 * 综合安全报告生成器
 * 运行所有安全测试并生成详细报告
 */

import { SecurityTester } from './security-test';
import { APISecurityTester } from './api-security-test';
import { NetworkSecurityTester } from './network-security-test';
import * as fs from 'fs';
import * as path from 'path';

interface SecurityReport {
  timestamp: string;
  target: string;
  overallScore: number;
  riskLevel: string;
  summary: {
    totalTests: number;
    passed: number;
    failed: number;
    warnings: number;
    info: number;
  };
  severitySummary: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  categories: {
    basic: any;
    api: any;
    network: any;
  };
  recommendations: string[];
  criticalIssues: any[];
}

class ComprehensiveSecurityReporter {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:5678') {
    this.baseUrl = baseUrl;
  }

  private calculateOverallScore(basicScore: number, apiScore: number, networkScore: number): number {
    // 权重分配：基础安全40%，API安全40%，网络安全20%
    return Math.round(basicScore * 0.4 + apiScore * 0.4 + networkScore * 0.2);
  }

  private determineRiskLevel(score: number): string {
    if (score >= 90) return '低风险';
    if (score >= 75) return '中低风险';
    if (score >= 60) return '中等风险';
    if (score >= 40) return '中高风险';
    return '高风险';
  }

  private generateRecommendations(basicResults: any, apiResults: any, networkResults: any): string[] {
    const recommendations = [];

    // 基于测试结果生成建议
    if (basicResults.severitySummary.critical > 0) {
      recommendations.push('🔴 立即修复严重安全漏洞');
    }
    
    if (basicResults.severitySummary.high > 0 || apiResults.highIssues > 0) {
      recommendations.push('🟠 优先处理高危安全问题');
    }

    if (networkResults.score < 80) {
      recommendations.push('🌐 加强网络层安全配置');
    }

    if (apiResults.score < 70) {
      recommendations.push('🔌 改进API安全防护');
    }

    // 通用建议
    recommendations.push('🔒 实施HTTPS和强TLS配置');
    recommendations.push('🛡️ 部署Web应用防火墙(WAF)');
    recommendations.push('📊 建立安全监控和日志分析');
    recommendations.push('🔄 定期进行安全评估和渗透测试');
    recommendations.push('👥 加强安全意识培训');
    recommendations.push('📋 制定安全事件响应计划');

    return recommendations;
  }

  private generateHTMLReport(report: SecurityReport): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNM API Server 安全评估报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }
        .content { padding: 30px; }
        .score-card { display: flex; justify-content: space-around; margin: 20px 0; }
        .score-item { text-align: center; padding: 20px; border-radius: 10px; margin: 0 10px; flex: 1; }
        .score-high { background: #d4edda; color: #155724; }
        .score-medium { background: #fff3cd; color: #856404; }
        .score-low { background: #f8d7da; color: #721c24; }
        .section { margin: 30px 0; padding: 20px; border-left: 4px solid #667eea; background: #f8f9fa; }
        .issue-critical { color: #dc3545; font-weight: bold; }
        .issue-high { color: #fd7e14; font-weight: bold; }
        .issue-medium { color: #ffc107; font-weight: bold; }
        .issue-low { color: #28a745; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .chart { width: 100%; height: 300px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .status-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 UNM API Server 安全评估报告</h1>
            <p>目标: ${report.target}</p>
            <p>生成时间: ${report.timestamp}</p>
        </div>
        
        <div class="content">
            <div class="score-card">
                <div class="score-item ${report.overallScore >= 80 ? 'score-high' : report.overallScore >= 60 ? 'score-medium' : 'score-low'}">
                    <h2>${report.overallScore}/100</h2>
                    <p>总体安全评分</p>
                </div>
                <div class="score-item ${report.riskLevel.includes('低') ? 'score-high' : report.riskLevel.includes('中') ? 'score-medium' : 'score-low'}">
                    <h2>${report.riskLevel}</h2>
                    <p>风险等级</p>
                </div>
                <div class="score-item">
                    <h2>${report.summary.totalTests}</h2>
                    <p>总测试项目</p>
                </div>
            </div>

            <div class="section">
                <h2>📊 测试统计</h2>
                <table>
                    <tr><th>状态</th><th>数量</th><th>百分比</th></tr>
                    <tr><td class="status-pass">✅ 通过</td><td>${report.summary.passed}</td><td>${Math.round(report.summary.passed/report.summary.totalTests*100)}%</td></tr>
                    <tr><td class="status-fail">❌ 失败</td><td>${report.summary.failed}</td><td>${Math.round(report.summary.failed/report.summary.totalTests*100)}%</td></tr>
                    <tr><td class="status-warning">⚠️ 警告</td><td>${report.summary.warnings}</td><td>${Math.round(report.summary.warnings/report.summary.totalTests*100)}%</td></tr>
                    <tr><td class="status-info">📋 信息</td><td>${report.summary.info}</td><td>${Math.round(report.summary.info/report.summary.totalTests*100)}%</td></tr>
                </table>
            </div>

            <div class="section">
                <h2>🚨 风险等级分布</h2>
                <table>
                    <tr><th>风险等级</th><th>数量</th><th>描述</th></tr>
                    <tr><td class="issue-critical">🔴 严重</td><td>${report.severitySummary.critical}</td><td>需要立即修复的安全漏洞</td></tr>
                    <tr><td class="issue-high">🟠 高危</td><td>${report.severitySummary.high}</td><td>可能导致系统被攻破的问题</td></tr>
                    <tr><td class="issue-medium">🟡 中危</td><td>${report.severitySummary.medium}</td><td>可能被利用的安全弱点</td></tr>
                    <tr><td class="issue-low">🟢 低危</td><td>${report.severitySummary.low}</td><td>安全配置改进建议</td></tr>
                </table>
            </div>

            ${report.criticalIssues.length > 0 ? `
            <div class="section">
                <h2>🚨 严重安全问题</h2>
                <ul>
                    ${report.criticalIssues.map(issue => `<li class="issue-critical">[${issue.category}] ${issue.test}: ${issue.message}</li>`).join('')}
                </ul>
            </div>
            ` : ''}

            <div class="section">
                <h2>📋 分类测试结果</h2>
                <h3>🔐 基础安全测试</h3>
                <p>评分: ${report.categories.basic.summary ? Math.round((report.categories.basic.summary.pass / (report.categories.basic.summary.pass + report.categories.basic.summary.fail + report.categories.basic.summary.warning)) * 100) : 'N/A'}/100</p>
                
                <h3>🔌 API安全测试</h3>
                <p>评分: ${report.categories.api.score || 'N/A'}/100</p>
                
                <h3>🌐 网络安全测试</h3>
                <p>评分: ${report.categories.network.score || 'N/A'}/100</p>
            </div>

            <div class="recommendations">
                <h2>💡 安全改进建议</h2>
                <ul>
                    ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>

            <div class="section">
                <h2>📝 详细说明</h2>
                <p><strong>测试范围：</strong>本次安全评估涵盖了Web应用安全、API安全、网络安全等多个维度。</p>
                <p><strong>测试方法：</strong>采用自动化安全扫描和手工渗透测试相结合的方式。</p>
                <p><strong>风险评估：</strong>基于OWASP Top 10和行业最佳实践进行风险评级。</p>
                <p><strong>建议优先级：</strong>建议优先修复严重和高危问题，然后逐步改进中低危问题。</p>
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  async generateComprehensiveReport(): Promise<SecurityReport> {
    console.log('🔒 开始综合安全评估...');
    console.log(`🎯 目标: ${this.baseUrl}\n`);

    // 运行基础安全测试
    console.log('1️⃣ 运行基础安全测试...');
    const basicTester = new SecurityTester(this.baseUrl);
    const basicResults = await basicTester.runAllTests();

    console.log('\n' + '='.repeat(60) + '\n');

    // 运行API安全测试
    console.log('2️⃣ 运行API安全测试...');
    const apiTester = new APISecurityTester(this.baseUrl);
    const apiResults = await apiTester.runAllTests();

    console.log('\n' + '='.repeat(60) + '\n');

    // 运行网络安全测试
    console.log('3️⃣ 运行网络安全测试...');
    const networkTester = new NetworkSecurityTester(this.baseUrl);
    const networkResults = await networkTester.runAllTests();

    // 计算综合评分
    const basicScore = Math.round((basicResults.summary.pass / (basicResults.summary.pass + basicResults.summary.fail + basicResults.summary.warning)) * 100);
    const overallScore = this.calculateOverallScore(basicScore, apiResults.score, networkResults.score);
    const riskLevel = this.determineRiskLevel(overallScore);

    // 汇总统计
    const totalSummary = {
      totalTests: basicResults.summary.total + apiResults.allResults.length + networkResults.allResults.length,
      passed: basicResults.summary.pass + apiResults.allResults.filter(r => r.status === 'PASS').length + networkResults.allResults.filter(r => r.status === 'PASS').length,
      failed: basicResults.summary.fail + apiResults.allResults.filter(r => r.status === 'FAIL').length + networkResults.allResults.filter(r => r.status === 'FAIL').length,
      warnings: basicResults.summary.warning + apiResults.allResults.filter(r => r.status === 'WARNING').length + networkResults.allResults.filter(r => r.status === 'WARNING').length,
      info: basicResults.summary.info + apiResults.allResults.filter(r => r.status === 'INFO').length + networkResults.allResults.filter(r => r.status === 'INFO').length
    };

    const totalSeveritySummary = {
      critical: basicResults.severitySummary.critical + apiResults.criticalIssues.length + networkResults.criticalIssues,
      high: basicResults.severitySummary.high + apiResults.highIssues.length + networkResults.highIssues,
      medium: basicResults.severitySummary.medium + apiResults.mediumIssues.length + networkResults.mediumIssues,
      low: basicResults.severitySummary.low + networkResults.lowIssues
    };

    // 收集所有严重问题
    const allCriticalIssues = [
      ...basicResults.highRiskIssues,
      ...apiResults.criticalIssues,
      ...apiResults.highIssues
    ];

    const report: SecurityReport = {
      timestamp: new Date().toLocaleString('zh-CN'),
      target: this.baseUrl,
      overallScore,
      riskLevel,
      summary: totalSummary,
      severitySummary: totalSeveritySummary,
      categories: {
        basic: basicResults,
        api: apiResults,
        network: networkResults
      },
      recommendations: this.generateRecommendations(basicResults, apiResults, networkResults),
      criticalIssues: allCriticalIssues
    };

    return report;
  }

  async saveReport(report: SecurityReport) {
    const reportsDir = path.join(process.cwd(), 'security-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir);
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // 保存JSON报告
    const jsonPath = path.join(reportsDir, `security-report-${timestamp}.json`);
    fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2));

    // 保存HTML报告
    const htmlPath = path.join(reportsDir, `security-report-${timestamp}.html`);
    const htmlContent = this.generateHTMLReport(report);
    fs.writeFileSync(htmlPath, htmlContent);

    console.log(`\n📄 报告已保存:`);
    console.log(`  JSON: ${jsonPath}`);
    console.log(`  HTML: ${htmlPath}`);

    return { jsonPath, htmlPath };
  }

  async run() {
    try {
      const report = await this.generateComprehensiveReport();
      
      console.log('\n' + '='.repeat(80));
      console.log('📊 综合安全评估报告');
      console.log('='.repeat(80));
      
      console.log(`\n🎯 目标: ${report.target}`);
      console.log(`📅 时间: ${report.timestamp}`);
      console.log(`📊 总体评分: ${report.overallScore}/100`);
      console.log(`⚠️  风险等级: ${report.riskLevel}`);
      
      console.log(`\n📈 测试统计:`);
      console.log(`  总测试数: ${report.summary.totalTests}`);
      console.log(`  ✅ 通过: ${report.summary.passed} (${Math.round(report.summary.passed/report.summary.totalTests*100)}%)`);
      console.log(`  ❌ 失败: ${report.summary.failed} (${Math.round(report.summary.failed/report.summary.totalTests*100)}%)`);
      console.log(`  ⚠️  警告: ${report.summary.warnings} (${Math.round(report.summary.warnings/report.summary.totalTests*100)}%)`);
      console.log(`  📋 信息: ${report.summary.info} (${Math.round(report.summary.info/report.summary.totalTests*100)}%)`);

      console.log(`\n🚨 风险分布:`);
      console.log(`  🔴 严重: ${report.severitySummary.critical}`);
      console.log(`  🟠 高危: ${report.severitySummary.high}`);
      console.log(`  🟡 中危: ${report.severitySummary.medium}`);
      console.log(`  🟢 低危: ${report.severitySummary.low}`);

      if (report.criticalIssues.length > 0) {
        console.log(`\n🚨 严重问题 (${report.criticalIssues.length}个):`);
        report.criticalIssues.forEach((issue, index) => {
          console.log(`  ${index + 1}. [${issue.category}] ${issue.test}: ${issue.message}`);
        });
      }

      console.log(`\n💡 主要建议:`);
      report.recommendations.slice(0, 5).forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });

      await this.saveReport(report);
      
      console.log(`\n🎉 综合安全评估完成！`);
      
      return report;
    } catch (error) {
      console.error('❌ 安全评估失败:', error);
      throw error;
    }
  }
}

// 运行综合安全评估
async function main() {
  const reporter = new ComprehensiveSecurityReporter();
  await reporter.run();
}

if (require.main === module) {
  main().catch(console.error);
}

export { ComprehensiveSecurityReporter };
