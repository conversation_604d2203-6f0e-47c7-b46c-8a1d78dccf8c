import { Router, Request, Response, NextFunction } from 'express';
import { unmService } from './../services/unmService';
import { APIResponse } from './../types/api';
import { APIError } from './../types/common';
import { logger } from './../utils/logger';
import { createSuccessResponse } from './../utils/response';
import { unmCacheMiddleware } from '../middleware/cache';

const router = Router();

/**
 * UNM (UnblockNeteaseMusic) 专用API端点
 */

/**
 * 音乐匹配API - 与主API接口格式保持一致
 * GET /api/v1/match?id=123456&sources=netease,qq&quality=320
 */
router.get('/api/v1/match', unmCacheMiddleware, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { id, sources, quality = '999' } = req.query;

    // 参数验证
    if (!id || typeof id !== 'string' || !/^[0-9]+$/.test(id)) {
      throw new APIError(400, 'Parameter "id" is required and must be numeric');
    }

    const qualityNum = parseInt(quality as string);
    const validQualities = [128, 192, 320, 740, 999];
    if (isNaN(qualityNum) || !validQualities.includes(qualityNum)) {
      throw new APIError(400, `Invalid quality. Must be one of: ${validQualities.join(', ')}`);
    }

    // 解析音源列表
    let sourceList: string[] | undefined;
    if (sources && typeof sources === 'string') {
      sourceList = sources.split(',').map(s => s.trim()).filter(s => s.length > 0);
    }

    logger.info('UNM: Match request received:', {
      id,
      sources: sourceList,
      quality: qualityNum,
      ip: req.ip
    });

    const result = await unmService.getMusicUrl(id, sourceList, qualityNum);

    const duration = Date.now() - startTime;
    logger.info('UNM: Match completed successfully', {
      id,
      source: result.source,
      duration: `${duration}ms`
    });

    const response: APIResponse = createSuccessResponse(result);
    res.json(response);

  } catch (error) {
    logger.error('UNM: Match failed:', error);
    next(error);
  }
});

/**
 * 直接使用UNM获取音乐链接
 * GET /api/v1/unm/songs/:id/url?sources=netease,qq&quality=320
 */
router.get('/api/v1/unm/songs/:id/url', unmCacheMiddleware, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { id } = req.params;
    const { sources, quality = '999' } = req.query;

    // 参数验证
    if (!id || !/^[0-9]+$/.test(id)) {
      throw new APIError(400, 'Invalid song ID: must be numeric');
    }

    const qualityNum = parseInt(quality as string);
    const validQualities = [128, 192, 320, 740, 999];
    if (isNaN(qualityNum) || !validQualities.includes(qualityNum)) {
      throw new APIError(400, `Invalid quality. Must be one of: ${validQualities.join(', ')}`);
    }

    // 解析音源列表
    let sourceList: string[] | undefined;
    if (sources && typeof sources === 'string') {
      sourceList = sources.split(',').map(s => s.trim()).filter(s => s.length > 0);
    }

    logger.info('UNM: Direct music URL request received:', { 
      id, 
      sources: sourceList, 
      quality: qualityNum,
      ip: req.ip 
    });

    const result = await unmService.getMusicUrl(id, sourceList, qualityNum);

    const responseTime = Date.now() - startTime;
    logger.info(`UNM: Music URL retrieved in ${responseTime}ms`, { 
      responseTime,
      source: result.source 
    });

    const response: APIResponse = createSuccessResponse(result);
    res.json(response);

  } catch (error) {
    logger.error('UNM: Music URL request failed:', error);
    next(error);
  }
});

/**
 * 批量获取音乐链接
 * POST /api/v1/unm/songs/batch-url
 * Body: { "ids": ["123", "456"], "sources": ["netease", "qq"], "quality": 320 }
 */
router.post('/api/v1/unm/songs/batch-url', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { ids, sources, quality = 999 } = req.body;

    // 参数验证
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new APIError(400, 'Parameter "ids" must be a non-empty array');
    }

    if (ids.length > 50) {
      throw new APIError(400, 'Maximum 50 songs per batch request');
    }

    // 验证所有ID都是数字
    for (const id of ids) {
      if (!id || !/^[0-9]+$/.test(id.toString())) {
        throw new APIError(400, `Invalid song ID: ${id} (must be numeric)`);
      }
    }

    const qualityNum = parseInt(quality.toString());
    const validQualities = [128, 192, 320, 740, 999];
    if (isNaN(qualityNum) || !validQualities.includes(qualityNum)) {
      throw new APIError(400, `Invalid quality. Must be one of: ${validQualities.join(', ')}`);
    }

    logger.info('UNM: Batch music URL request received:', { 
      songCount: ids.length, 
      sources, 
      quality: qualityNum,
      ip: req.ip 
    });

    const results = await unmService.getBatchMusicUrls(
      ids.map(id => id.toString()), 
      sources, 
      qualityNum
    );

    const responseTime = Date.now() - startTime;
    const successCount = Object.values(results).filter(r => r !== null).length;
    
    logger.info(`UNM: Batch music URLs completed in ${responseTime}ms`, { 
      responseTime,
      total: ids.length,
      success: successCount,
      failed: ids.length - successCount
    });

    const response: APIResponse = createSuccessResponse(results);
    res.json(response);

  } catch (error) {
    logger.error('UNM: Batch music URL request failed:', error);
    next(error);
  }
});

/**
 * 测试音源可用性
 * GET /api/v1/unm/test-sources?sources=netease,qq,xiami&testId=418602084
 */
router.get('/api/v1/unm/test-sources', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { sources, testId = '418602084' } = req.query;

    // 参数验证
    if (!sources || typeof sources !== 'string') {
      throw new APIError(400, 'Parameter "sources" is required (comma-separated list)');
    }

    if (!testId || !/^[0-9]+$/.test(testId as string)) {
      throw new APIError(400, 'Invalid test ID: must be numeric');
    }

    const sourceList = sources.split(',').map(s => s.trim()).filter(s => s.length > 0);
    if (sourceList.length === 0) {
      throw new APIError(400, 'At least one source must be specified');
    }

    logger.info('UNM: Source testing request received:', { 
      sources: sourceList, 
      testId,
      ip: req.ip 
    });

    const results = await unmService.testSources(sourceList, testId as string);

    const responseTime = Date.now() - startTime;
    const availableCount = Object.values(results).filter(r => r).length;
    
    logger.info(`UNM: Source testing completed in ${responseTime}ms`, { 
      responseTime,
      total: sourceList.length,
      available: availableCount
    });

    const response: APIResponse = createSuccessResponse({
      test_song_id: testId,
      results,
      summary: {
        total: sourceList.length,
        available: availableCount,
        unavailable: sourceList.length - availableCount
      }
    });
    res.json(response);

  } catch (error) {
    logger.error('UNM: Source testing request failed:', error);
    next(error);
  }
});

/**
 * 获取UNM支持的音源列表
 * GET /api/v1/unm/supported-sources
 */
router.get('/api/v1/unm/supported-sources', async (req: Request, res: Response, next: NextFunction) => {
  try {
    logger.info('UNM: Supported sources request received:', { ip: req.ip });

    const sources = unmService.getSupportedSources();
    const version = unmService.getVersion();

    const response: APIResponse = createSuccessResponse({
      version,
      sources,
      count: sources.length
    });
    res.json(response);

  } catch (error) {
    logger.error('UNM: Get supported sources failed:', error);
    next(error);
  }
});

/**
 * 获取UNM服务信息
 * GET /api/v1/unm/info
 */
router.get('/api/v1/unm/info', async (req: Request, res: Response, next: NextFunction) => {
  try {
    logger.info('UNM: Service info request received:', { ip: req.ip });

    const version = unmService.getVersion();
    const supportedSources = unmService.getSupportedSources();

    const response: APIResponse = createSuccessResponse({
      service: 'UnblockNeteaseMusic',
      version,
      description: 'Third-party music source integration service',
      supported_sources: supportedSources,
      features: [
        'Direct music URL retrieval',
        'Batch processing',
        'Source availability testing',
        'Multiple source fallback'
      ]
    });
    res.json(response);

  } catch (error) {
    logger.error('UNM: Get service info failed:', error);
    next(error);
  }
});

export default router;
