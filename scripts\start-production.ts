#!/usr/bin/env tsx

/**
 * 生产环境启动脚本
 * 配置生产环境设置并启动服务器
 */

import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';

interface ProductionConfig {
  envFile: string;
  logDir: string;
  sslCertDir: string;
  pidFile: string;
  enableHttps: boolean;
  enableMonitoring: boolean;
}

class ProductionStarter {
  private config: ProductionConfig;

  constructor() {
    this.config = {
      envFile: '.env.production',
      logDir: 'logs',
      sslCertDir: 'certs',
      pidFile: 'server.pid',
      enableHttps: process.env.ENABLE_HTTPS === 'true',
      enableMonitoring: process.env.ENABLE_MONITORING !== 'false'
    };
  }

  private async checkPrerequisites(): Promise<boolean> {
    console.log('🔍 检查生产环境前置条件...');

    // 检查环境配置文件
    if (!fs.existsSync(this.config.envFile)) {
      console.error(`❌ 生产环境配置文件不存在: ${this.config.envFile}`);
      return false;
    }
    console.log(`✅ 环境配置文件: ${this.config.envFile}`);

    // 创建日志目录
    if (!fs.existsSync(this.config.logDir)) {
      fs.mkdirSync(this.config.logDir, { recursive: true });
      console.log(`✅ 创建日志目录: ${this.config.logDir}`);
    } else {
      console.log(`✅ 日志目录存在: ${this.config.logDir}`);
    }

    // 检查SSL证书（如果启用HTTPS）
    if (this.config.enableHttps) {
      const keyPath = process.env.SSL_KEY_PATH || path.join(this.config.sslCertDir, 'server.key');
      const certPath = process.env.SSL_CERT_PATH || path.join(this.config.sslCertDir, 'server.crt');

      if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
        console.log('⚠️  SSL证书不存在，将生成自签名证书用于测试...');
        await this.generateSelfSignedCert();
      } else {
        console.log('✅ SSL证书文件存在');
      }
    }

    // 检查Node.js版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
      console.error(`❌ Node.js版本过低: ${nodeVersion}，需要 >= 18.0.0`);
      return false;
    }
    console.log(`✅ Node.js版本: ${nodeVersion}`);

    return true;
  }

  private async generateSelfSignedCert(): Promise<void> {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);

    if (!fs.existsSync(this.config.sslCertDir)) {
      fs.mkdirSync(this.config.sslCertDir, { recursive: true });
    }

    const keyPath = path.join(this.config.sslCertDir, 'server.key');
    const certPath = path.join(this.config.sslCertDir, 'server.crt');

    try {
      const command = `openssl req -x509 -newkey rsa:4096 -keyout "${keyPath}" -out "${certPath}" -days 365 -nodes -subj "/C=CN/ST=State/L=City/O=UNM-API/CN=localhost"`;
      await execAsync(command);
      console.log('✅ 自签名SSL证书生成成功');
    } catch (error) {
      console.error('❌ SSL证书生成失败:', error);
      throw error;
    }
  }

  private setupEnvironment(): void {
    console.log('🔧 设置生产环境变量...');

    // 复制生产环境配置
    if (fs.existsSync('.env')) {
      fs.copyFileSync('.env', '.env.backup');
      console.log('✅ 备份当前环境配置');
    }

    fs.copyFileSync(this.config.envFile, '.env');
    console.log('✅ 应用生产环境配置');

    // 设置额外的生产环境变量
    process.env.NODE_ENV = 'production';
    process.env.UV_THREADPOOL_SIZE = '128'; // 增加线程池大小
    process.env.NODE_OPTIONS = '--max-old-space-size=2048'; // 增加内存限制
  }

  private async performHealthCheck(): Promise<boolean> {
    console.log('🏥 执行健康检查...');

    const http = require('http');
    const port = process.env.PORT || 5678;

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log('⚠️  健康检查超时');
        resolve(false);
      }, 10000);

      const req = http.get(`http://localhost:${port}/health`, (res: any) => {
        clearTimeout(timeout);
        if (res.statusCode === 200) {
          console.log('✅ 健康检查通过');
          resolve(true);
        } else {
          console.log(`⚠️  健康检查失败: HTTP ${res.statusCode}`);
          resolve(false);
        }
      });

      req.on('error', () => {
        clearTimeout(timeout);
        console.log('⚠️  健康检查连接失败');
        resolve(false);
      });
    });
  }

  private startServer(): Promise<void> {
    console.log('🚀 启动生产服务器...');

    return new Promise((resolve, reject) => {
      // 使用tsx直接运行TypeScript
      const serverProcess = spawn('npx', ['tsx', 'src/server.ts'], {
        stdio: ['inherit', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      // 保存进程ID
      fs.writeFileSync(this.config.pidFile, serverProcess.pid?.toString() || '');

      let serverStarted = false;

      serverProcess.stdout?.on('data', (data) => {
        const output = data.toString();
        console.log(output);

        // 检测服务器启动成功
        if (output.includes('Server running on port') && !serverStarted) {
          serverStarted = true;
          console.log('✅ 服务器启动成功');
          resolve();
        }
      });

      serverProcess.stderr?.on('data', (data) => {
        const error = data.toString();
        console.error('服务器错误:', error);
      });

      serverProcess.on('error', (error) => {
        console.error('❌ 服务器启动失败:', error);
        reject(error);
      });

      serverProcess.on('exit', (code) => {
        console.log(`服务器进程退出，代码: ${code}`);
        if (fs.existsSync(this.config.pidFile)) {
          fs.unlinkSync(this.config.pidFile);
        }
      });

      // 处理进程信号
      process.on('SIGINT', () => {
        console.log('\n🛑 收到停止信号，正在关闭服务器...');
        serverProcess.kill('SIGTERM');
        if (fs.existsSync(this.config.pidFile)) {
          fs.unlinkSync(this.config.pidFile);
        }
        process.exit(0);
      });

      process.on('SIGTERM', () => {
        console.log('\n🛑 收到终止信号，正在关闭服务器...');
        serverProcess.kill('SIGTERM');
        if (fs.existsSync(this.config.pidFile)) {
          fs.unlinkSync(this.config.pidFile);
        }
        process.exit(0);
      });

      // 超时检查
      setTimeout(() => {
        if (!serverStarted) {
          console.error('❌ 服务器启动超时');
          serverProcess.kill('SIGTERM');
          reject(new Error('Server start timeout'));
        }
      }, 30000);
    });
  }

  private displayProductionInfo(): void {
    const port = process.env.PORT || 5678;
    const httpsPort = process.env.HTTPS_PORT || 443;
    const enableHttps = process.env.ENABLE_HTTPS === 'true';

    console.log('\n' + '='.repeat(60));
    console.log('🎉 UNM API Server 生产环境启动完成！');
    console.log('='.repeat(60));
    console.log(`📡 HTTP服务器: http://localhost:${port}`);
    if (enableHttps) {
      console.log(`🔒 HTTPS服务器: https://localhost:${httpsPort}`);
    }
    console.log(`🌍 环境: ${process.env.NODE_ENV}`);
    console.log(`📋 健康检查: http://localhost:${port}/health`);
    console.log(`🎵 音乐API: http://localhost:${port}/api/v1/search`);
    console.log(`📚 API文档: http://localhost:${port}/info`);
    console.log(`📊 进程ID: ${fs.readFileSync(this.config.pidFile, 'utf-8')}`);
    console.log(`📝 日志目录: ${path.resolve(this.config.logDir)}`);
    console.log('='.repeat(60));
    console.log('💡 使用 Ctrl+C 停止服务器');
    console.log('💡 使用 npm run stop-production 停止后台服务');
    console.log('='.repeat(60) + '\n');
  }

  async start(): Promise<void> {
    try {
      console.log('🚀 UNM API Server 生产环境启动器');
      console.log('='.repeat(50) + '\n');

      // 检查前置条件
      const prerequisitesOk = await this.checkPrerequisites();
      if (!prerequisitesOk) {
        process.exit(1);
      }

      // 设置环境
      this.setupEnvironment();

      // 启动服务器
      await this.startServer();

      // 等待服务器完全启动
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 健康检查
      const healthOk = await this.performHealthCheck();
      if (!healthOk) {
        console.log('⚠️  健康检查失败，但服务器可能仍在启动中');
      }

      // 显示生产环境信息
      this.displayProductionInfo();

    } catch (error) {
      console.error('❌ 生产环境启动失败:', error);
      process.exit(1);
    }
  }
}

// 运行生产环境启动器
async function main() {
  const starter = new ProductionStarter();
  await starter.start();
}

if (require.main === module) {
  main().catch(console.error);
}

export { ProductionStarter };
