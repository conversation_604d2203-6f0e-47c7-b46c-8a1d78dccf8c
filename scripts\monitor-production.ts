#!/usr/bin/env tsx

/**
 * 生产环境监控脚本
 * 监控服务器状态、性能指标和健康状况
 */

import * as fs from 'fs';
import * as os from 'os';
import * as http from 'http';
import { SystemMonitor } from '../src/utils/systemMonitor';

// 使用SystemMonitor中的接口
import { SystemMetrics as SystemMonitorMetrics } from '../src/utils/systemMonitor';

interface ServerMetrics {
  status: 'running' | 'stopped' | 'error';
  responseTime: number;
  uptime: number;
  pid?: number;
}

class ProductionMonitor {
  private pidFile = 'server.pid';
  private logFile = 'logs/monitor.log';
  private metricsFile = 'logs/metrics.json';
  private port = process.env.PORT || 5678;
  private systemMonitor: SystemMonitor;

  constructor() {
    // 确保日志目录存在
    if (!fs.existsSync('logs')) {
      fs.mkdirSync('logs', { recursive: true });
    }

    // 初始化系统监控器
    this.systemMonitor = SystemMonitor.getInstance();
  }

  private async getSystemMetrics(): Promise<SystemMonitorMetrics> {
    // 使用真实的系统监控数据
    return await this.systemMonitor.getSystemMetrics();
  }
  //  
  private async getServerMetrics(): Promise<ServerMetrics> {
    const startTime = Date.now();
    
    // 检查进程是否运行
    let pid: number | undefined;
    let isRunning = false;

    if (fs.existsSync(this.pidFile)) {
      try {
        pid = parseInt(fs.readFileSync(this.pidFile, 'utf-8').trim());
        process.kill(pid, 0); // 检查进程是否存在
        isRunning = true;
      } catch (error) {
        isRunning = false;
      }
    }

    if (!isRunning) {
      return {
        status: 'stopped',
        responseTime: 0,
        uptime: 0,
        pid
      };
    }

    // 检查HTTP响应
    try {
      const responseTime = await this.checkHttpResponse();
      const uptime = process.uptime();

      return {
        status: 'running',
        responseTime,
        uptime,
        pid
      };
    } catch (error) {
      return {
        status: 'error',
        responseTime: 0,
        uptime: process.uptime(),
        pid
      };
    }
  }

  private async checkHttpResponse(): Promise<number> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      // 使用环境变量或配置文件中的主机名，而不是硬编码localhost
      const host = process.env.SERVER_HOST || 'localhost';
      const url = `http://${host}:${this.port}/health`;

      const req = http.get(url, (res) => {
        const responseTime = Date.now() - startTime;

        if (res.statusCode === 200) {
          resolve(responseTime);
        } else {
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(5000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  private logMetrics(systemMetrics: SystemMonitorMetrics, serverMetrics: ServerMetrics): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      system: systemMetrics,
      server: serverMetrics
    };

    // 写入日志文件 - 包含更详细的磁盘和网络信息
    const diskInfo = systemMetrics.disk.length > 0 ?
      `Disk: ${systemMetrics.disk[0].usage.toFixed(1)}%` : 'Disk: N/A';
    const networkInfo = `Network: ${systemMetrics.network.total} connections`;

    const logLine = `${logEntry.timestamp} - Server: ${serverMetrics.status}, CPU: ${systemMetrics.cpu.usage.toFixed(1)}%, Memory: ${systemMetrics.memory.usage.toFixed(1)}%, ${diskInfo}, ${networkInfo}, Response: ${serverMetrics.responseTime}ms\n`;
    fs.appendFileSync(this.logFile, logLine);

    // 写入指标文件
    let metrics: Array<{
      timestamp: string;
      system: SystemMonitorMetrics;
      server: ServerMetrics;
    }> = [];
    if (fs.existsSync(this.metricsFile)) {
      try {
        const data = fs.readFileSync(this.metricsFile, 'utf-8');
        metrics = JSON.parse(data);
      } catch (error) {
        metrics = [];
      }
    }
    metrics.push(logEntry);
    // 只保留最近1000条记录
    if (metrics.length > 1000) {
      metrics = metrics.slice(-1000);
    }

    fs.writeFileSync(this.metricsFile, JSON.stringify(metrics, null, 2));
  }

  private displayMetrics(systemMetrics: SystemMonitorMetrics, serverMetrics: ServerMetrics): void {
    console.clear();
    console.log('📊 UNM API Server 生产环境监控');
    console.log('='.repeat(60));
    console.log(`⏰ 时间: ${new Date().toLocaleString('zh-CN')}`);
    console.log('');

    // 服务器状态
    const statusIcon = serverMetrics.status === 'running' ? '🟢' : 
                      serverMetrics.status === 'stopped' ? '🔴' : '🟡';
    console.log(`${statusIcon} 服务器状态: ${serverMetrics.status.toUpperCase()}`);
    
    if (serverMetrics.pid) {
      console.log(`🆔 进程ID: ${serverMetrics.pid}`);
    }
    
    if (serverMetrics.status === 'running') {
      console.log(`⚡ 响应时间: ${serverMetrics.responseTime}ms`);
      console.log(`⏱️  运行时间: ${Math.floor(serverMetrics.uptime / 3600)}h ${Math.floor((serverMetrics.uptime % 3600) / 60)}m`);
    }

    console.log('');

    // 系统指标
    console.log('💻 系统指标:');
    console.log(`   CPU使用率: ${systemMetrics.cpu.usage.toFixed(1)}%`);
    console.log(`   负载平均: ${systemMetrics.cpu.loadAverage.map(l => l.toFixed(2)).join(', ')}`);
    console.log(`   内存使用: ${systemMetrics.memory.usage.toFixed(1)}% (${Math.round(systemMetrics.memory.used / 1024 / 1024)}MB / ${Math.round(systemMetrics.memory.total / 1024 / 1024)}MB)`);

    // 显示所有磁盘的使用情况
    if (systemMetrics.disk.length > 0) {
      systemMetrics.disk.forEach((disk, index) => {
        const prefix = index === 0 ? '   磁盘使用:' : '           ';
        console.log(`${prefix} ${disk.mountpoint}: ${disk.usage.toFixed(1)}% (${Math.round(disk.used / 1024 / 1024 / 1024)}GB / ${Math.round(disk.total / 1024 / 1024 / 1024)}GB)`);
      });
    } else {
      console.log('   磁盘使用: 无法获取');
    }

    console.log(`   网络连接: ${systemMetrics.network.total} (TCP: ${systemMetrics.network.tcp}, UDP: ${systemMetrics.network.udp})`);

    console.log('');

    // 警告信息
    const warnings: string[] = [];
    if (systemMetrics.cpu.usage > 80) warnings.push('⚠️  CPU使用率过高');
    if (systemMetrics.memory.usage > 85) warnings.push('⚠️  内存使用率过高');

    // 检查所有磁盘的使用率
    systemMetrics.disk.forEach(disk => {
      if (disk.usage > 90) warnings.push(`⚠️  磁盘 ${disk.mountpoint} 使用率过高`);
    });

    if (serverMetrics.responseTime > 1000) warnings.push('⚠️  响应时间过长');
    if (serverMetrics.status !== 'running') warnings.push('⚠️  服务器未运行');

    if (warnings.length > 0) {
      console.log('🚨 警告:');
      warnings.forEach(warning => console.log(`   ${warning}`));
      console.log('');
    }

    console.log('='.repeat(60));
    console.log('💡 按 Ctrl+C 停止监控');
    console.log(`📝 日志文件: ${this.logFile}`);
    console.log(`📊 指标文件: ${this.metricsFile}`);
  }

  async startMonitoring(interval: number = 5000): Promise<void> {
    console.log('🚀 启动生产环境监控...\n');

    const monitor = async () => {
      try {
        const systemMetrics = await this.getSystemMetrics();
        const serverMetrics = await this.getServerMetrics();

        this.logMetrics(systemMetrics, serverMetrics);
        this.displayMetrics(systemMetrics, serverMetrics);

      } catch (error) {
        console.error('❌ 监控错误:', error);
      }
    };

    // 立即执行一次
    await monitor();

    // 设置定时监控
    const intervalId = setInterval(monitor, interval);

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 停止监控...');
      clearInterval(intervalId);
      process.exit(0);
    });
  }

  async generateReport(): Promise<void> {
    console.log('📊 生成监控报告...');

    if (!fs.existsSync(this.metricsFile)) {
      console.log('❌ 没有找到监控数据');
      return;
    }

    try {
      const data = fs.readFileSync(this.metricsFile, 'utf-8');
      const metrics = JSON.parse(data);

      if (metrics.length === 0) {
        console.log('❌ 监控数据为空');
        return;
      }

      // 计算统计信息
      const cpuUsages = metrics.map((m: any) => m.system.cpu.usage);
      const memoryUsages = metrics.map((m: any) => m.system.memory.usage);
      const responseTimes = metrics.filter((m: any) => m.server.status === 'running').map((m: any) => m.server.responseTime);

      const report = {
        period: {
          start: metrics[0].timestamp,
          end: metrics[metrics.length - 1].timestamp,
          duration: `${Math.round((new Date(metrics[metrics.length - 1].timestamp).getTime() - new Date(metrics[0].timestamp).getTime()) / 1000 / 60)} 分钟`
        },
        cpu: {
          average: cpuUsages.reduce((a: number, b: number) => a + b, 0) / cpuUsages.length,
          max: Math.max(...cpuUsages),
          min: Math.min(...cpuUsages)
        },
        memory: {
          average: memoryUsages.reduce((a: number, b: number) => a + b, 0) / memoryUsages.length,
          max: Math.max(...memoryUsages),
          min: Math.min(...memoryUsages)
        },
        response: {
          average: responseTimes.length > 0 ? responseTimes.reduce((a: number, b: number) => a + b, 0) / responseTimes.length : 0,
          max: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
          min: responseTimes.length > 0 ? Math.min(...responseTimes) : 0
        },
        uptime: metrics.filter((m: any) => m.server.status === 'running').length / metrics.length * 100
      };

      console.log('\n📊 监控报告');
      console.log('='.repeat(50));
      console.log(`📅 监控周期: ${report.period.start} 至 ${report.period.end}`);
      console.log(`⏱️  监控时长: ${report.period.duration}`);
      console.log(`💻 CPU使用率: 平均 ${report.cpu.average.toFixed(1)}%, 最高 ${report.cpu.max.toFixed(1)}%, 最低 ${report.cpu.min.toFixed(1)}%`);
      console.log(`🧠 内存使用率: 平均 ${report.memory.average.toFixed(1)}%, 最高 ${report.memory.max.toFixed(1)}%, 最低 ${report.memory.min.toFixed(1)}%`);
      console.log(`⚡ 响应时间: 平均 ${report.response.average.toFixed(0)}ms, 最长 ${report.response.max}ms, 最短 ${report.response.min}ms`);
      console.log(`🟢 服务可用性: ${report.uptime.toFixed(1)}%`);
      console.log('='.repeat(50));

      // 保存报告
      const reportFile = `logs/report-${new Date().toISOString().split('T')[0]}.json`;
      fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
      console.log(`📄 报告已保存: ${reportFile}`);

    } catch (error) {
      console.error('❌ 生成报告失败:', error);
    }
  }
}

// 命令行参数处理
async function main() {
  const monitor = new ProductionMonitor();
  const command = process.argv[2];

  switch (command) {
    case 'start':
      await monitor.startMonitoring();
      break;
    case 'report':
      await monitor.generateReport();
      break;
    default:
      console.log('使用方法:');
      console.log('  npm run monitor-production start  - 开始监控');
      console.log('  npm run monitor-production report - 生成报告');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { ProductionMonitor };
