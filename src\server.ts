import { createServer } from 'http';
import app from './app';
import { config } from './config/environment';
import { logger } from './utils/logger';
import { createHTTPSServer } from './utils/httpsServer';

// 创建HTTP服务器
const server = createServer(app);

// 启动服务器
const startServer = async () => {
  const port = config.port;
  const host = process.env.SERVER_HOST || 'localhost';

  // 启动HTTP服务器
  server.listen(port, () => {
    logger.info(`🚀 ${config.serverName} started successfully`);
    logger.info(`📡 HTTP Server running on port ${port}`);
    logger.info(`🌍 Environment: ${config.nodeEnv}`);
    logger.info(`🔗 Health check: http://${host}:${port}/health`);
    logger.info(`🎵 Music API: http://${host}:${port}${config.apiBasePath}/${config.apiVersion}/search`);
    logger.info(`📚 API Documentation: http://${host}:${port}/info`);
  });

  // 启动HTTPS服务器（如果启用）
  try {
    const httpsServer = await createHTTPSServer(app);
    if (httpsServer) {
      logger.info('✅ HTTPS server started successfully');
    }
  } catch (error) {
    logger.error('Failed to start HTTPS server', { error });
  }
};

// 优雅关闭处理
const gracefulShutdown = (signal: string) => {
  logger.info(`${signal} received, shutting down gracefully`);
  
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });
  
  // 强制关闭超时
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// 监听关闭信号
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 启动服务器
startServer().catch((error) => {
  logger.error('Failed to start server', { error });
  process.exit(1);
});

export default server;
