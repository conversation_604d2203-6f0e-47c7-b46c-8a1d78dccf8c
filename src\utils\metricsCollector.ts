import { logger } from './logger';

/**
 * 指标类型枚举
 */
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  TIMER = 'timer'
}

/**
 * 指标数据接口
 */
export interface MetricData {
  name: string;
  type: MetricType;
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
}

/**
 * API调用指标
 */
export interface ApiCallMetric {
  source: string;
  endpoint: string;
  method: string;
  statusCode: number;
  duration: number;
  success: boolean;
  timestamp: number;
  userAgent?: string;
  ip?: string;
}

/**
 * 聚合指标
 */
export interface AggregatedMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  successRate: number;
  requestsPerMinute: number;
  errorRate: number;
  lastUpdated: number;
}

/**
 * 端点统计信息
 */
export interface EndpointStats {
  endpoint: string;
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  successRate: number;
  lastCall: number;
  responseTimes: number[];
  errorCounts: Record<string, number>;
}

/**
 * 系统指标
 */
export interface SystemMetrics {
  uptime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  activeConnections: number;
  totalConnections: number;
  timestamp: number;
}

/**
 * 业务指标收集器
 */
export class MetricsCollector {
  private metrics: Map<string, MetricData[]> = new Map();
  private apiCallMetrics: ApiCallMetric[] = [];
  private endpointStats: Map<string, EndpointStats> = new Map();
  private systemMetrics: SystemMetrics[] = [];
  private startTime: number = Date.now();
  
  // 配置参数
  private readonly MAX_METRICS_HISTORY = 1000;
  private readonly MAX_API_CALL_HISTORY = 5000;
  private readonly MAX_SYSTEM_METRICS_HISTORY = 100;
  private readonly RESPONSE_TIME_HISTORY_SIZE = 100;

  constructor() {
    // 定期清理旧数据
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 300000); // 5分钟清理一次

    // 定期收集系统指标
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // 30秒收集一次

    logger.info('Metrics collector initialized');
  }

  /**
   * 记录API调用指标
   */
  recordApiCall(metric: Omit<ApiCallMetric, 'timestamp'>): void {
    const apiCallMetric: ApiCallMetric = {
      ...metric,
      timestamp: Date.now(),
    };

    // 添加到API调用历史
    this.apiCallMetrics.push(apiCallMetric);
    if (this.apiCallMetrics.length > this.MAX_API_CALL_HISTORY) {
      this.apiCallMetrics.shift();
    }

    // 更新端点统计
    this.updateEndpointStats(apiCallMetric);

    // 记录通用指标
    this.recordMetric('api_calls_total', MetricType.COUNTER, 1, {
      source: metric.source,
      endpoint: metric.endpoint,
      method: metric.method,
      status: metric.statusCode.toString(),
    });

    this.recordMetric('api_response_time', MetricType.HISTOGRAM, metric.duration, {
      source: metric.source,
      endpoint: metric.endpoint,
    });

    if (!metric.success) {
      this.recordMetric('api_errors_total', MetricType.COUNTER, 1, {
        source: metric.source,
        endpoint: metric.endpoint,
        status: metric.statusCode.toString(),
      });
    }

    logger.debug('API call metric recorded', {
      source: metric.source,
      endpoint: metric.endpoint,
      duration: metric.duration,
      success: metric.success,
    });
  }

  /**
   * 记录通用指标
   */
  recordMetric(name: string, type: MetricType, value: number, labels?: Record<string, string>): void {
    const metric: MetricData = {
      name,
      type,
      value,
      timestamp: Date.now(),
      ...(labels && { labels }),
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricHistory = this.metrics.get(name)!;
    metricHistory.push(metric);

    // 限制历史记录大小
    if (metricHistory.length > this.MAX_METRICS_HISTORY) {
      metricHistory.shift();
    }
  }

  /**
   * 更新端点统计信息
   */
  private updateEndpointStats(metric: ApiCallMetric): void {
    const key = `${metric.source}_${metric.endpoint}`;
    
    if (!this.endpointStats.has(key)) {
      this.endpointStats.set(key, {
        endpoint: key,
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageResponseTime: 0,
        minResponseTime: Infinity,
        maxResponseTime: 0,
        successRate: 0,
        lastCall: 0,
        responseTimes: [],
        errorCounts: {},
      });
    }

    const stats = this.endpointStats.get(key)!;
    
    stats.totalCalls++;
    stats.lastCall = metric.timestamp;
    
    if (metric.success) {
      stats.successfulCalls++;
    } else {
      stats.failedCalls++;
      const errorKey = metric.statusCode.toString();
      stats.errorCounts[errorKey] = (stats.errorCounts[errorKey] || 0) + 1;
    }

    // 更新响应时间统计
    stats.responseTimes.push(metric.duration);
    if (stats.responseTimes.length > this.RESPONSE_TIME_HISTORY_SIZE) {
      stats.responseTimes.shift();
    }

    stats.minResponseTime = Math.min(stats.minResponseTime, metric.duration);
    stats.maxResponseTime = Math.max(stats.maxResponseTime, metric.duration);
    stats.averageResponseTime = stats.responseTimes.reduce((sum, time) => sum + time, 0) / stats.responseTimes.length;
    stats.successRate = stats.successfulCalls / stats.totalCalls;
  }

  /**
   * 收集系统指标
   */
  private collectSystemMetrics(): void {
    const systemMetric: SystemMetrics = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      activeConnections: 0, // 需要从服务器获取
      totalConnections: 0,  // 需要从服务器获取
      timestamp: Date.now(),
    };

    this.systemMetrics.push(systemMetric);
    if (this.systemMetrics.length > this.MAX_SYSTEM_METRICS_HISTORY) {
      this.systemMetrics.shift();
    }

    // 记录系统指标
    this.recordMetric('system_uptime', MetricType.GAUGE, systemMetric.uptime);
    this.recordMetric('system_memory_used', MetricType.GAUGE, systemMetric.memoryUsage.heapUsed);
    this.recordMetric('system_memory_total', MetricType.GAUGE, systemMetric.memoryUsage.heapTotal);
  }

  /**
   * 获取聚合指标
   */
  getAggregatedMetrics(timeWindow?: number): AggregatedMetrics {
    const now = Date.now();
    const windowStart = timeWindow ? now - timeWindow : this.startTime;
    
    const recentApiCalls = this.apiCallMetrics.filter(metric => metric.timestamp >= windowStart);
    
    if (recentApiCalls.length === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        successRate: 0,
        requestsPerMinute: 0,
        errorRate: 0,
        lastUpdated: now,
      };
    }

    const successfulRequests = recentApiCalls.filter(call => call.success).length;
    const failedRequests = recentApiCalls.length - successfulRequests;
    const responseTimes = recentApiCalls.map(call => call.duration);
    const timeWindowMinutes = (now - windowStart) / 60000;

    return {
      totalRequests: recentApiCalls.length,
      successfulRequests,
      failedRequests,
      averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      successRate: successfulRequests / recentApiCalls.length,
      requestsPerMinute: recentApiCalls.length / Math.max(timeWindowMinutes, 1),
      errorRate: failedRequests / recentApiCalls.length,
      lastUpdated: now,
    };
  }

  /**
   * 获取端点统计信息
   */
  getEndpointStats(): EndpointStats[] {
    return Array.from(this.endpointStats.values());
  }

  /**
   * 获取系统指标
   */
  getSystemMetrics(): SystemMetrics | null {
    const lastMetric = this.systemMetrics[this.systemMetrics.length - 1];
    return lastMetric || null;
  }

  /**
   * 获取指标历史
   */
  getMetricHistory(name: string, limit?: number): MetricData[] {
    const history = this.metrics.get(name) || [];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * 获取所有指标摘要
   */
  getMetricsSummary(): any {
    const aggregated = this.getAggregatedMetrics();
    const endpointStats = this.getEndpointStats();
    const systemMetrics = this.getSystemMetrics();

    return {
      timestamp: Date.now(),
      uptime: process.uptime(),
      aggregated,
      endpoints: endpointStats,
      system: systemMetrics,
      cache: {
        totalMetrics: Array.from(this.metrics.values()).reduce((sum, arr) => sum + arr.length, 0),
        totalApiCalls: this.apiCallMetrics.length,
        totalEndpoints: this.endpointStats.size,
      },
    };
  }

  /**
   * 重置所有指标
   */
  reset(): void {
    this.metrics.clear();
    this.apiCallMetrics.length = 0;
    this.endpointStats.clear();
    this.systemMetrics.length = 0;
    this.startTime = Date.now();
    
    logger.info('Metrics collector reset');
  }

  /**
   * 清理旧指标数据
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - 3600000; // 1小时前
    
    // 清理API调用指标
    this.apiCallMetrics = this.apiCallMetrics.filter(metric => metric.timestamp > cutoffTime);
    
    // 清理通用指标
    for (const [name, metricHistory] of this.metrics.entries()) {
      const filteredHistory = metricHistory.filter(metric => metric.timestamp > cutoffTime);
      if (filteredHistory.length === 0) {
        this.metrics.delete(name);
      } else {
        this.metrics.set(name, filteredHistory);
      }
    }
    
    logger.debug('Old metrics cleaned up', {
      cutoffTime: new Date(cutoffTime).toISOString(),
      remainingApiCalls: this.apiCallMetrics.length,
      remainingMetrics: this.metrics.size,
    });
  }
}

// 创建全局指标收集器实例
export const metricsCollector = new MetricsCollector();
