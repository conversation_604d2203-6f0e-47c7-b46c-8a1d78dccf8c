import NodeCache from 'node-cache';
import { Redis } from 'ioredis';
import { logger } from '../utils/logger';

/**
 * 缓存统计信息
 */
export interface CacheStats {
  l1Hits: number;
  l2Hits: number;
  misses: number;
  sets: number;
  deletes: number;
  totalRequests: number;
  hitRate: number;
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  l1TTL: number;
  l2TTL: number;
  maxKeys: number;
  checkPeriod: number;
  redisEnabled: boolean;
  redisConfig?: {
    host: string;
    port: number;
    password?: string | undefined;
    db?: number | undefined;
  };
}

/**
 * 增强的多层缓存服务
 * L1: 内存缓存 (NodeCache)
 * L2: Redis缓存 (可选)
 */
export class EnhancedCacheService {
  private memoryCache: NodeCache;
  private redisClient?: Redis;
  private stats: CacheStats;
  private config: CacheConfig;

  constructor(cacheConfig?: Partial<CacheConfig>) {
    const baseConfig = {
      l1TTL: parseInt(process.env.CACHE_L1_TTL || '300'),
      l2TTL: parseInt(process.env.CACHE_L2_TTL || '1800'),
      maxKeys: parseInt(process.env.CACHE_MAX_SIZE || '1000'),
      checkPeriod: 60,
      redisEnabled: process.env.REDIS_ENABLED === 'true'
    };

    const redisConfig = process.env.REDIS_ENABLED === 'true' ? {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0')
    } : undefined;

    this.config = {
      ...baseConfig,
      ...(redisConfig && { redisConfig }),
      ...cacheConfig
    };

    // 初始化统计信息
    this.stats = {
      l1Hits: 0,
      l2Hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      totalRequests: 0,
      hitRate: 0
    };

    // 初始化L1内存缓存
    this.memoryCache = new NodeCache({
      stdTTL: this.config.l1TTL,
      checkperiod: this.config.checkPeriod,
      useClones: false,
      maxKeys: this.config.maxKeys
    });

    // 初始化L2 Redis缓存
    if (this.config.redisEnabled && this.config.redisConfig) {
      try {
        const redisOptions: any = {
          host: this.config.redisConfig.host,
          port: this.config.redisConfig.port,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
          connectTimeout: 10000
        };

        if (this.config.redisConfig.password) {
          redisOptions.password = this.config.redisConfig.password;
        }

        if (this.config.redisConfig.db !== undefined) {
          redisOptions.db = this.config.redisConfig.db;
        }

        this.redisClient = new Redis(redisOptions);

        this.redisClient.on('connect', () => {
          logger.info('Redis cache connected', {
            host: this.config.redisConfig?.host,
            port: this.config.redisConfig?.port
          });
        });

        this.redisClient.on('error', (error) => {
          logger.error('Redis cache error', { error: error.message });
        });

        this.redisClient.on('close', () => {
          logger.warn('Redis cache connection closed');
        });

      } catch (error) {
        logger.error('Failed to initialize Redis cache', { error });
        this.redisClient = undefined as any;
      }
    }

    // 设置内存缓存事件监听
    this.memoryCache.on('set', (key, value) => {
      logger.debug('L1 cache set', { key, size: JSON.stringify(value).length });
    });

    this.memoryCache.on('del', (key, _value) => {
      logger.debug('L1 cache delete', { key });
    });

    this.memoryCache.on('expired', (key, _value) => {
      logger.debug('L1 cache expired', { key });
    });

    logger.info('Enhanced cache service initialized', {
      l1TTL: this.config.l1TTL,
      l2TTL: this.config.l2TTL,
      maxKeys: this.config.maxKeys,
      redisEnabled: this.config.redisEnabled
    });
  }

  /**
   * 获取缓存数据
   */
  async get<T>(key: string): Promise<T | null> {
    this.stats.totalRequests++;

    try {
      // L1: 内存缓存查找
      const memoryResult = this.memoryCache.get<T>(key);
      if (memoryResult !== undefined) {
        this.stats.l1Hits++;
        this.updateHitRate();
        logger.debug('Cache hit (L1)', { key });
        return memoryResult;
      }

      // L2: Redis缓存查找
      if (this.redisClient) {
        try {
          const redisResult = await this.redisClient.get(key);
          if (redisResult !== null) {
            const parsed = JSON.parse(redisResult) as T;
            
            // 将数据回填到L1缓存
            this.memoryCache.set(key, parsed, Math.min(this.config.l1TTL, 60));
            
            this.stats.l2Hits++;
            this.updateHitRate();
            logger.debug('Cache hit (L2)', { key });
            return parsed;
          }
        } catch (redisError) {
          logger.warn('Redis cache get error', { key, error: redisError });
        }
      }

      // 缓存未命中
      this.stats.misses++;
      this.updateHitRate();
      logger.debug('Cache miss', { key });
      return null;

    } catch (error) {
      logger.error('Cache get error', { key, error });
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    this.stats.sets++;

    try {
      const l1TTL = ttl || this.config.l1TTL;
      const l2TTL = ttl || this.config.l2TTL;

      // 设置L1内存缓存
      this.memoryCache.set(key, value, l1TTL);

      // 设置L2 Redis缓存
      if (this.redisClient) {
        try {
          await this.redisClient.setex(key, l2TTL, JSON.stringify(value));
          logger.debug('Cache set (L1+L2)', { key, l1TTL, l2TTL });
        } catch (redisError) {
          logger.warn('Redis cache set error', { key, error: redisError });
          logger.debug('Cache set (L1 only)', { key, l1TTL });
        }
      } else {
        logger.debug('Cache set (L1 only)', { key, l1TTL });
      }

    } catch (error) {
      logger.error('Cache set error', { key, error });
    }
  }

  /**
   * 删除缓存数据
   */
  async del(key: string): Promise<void> {
    this.stats.deletes++;

    try {
      // 删除L1缓存
      this.memoryCache.del(key);

      // 删除L2缓存
      if (this.redisClient) {
        try {
          await this.redisClient.del(key);
          logger.debug('Cache delete (L1+L2)', { key });
        } catch (redisError) {
          logger.warn('Redis cache delete error', { key, error: redisError });
          logger.debug('Cache delete (L1 only)', { key });
        }
      } else {
        logger.debug('Cache delete (L1 only)', { key });
      }

    } catch (error) {
      logger.error('Cache delete error', { key, error });
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    try {
      // 清空L1缓存
      this.memoryCache.flushAll();

      // 清空L2缓存
      if (this.redisClient) {
        try {
          await this.redisClient.flushdb();
          logger.info('Cache cleared (L1+L2)');
        } catch (redisError) {
          logger.warn('Redis cache clear error', { error: redisError });
          logger.info('Cache cleared (L1 only)');
        }
      } else {
        logger.info('Cache cleared (L1 only)');
      }

      // 重置统计信息
      this.resetStats();

    } catch (error) {
      logger.error('Cache clear error', { error });
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      l1Hits: 0,
      l2Hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      totalRequests: 0,
      hitRate: 0
    };
  }

  /**
   * 获取缓存键列表
   */
  getKeys(): string[] {
    return this.memoryCache.keys();
  }

  /**
   * 获取缓存大小信息
   */
  getSize(): { l1Keys: number; l1Size: number } {
    const keys = this.memoryCache.keys();
    let totalSize = 0;

    keys.forEach(key => {
      const value = this.memoryCache.get(key);
      if (value !== undefined) {
        totalSize += JSON.stringify(value).length;
      }
    });

    return {
      l1Keys: keys.length,
      l1Size: totalSize
    };
  }

  /**
   * 检查缓存健康状态
   */
  async healthCheck(): Promise<{ l1: boolean; l2: boolean }> {
    const l1Health = this.memoryCache !== undefined;
    let l2Health = false;

    if (this.redisClient) {
      try {
        await this.redisClient.ping();
        l2Health = true;
      } catch (error) {
        logger.warn('Redis health check failed', { error });
      }
    }

    return { l1: l1Health, l2: l2Health };
  }

  /**
   * 更新命中率
   */
  private updateHitRate(): void {
    if (this.stats.totalRequests > 0) {
      const totalHits = this.stats.l1Hits + this.stats.l2Hits;
      this.stats.hitRate = Number((totalHits / this.stats.totalRequests).toFixed(4));
    }
  }

  /**
   * 关闭缓存服务
   */
  async close(): Promise<void> {
    try {
      this.memoryCache.close();
      
      if (this.redisClient) {
        await this.redisClient.quit();
      }
      
      logger.info('Enhanced cache service closed');
    } catch (error) {
      logger.error('Error closing cache service', { error });
    }
  }
}

// 创建全局缓存服务实例
export const enhancedCacheService = new EnhancedCacheService();
