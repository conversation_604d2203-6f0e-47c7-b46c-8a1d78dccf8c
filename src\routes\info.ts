import { Router, Request, Response } from 'express';
import { createSuccessResponse } from './../utils/response';
import { logger } from './../utils/logger';
import { ServerInfo } from './../types/api';
import { getAllSources, getStableSources } from './../config/sources';
import { cacheService } from '../services/cacheService';
import { config } from '../config/environment';

const router = Router();

/**
 * 服务器信息端点
 * GET /info
 */
router.get('/info', (_req: Request, res: Response) => {
  try {
    const cacheStats = cacheService.getStats();

    const serverInfo: ServerInfo = {
      name: config.serverName,
      version: config.serverVersion,
      author: config.serverAuthor,
      description: config.serverDescription,
      supported_sources: config.supportedSources,
      stable_sources: config.stableSources,
      cache_enabled: config.enableCache,
      cache_stats: cacheStats,
    };

    logger.debug('Server info requested', serverInfo);

    const response = createSuccessResponse(serverInfo);
    res.json(response);
  } catch (error) {
    logger.error('Get server info failed:', error);
    res.status(500).json(createSuccessResponse(null, 'Internal server error'));
  }
});

/**
 * 支持的音源列表端点
 * GET /sources
 */
router.get('/sources', (_req: Request, res: Response) => {
  try {
    const sources = {
      stable: getStableSources(),
      all: getAllSources(),
    };

    logger.debug('Sources info requested', sources);
    
    const response = createSuccessResponse(sources);
    res.json(response);
  } catch (error) {
    logger.error('Get sources info failed:', error);
    res.status(500).json(createSuccessResponse(null, 'Internal server error'));
  }
});

export default router;
