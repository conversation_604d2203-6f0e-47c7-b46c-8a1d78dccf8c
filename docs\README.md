# 📚 UNM API Server 文档中心

欢迎来到 UNM API Server 的文档中心！这里包含了项目的完整文档，帮助您快速上手和深入了解项目。

## 🚀 快速开始

如果您是第一次使用，建议按以下顺序阅读文档：

1. **[主README](../README.md)** - 项目概览和快速开始
2. **[API文档](API.md)** - 了解API接口使用方法
3. **[部署指南](DEPLOYMENT.md)** - 部署到生产环境
4. **[开发指南](DEVELOPMENT.md)** - 参与项目开发

## 📖 文档导航

### 🎯 用户文档

#### 新手入门
- **[项目概览](../README.md#特性)** - 了解项目功能和特性
- **[快速安装](../README.md#快速开始)** - 5分钟快速体验
- **[API使用示例](../README.md#api-使用示例)** - 常用API调用示例

#### API参考
- **[API接口文档](API.md)** - 完整的API接口说明
  - [传统兼容API](API.md#传统兼容api) - 向后兼容的API接口
  - [RESTful API](API.md#现代restful-api) - 现代化的API接口
  - [系统API](API.md#系统api) - 健康检查和监控接口
  - [错误码说明](API.md#错误码说明) - 错误处理和调试

#### 部署运维
- **[部署指南](DEPLOYMENT.md)** - 生产环境部署
  - [Docker部署](DEPLOYMENT.md#docker-部署) - 容器化部署方案
  - [传统部署](DEPLOYMENT.md#传统部署) - 直接部署到服务器
  - [反向代理配置](DEPLOYMENT.md#反向代理配置) - Nginx/Apache配置
  - [SSL/TLS配置](DEPLOYMENT.md#ssltls-配置) - HTTPS安全配置
  - [监控和日志](DEPLOYMENT.md#监控和日志) - 运维监控方案

### 🛠️ 开发者文档

#### 开发环境
- **[开发指南](DEVELOPMENT.md)** - 开发环境搭建
  - [环境搭建](DEVELOPMENT.md#开发环境搭建) - 本地开发环境配置
  - [项目结构](DEVELOPMENT.md#项目结构详解) - 代码组织和架构
  - [代码规范](DEVELOPMENT.md#代码规范) - 编码标准和最佳实践
  - [测试指南](DEVELOPMENT.md#测试) - 单元测试和集成测试
  - [贡献流程](DEVELOPMENT.md#贡献流程) - 如何参与项目开发

#### 技术架构
- **[架构文档](ARCHITECTURE.md)** - 技术架构设计
  - [整体架构](ARCHITECTURE.md#整体架构图) - 系统架构概览
  - [分层架构](ARCHITECTURE.md#分层架构详解) - 代码分层设计
  - [数据流架构](ARCHITECTURE.md#数据流架构) - 请求处理流程
  - [性能架构](ARCHITECTURE.md#性能架构) - 缓存和优化策略
  - [扩展性设计](ARCHITECTURE.md#扩展性设计) - 水平和垂直扩展

#### 安全防护
- **[安全指南](SECURITY.md)** - 安全配置和防护
  - [认证授权](SECURITY.md#认证和授权) - API认证机制
  - [访问控制](SECURITY.md#访问控制) - CORS和IP白名单
  - [安全头部](SECURITY.md#安全头部配置) - HTTP安全头部配置
  - [频率限制](SECURITY.md#频率限制) - API访问频率控制
  - [输入验证](SECURITY.md#输入验证和清理) - 参数验证和XSS防护
  - [安全监控](SECURITY.md#安全监控) - 威胁检测和响应

### 🔧 运维文档

#### 故障排除
- **[故障排除指南](TROUBLESHOOTING.md)** - 常见问题解决
  - [常见问题](TROUBLESHOOTING.md#常见问题) - 启动、依赖、API问题
  - [诊断工具](TROUBLESHOOTING.md#诊断工具) - 健康检查和日志分析
  - [性能优化](TROUBLESHOOTING.md#性能优化) - 缓存和连接池优化
  - [紧急处理](TROUBLESHOOTING.md#紧急情况处理) - 紧急故障处理流程

#### 版本管理
- **[更新日志](../CHANGELOG.md)** - 版本更新记录
  - [版本说明](../CHANGELOG.md#版本说明) - 版本号规则和发布周期
  - [变更记录](../CHANGELOG.md#100---2025-01-23) - 详细的变更历史
  - [升级指南](../CHANGELOG.md#贡献指南) - 版本升级注意事项

## 🎯 按角色分类

### 👨‍💼 项目经理 / 产品经理
- [项目概览](../README.md) - 了解项目功能和价值
- [需求文档](../requirements.md) - 业务需求和验收标准
- [任务文档](../tasks.md) - 开发进度和里程碑

### 👨‍💻 后端开发者
- [开发指南](DEVELOPMENT.md) - 开发环境和规范
- [架构文档](ARCHITECTURE.md) - 技术架构和设计
- [API文档](API.md) - 接口设计和实现

### 🔧 运维工程师
- [部署指南](DEPLOYMENT.md) - 部署和配置
- [安全指南](SECURITY.md) - 安全配置和防护
- [故障排除](TROUBLESHOOTING.md) - 运维和故障处理

### 🧪 测试工程师
- [API文档](API.md) - 接口测试用例
- [开发指南](DEVELOPMENT.md#测试) - 测试框架和方法
- [故障排除](TROUBLESHOOTING.md#诊断工具) - 测试和诊断工具

### 🔒 安全工程师
- [安全指南](SECURITY.md) - 全面的安全配置
- [架构文档](ARCHITECTURE.md#安全架构) - 安全架构设计
- [部署指南](DEPLOYMENT.md#ssltls-配置) - 安全部署配置

## 📋 文档使用指南

### 🔍 如何查找信息

1. **使用搜索功能** - 在GitHub中使用搜索功能查找特定内容
2. **查看目录结构** - 每个文档都有详细的目录导航
3. **关注交叉引用** - 文档间有丰富的交叉链接
4. **查看示例代码** - 所有配置都有完整的示例

### 📝 文档约定

- **代码块** - 使用语法高亮的代码块
- **配置示例** - 提供完整可用的配置示例
- **命令行** - 包含完整的命令行操作步骤
- **链接引用** - 相关文档间的交叉引用
- **图表说明** - 使用Mermaid图表说明架构和流程

### 🔄 文档更新

文档与代码同步更新，确保信息的准确性：

- **版本同步** - 文档版本与代码版本保持一致
- **实时更新** - 功能变更时同步更新文档
- **社区贡献** - 欢迎社区贡献文档改进

## 🤝 贡献文档

我们欢迎您为文档做出贡献：

### 📝 改进现有文档
- 修正错误和不准确的信息
- 补充缺失的内容
- 改进文档结构和可读性
- 添加更多示例和用例

### ✨ 创建新文档
- 教程和最佳实践
- 高级配置指南
- 集成案例研究
- 常见问题解答

### 🔧 文档工具
- **Markdown** - 使用标准Markdown格式
- **Mermaid** - 用于绘制图表和流程图
- **代码高亮** - 支持多种编程语言语法高亮

## 📞 获取帮助

如果您在使用文档时遇到问题：

1. **查看故障排除** - [故障排除指南](TROUBLESHOOTING.md)
2. **搜索已有问题** - [GitHub Issues](https://github.com/your-username/unm-api-server/issues)
3. **参与讨论** - [GitHub Discussions](https://github.com/your-username/unm-api-server/discussions)
4. **提交问题** - [创建新Issue](https://github.com/your-username/unm-api-server/issues/new)

## 📊 文档统计

- **总文档数量**: 8个主要文档
- **代码示例**: 100+ 个配置和代码示例
- **图表数量**: 10+ 个架构和流程图
- **最后更新**: 2025-01-23

---

感谢您使用 UNM API Server！如果这些文档对您有帮助，请给我们一个 ⭐ 星标支持！
