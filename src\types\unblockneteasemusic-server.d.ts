/**
 * Type definitions for @unblockneteasemusic/server
 * 
 * UnblockNeteaseMusic 第三方音源解灰库的类型定义
 * 用于解决网易云音乐灰色歌曲的播放问题
 */

declare module '@unblockneteasemusic/server' {
  // 导入项目中已有的 MusicUrl 接口类型
  import { MusicUrl } from '../api';

  /**
   * 音乐匹配结果接口 - 与项目主 API 接口格式保持一致
   * 基于项目中的 MusicUrl 接口，确保格式统一
   */
  interface MatchResult {
    /** 音乐播放链接 */
    url?: string;
    /** 实际音质 (kbps) - 与 MusicUrl.br 一致 */
    br?: number;
    /** 文件大小 (KB) - 与 MusicUrl.size 一致 */
    size?: number;
    /** 文件格式 (mp3/flac) - 与 MusicUrl.type 一致 */
    type?: string;
    /** 实际音源 (来自UNM) - 与 MusicUrl.source 一致 */
    source?: string;
  }

  /**
   * 支持的音源类型
   */
  type SupportedSource = 
    | 'netease'     // 网易云音乐
    | 'qq'          // QQ音乐
    | 'xiami'       // 虾米音乐
    | 'baidu'       // 百度音乐
    | 'kugou'       // 酷狗音乐
    | 'kuwo'        // 酷我音乐
    | 'migu'        // 咪咕音乐
    | 'joox'        // JOOX音乐
    | 'youtube'     // YouTube Music
    | 'bilibili'    // 哔哩哔哩
    | 'pyncmd';     // Python网易云音乐

  /**
   * 主要的音乐匹配函数
   *
   * @param songId - 歌曲ID（数字）
   * @param sources - 音源列表，指定要搜索的音源
   * @param options - 可选配置参数
   * @returns Promise<MatchResult | null> - 匹配结果，如果没有找到则返回null
   *
   * 注意：返回的 MatchResult 与项目中的 MusicUrl 接口格式完全兼容
   */
  function match(
    songId: number,
    sources: SupportedSource[] | string[],
    options?: {
      /** 音质要求 */
      quality?: number;
      /** 超时时间（毫秒） */
      timeout?: number;
    }
  ): Promise<MatchResult | null>;

  // 默认导出 match 函数
  export = match;
}

/**
 * 扩展全局类型，支持 require 方式导入包信息
 */
declare module '@unblockneteasemusic/server/package.json' {
  interface PackageInfo {
    name: string;
    version: string;
    description?: string;
    author?: string;
    license?: string;
  }

  const packageInfo: PackageInfo;
  export = packageInfo;
}
