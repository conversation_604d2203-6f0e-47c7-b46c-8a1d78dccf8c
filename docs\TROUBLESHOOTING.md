# 🔧 故障排除指南

本文档提供了 UNM API Server 常见问题的诊断和解决方案。

## 🚨 常见问题

### 1. 服务启动问题

#### 问题：端口被占用

**错误信息**:
```
Error: listen EADDRINUSE: address already in use :::3000
```

**解决方案**:
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3000
# 或使用 lsof
sudo lsof -i :3000

# 杀死占用进程
sudo kill -9 <PID>

# 或更改端口
export PORT=3001
npm start
```

#### 问题：权限不足

**错误信息**:
```
Error: EACCES: permission denied, open '/var/log/unm-api.log'
```

**解决方案**:
```bash
# 修改文件权限
sudo chown -R $USER:$USER /path/to/project
sudo chmod -R 755 /path/to/project

# 或创建日志目录
sudo mkdir -p /var/log/unm-api
sudo chown $USER:$USER /var/log/unm-api
```

#### 问题：环境变量未加载

**错误信息**:
```
TypeError: Cannot read property 'PORT' of undefined
```

**解决方案**:
```bash
# 检查 .env 文件是否存在
ls -la .env

# 复制环境变量模板
cp .env.template .env

# 检查环境变量加载
node -e "require('dotenv').config(); console.log(process.env.PORT);"
```

### 2. 依赖问题

#### 问题：Node.js 版本不兼容

**错误信息**:
```
Error: The engine "node" is incompatible with this module
```

**解决方案**:
```bash
# 检查 Node.js 版本
node --version

# 使用 nvm 切换版本
nvm install 18
nvm use 18

# 或更新 Node.js
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# macOS
brew install node@18
```

#### 问题：依赖安装失败

**错误信息**:
```
npm ERR! peer dep missing: typescript@>=4.0.0
```

**解决方案**:
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 或使用 yarn
yarn install
```

### 3. API 请求问题

#### 问题：API 返回 404

**错误信息**:
```json
{
  "code": 404,
  "message": "Not Found",
  "data": null
}
```

**诊断步骤**:
```bash
# 检查服务是否运行
curl http://localhost:3000/health

# 检查路由配置
curl -v http://localhost:3000/api.php?types=search&name=test

# 查看服务日志
tail -f logs/combined.log
```

**解决方案**:
- 检查 URL 路径是否正确
- 确认服务正常运行
- 检查路由配置

#### 问题：API 返回 500 错误

**错误信息**:
```json
{
  "code": 500,
  "message": "Internal server error",
  "data": null
}
```

**诊断步骤**:
```bash
# 查看错误日志
tail -f logs/error.log

# 检查系统资源
top
df -h
free -m

# 检查外部API连通性
curl -I https://music-api.example.com
```

**解决方案**:
- 检查错误日志找出具体原因
- 确认外部API服务可用
- 检查系统资源是否充足

#### 问题：API 响应缓慢

**症状**: 请求响应时间超过 10 秒

**诊断步骤**:
```bash
# 检查响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3000/api.php?types=search&name=test"

# curl-format.txt 内容:
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n

# 检查缓存状态
curl http://localhost:3000/cache/stats

# 监控系统性能
htop
iotop
```

**解决方案**:
- 启用缓存机制
- 优化数据库查询
- 增加服务器资源
- 使用CDN加速

### 4. 缓存问题

#### 问题：缓存不工作

**症状**: 重复请求没有使用缓存

**诊断步骤**:
```bash
# 检查缓存配置
curl http://localhost:3000/cache/stats

# 检查环境变量
echo $ENABLE_CACHE
echo $CACHE_TTL

# 查看缓存日志
grep "Cache" logs/combined.log
```

**解决方案**:
```bash
# 启用缓存
export ENABLE_CACHE=true

# 设置缓存TTL
export CACHE_TTL=300

# 重启服务
npm restart
```

#### 问题：Redis 连接失败

**错误信息**:
```
Error: Redis connection failed: ECONNREFUSED 127.0.0.1:6379
```

**解决方案**:
```bash
# 检查 Redis 服务状态
sudo systemctl status redis
# 或
redis-cli ping

# 启动 Redis 服务
sudo systemctl start redis

# 检查 Redis 配置
redis-cli config get "*"

# 测试连接
redis-cli -h localhost -p 6379 ping
```

### 5. 外部API问题

#### 问题：音源API不可用

**错误信息**:
```json
{
  "code": 503,
  "message": "Music API service unavailable",
  "data": null
}
```

**诊断步骤**:
```bash
# 检查外部API状态
curl -I https://music-api.example.com/api.php

# 测试不同音源
curl "http://localhost:3000/api.php?types=search&source=netease&name=test"
curl "http://localhost:3000/api.php?types=search&source=qq&name=test"

# 查看API健康状态
curl http://localhost:3000/api/health/sources
```

**解决方案**:
- 等待外部API服务恢复
- 切换到其他可用音源
- 配置API降级策略

#### 问题：网络超时

**错误信息**:
```
Error: timeout of 10000ms exceeded
```

**解决方案**:
```bash
# 增加超时时间
export HTTP_TIMEOUT=30000

# 检查网络连接
ping music-api.example.com
traceroute music-api.example.com

# 配置代理 (如果需要)
export HTTP_PROXY=http://proxy.example.com:8080
export HTTPS_PROXY=http://proxy.example.com:8080
```

### 6. 内存问题

#### 问题：内存泄漏

**症状**: 内存使用持续增长

**诊断步骤**:
```bash
# 监控内存使用
ps aux | grep node
top -p <PID>

# 生成内存快照
node --inspect server.js
# 在 Chrome DevTools 中分析内存

# 使用 clinic.js 分析
npm install -g clinic
clinic doctor -- node server.js
```

**解决方案**:
- 检查缓存大小限制
- 修复内存泄漏代码
- 增加服务器内存
- 配置进程重启策略

#### 问题：内存不足

**错误信息**:
```
FATAL ERROR: Ineffective mark-compacts near heap limit Allocation failed - JavaScript heap out of memory
```

**解决方案**:
```bash
# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=2048"

# 或在启动脚本中设置
node --max-old-space-size=2048 server.js

# 优化缓存配置
export CACHE_MAX_SIZE=500
export CACHE_TTL=300
```

## 🔍 诊断工具

### 1. 健康检查

```bash
# 基础健康检查
curl http://localhost:3000/health

# 详细系统信息
curl http://localhost:3000/info

# 缓存状态
curl http://localhost:3000/cache/stats

# 性能指标
curl http://localhost:3000/metrics
```

### 2. 日志分析

```bash
# 查看实时日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log

# 搜索特定错误
grep "Error" logs/combined.log

# 分析访问模式
awk '{print $1}' logs/access.log | sort | uniq -c | sort -nr
```

### 3. 性能监控

```bash
# 系统资源监控
htop
iotop
nethogs

# Node.js 进程监控
pm2 monit

# 网络连接监控
netstat -an | grep :3000
ss -tuln | grep :3000
```

### 4. 数据库连接测试

```bash
# Redis 连接测试
redis-cli ping
redis-cli info

# 测试缓存操作
redis-cli set test "hello"
redis-cli get test
redis-cli del test
```

## 🛠️ 调试技巧

### 1. 启用调试模式

```bash
# 设置调试级别
export LOG_LEVEL=debug
export NODE_ENV=development

# 启用详细日志
export DEBUG=*

# 重启服务
npm run dev
```

### 2. 使用调试器

```bash
# 启动调试模式
node --inspect server.js

# 或使用 VS Code 调试
# 在 .vscode/launch.json 中配置调试选项
```

### 3. 网络调试

```bash
# 抓包分析
sudo tcpdump -i any -w capture.pcap port 3000

# 使用 Wireshark 分析
wireshark capture.pcap

# HTTP 请求调试
curl -v http://localhost:3000/api.php?types=search&name=test
```

## 📊 性能优化

### 1. 缓存优化

```bash
# 调整缓存配置
export CACHE_TTL=600
export CACHE_MAX_SIZE=2000
export ENABLE_REDIS=true

# 监控缓存命中率
curl http://localhost:3000/cache/stats
```

### 2. 连接池优化

```javascript
// 在 httpService.ts 中配置
const agent = new https.Agent({
  keepAlive: true,
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 60000
});
```

### 3. 进程管理

```bash
# 使用 PM2 集群模式
pm2 start ecosystem.config.js --env production

# 监控进程状态
pm2 status
pm2 monit

# 重启进程
pm2 restart all
```

## 🚨 紧急情况处理

### 1. 服务完全不可用

```bash
# 快速重启
pm2 restart all
# 或
sudo systemctl restart unm-api-server

# 检查系统资源
df -h
free -m
top

# 查看最近的错误
tail -100 logs/error.log
```

### 2. 高负载情况

```bash
# 临时增加频率限制
export RATE_LIMIT_MAX=50
export RATE_LIMIT_WINDOW=60000

# 启用慢速防护
export ENABLE_SLOW_DOWN=true

# 重启服务应用新配置
pm2 restart all
```

### 3. 安全事件

```bash
# 查看安全日志
tail -f logs/security.log

# 临时阻断可疑IP
iptables -A INPUT -s <suspicious_ip> -j DROP

# 启用严格模式
export SECURITY_STRICT_MODE=true
```

## 📞 获取帮助

### 1. 日志收集

在寻求帮助时，请提供以下信息：

```bash
# 系统信息
uname -a
node --version
npm --version

# 服务状态
curl http://localhost:3000/health
curl http://localhost:3000/info

# 最近的日志
tail -100 logs/error.log
tail -100 logs/combined.log

# 配置信息 (移除敏感信息)
cat .env | grep -v PASSWORD | grep -v SECRET
```

### 2. 问题报告模板

```markdown
## 问题描述
[详细描述遇到的问题]

## 环境信息
- 操作系统: 
- Node.js 版本: 
- 项目版本: 

## 重现步骤
1. 
2. 
3. 

## 期望结果
[描述期望的正常行为]

## 实际结果
[描述实际发生的情况]

## 错误日志
```
[粘贴相关的错误日志]
```

## 已尝试的解决方案
[列出已经尝试过的解决方法]
```

### 3. 联系方式

- **GitHub Issues**: [项目Issues页面]
- **文档**: [在线文档地址]
- **社区讨论**: [讨论区地址]

---

如果以上解决方案都无法解决您的问题，请创建详细的问题报告并寻求社区帮助。
