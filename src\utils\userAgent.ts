import { config } from './../config/environment';

/**
 * 随机获取一个User-Agent
 */
export function getRandomUserAgent(): string {
  const userAgents = config.userAgents;
  if (userAgents.length === 0) {
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
  }
  const randomIndex = Math.floor(Math.random() * userAgents.length);
  return userAgents[randomIndex] || userAgents[0] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
}

/**
 * 根据平台类型获取User-Agent
 */
export function getUserAgentByPlatform(platform: 'windows' | 'mac' | 'linux' | 'android' | 'ios' | 'random' = 'random'): string {
  const userAgents = config.userAgents;
  const defaultUA = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';

  if (userAgents.length === 0) {
    return defaultUA;
  }

  switch (platform) {
    case 'windows':
      // 查找Windows相关的User-Agent
      const windowsUA = userAgents.find(ua => ua.includes('Windows NT'));
      return windowsUA || userAgents[0] || defaultUA;

    case 'mac':
      // 查找macOS相关的User-Agent
      const macUA = userAgents.find(ua => ua.includes('Macintosh'));
      return macUA || userAgents[0] || defaultUA;

    case 'linux':
      // 查找Linux相关的User-Agent (排除Android)
      const linuxUA = userAgents.find(ua => ua.includes('X11; Linux'));
      return linuxUA || userAgents[0] || defaultUA;

    case 'android':
      // 查找Android相关的User-Agent
      const androidUA = userAgents.find(ua => ua.includes('Android'));
      return androidUA || userAgents[0] || defaultUA;

    case 'ios':
      // 查找iOS相关的User-Agent
      const iosUA = userAgents.find(ua => ua.includes('iPhone') || ua.includes('iPad'));
      return iosUA || userAgents[0] || defaultUA;

    case 'random':
    default:
      return getRandomUserAgent();
  }
}

/**
 * 获取所有可用的User-Agent列表
 */
export function getAllUserAgents(): string[] {
  return [...config.userAgents];
}

/**
 * 验证User-Agent是否有效
 */
export function isValidUserAgent(userAgent: string): boolean {
  return typeof userAgent === 'string' && userAgent.length > 0 && userAgent.includes('Mozilla');
}
