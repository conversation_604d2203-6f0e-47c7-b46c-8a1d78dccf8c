#!/usr/bin/env tsx

/**
 * 安全渗透测试脚本
 * 对UNM API Server进行全面的安全测试
 */

import axios, { AxiosResponse } from 'axios';
import { logger } from '../src/utils/logger';

interface TestResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING' | 'INFO';
  message: string;
  details?: any;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

class SecurityTester {
  private baseUrl: string;
  private results: TestResult[] = [];

  constructor(baseUrl: string = 'http://localhost:5678') {
    this.baseUrl = baseUrl;
  }

  private addResult(result: TestResult) {
    this.results.push(result);
    const statusIcon = {
      'PASS': '✅',
      'FAIL': '❌',
      'WARNING': '⚠️',
      'INFO': '📋'
    }[result.status];
    
    const severityColor = result.severity ? {
      'LOW': '\x1b[32m',      // Green
      'MEDIUM': '\x1b[33m',   // Yellow
      'HIGH': '\x1b[31m',     // Red
      'CRITICAL': '\x1b[35m'  // Magenta
    }[result.severity] : '';
    
    const resetColor = '\x1b[0m';
    
    console.log(`${statusIcon} [${result.category}] ${severityColor}${result.test}${resetColor}: ${result.message}`);
    if (result.details) {
      console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
    }
  }

  private async makeRequest(method: string, path: string, data?: any, headers?: any): Promise<AxiosResponse | null> {
    try {
      const config = {
        method,
        url: `${this.baseUrl}${path}`,
        data,
        headers: {
          'User-Agent': 'SecurityTester/1.0',
          ...headers
        },
        timeout: 10000,
        validateStatus: () => true // Don't throw on any status code
      };
      
      return await axios(config);
    } catch (error: any) {
      return null;
    }
  }

  // 1. 基础连接测试
  async testBasicConnectivity() {
    console.log('\n🔍 基础连接测试...');
    
    const response = await this.makeRequest('GET', '/');
    if (response && response.status === 200) {
      this.addResult({
        category: '连接性',
        test: '基础连接',
        status: 'PASS',
        message: '服务器正常响应'
      });
    } else {
      this.addResult({
        category: '连接性',
        test: '基础连接',
        status: 'FAIL',
        message: '无法连接到服务器',
        severity: 'CRITICAL'
      });
      return false;
    }
    return true;
  }

  // 2. HTTP方法测试
  async testHttpMethods() {
    console.log('\n🔍 HTTP方法安全测试...');
    
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS', 'TRACE'];
    
    for (const method of methods) {
      const response = await this.makeRequest(method, '/');
      if (response) {
        if (method === 'TRACE' && response.status !== 405) {
          this.addResult({
            category: 'HTTP方法',
            test: `${method}方法`,
            status: 'FAIL',
            message: 'TRACE方法未被禁用，可能存在XST攻击风险',
            severity: 'MEDIUM'
          });
        } else if (['GET', 'POST', 'OPTIONS'].includes(method) && response.status < 400) {
          this.addResult({
            category: 'HTTP方法',
            test: `${method}方法`,
            status: 'PASS',
            message: '方法正常工作'
          });
        } else if (response.status === 405) {
          this.addResult({
            category: 'HTTP方法',
            test: `${method}方法`,
            status: 'PASS',
            message: '不支持的方法被正确拒绝'
          });
        }
      }
    }
  }

  // 3. 输入验证测试
  async testInputValidation() {
    console.log('\n🔍 输入验证安全测试...');
    
    // SQL注入测试
    const sqlPayloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM information_schema.tables --",
      "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0 --"
    ];

    for (const payload of sqlPayloads) {
      const response = await this.makeRequest('GET', `/api/v1/search?source=netease&q=${encodeURIComponent(payload)}`);
      if (response && response.status === 200 && response.data.code === 200) {
        this.addResult({
          category: '输入验证',
          test: 'SQL注入防护',
          status: 'WARNING',
          message: `可能存在SQL注入风险，payload: ${payload}`,
          severity: 'HIGH'
        });
      } else {
        this.addResult({
          category: '输入验证',
          test: 'SQL注入防护',
          status: 'PASS',
          message: 'SQL注入payload被正确处理'
        });
      }
    }

    // XSS测试
    const xssPayloads = [
      "<script>alert('XSS')</script>",
      "javascript:alert('XSS')",
      "<img src=x onerror=alert('XSS')>",
      "';alert('XSS');//"
    ];

    for (const payload of xssPayloads) {
      const response = await this.makeRequest('GET', `/api/v1/search?source=netease&q=${encodeURIComponent(payload)}`);
      if (response && response.data && typeof response.data === 'string' && response.data.includes(payload)) {
        this.addResult({
          category: '输入验证',
          test: 'XSS防护',
          status: 'FAIL',
          message: `可能存在XSS风险，payload被反射: ${payload}`,
          severity: 'HIGH'
        });
      } else {
        this.addResult({
          category: '输入验证',
          test: 'XSS防护',
          status: 'PASS',
          message: 'XSS payload被正确处理'
        });
      }
    }

    // 路径遍历测试
    const pathTraversalPayloads = [
      "../../../etc/passwd",
      "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
      "....//....//....//etc/passwd",
      "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
    ];

    for (const payload of pathTraversalPayloads) {
      const response = await this.makeRequest('GET', `/api/v1/search?source=${encodeURIComponent(payload)}&q=test`);
      if (response && response.status === 200 && response.data.code === 200) {
        this.addResult({
          category: '输入验证',
          test: '路径遍历防护',
          status: 'WARNING',
          message: `可能存在路径遍历风险，payload: ${payload}`,
          severity: 'MEDIUM'
        });
      } else {
        this.addResult({
          category: '输入验证',
          test: '路径遍历防护',
          status: 'PASS',
          message: '路径遍历payload被正确拒绝'
        });
      }
    }
  }

  // 4. 参数污染测试
  async testParameterPollution() {
    console.log('\n🔍 参数污染测试...');
    
    const response = await this.makeRequest('GET', '/api/v1/search?source=netease&source=malicious&q=test');
    if (response) {
      this.addResult({
        category: '参数安全',
        test: '参数污染',
        status: 'INFO',
        message: '测试重复参数处理',
        details: { status: response.status, data: response.data }
      });
    }
  }

  // 5. 头部注入测试
  async testHeaderInjection() {
    console.log('\n🔍 HTTP头部注入测试...');
    
    const maliciousHeaders = {
      'X-Forwarded-For': '127.0.0.1, <script>alert("XSS")</script>',
      'User-Agent': 'Mozilla/5.0 <script>alert("XSS")</script>',
      'Referer': 'http://evil.com/<script>alert("XSS")</script>',
      'X-Real-IP': '../../etc/passwd'
    };

    for (const [header, value] of Object.entries(maliciousHeaders)) {
      const response = await this.makeRequest('GET', '/', null, { [header]: value });
      if (response) {
        this.addResult({
          category: '头部安全',
          test: `${header}头部注入`,
          status: 'PASS',
          message: '恶意头部被正常处理'
        });
      }
    }
  }

  // 6. 频率限制测试
  async testRateLimit() {
    console.log('\n🔍 频率限制测试...');
    
    const requests = [];
    for (let i = 0; i < 10; i++) {
      requests.push(this.makeRequest('GET', '/api/v1/search?source=netease&q=158616'));
    }
    
    const responses = await Promise.all(requests);
    const rateLimited = responses.some(r => r && r.status === 429);
    
    if (rateLimited) {
      this.addResult({
        category: '频率限制',
        test: '并发请求限制',
        status: 'PASS',
        message: '频率限制正常工作'
      });
    } else {
      this.addResult({
        category: '频率限制',
        test: '并发请求限制',
        status: 'WARNING',
        message: '未检测到频率限制，可能存在DoS风险',
        severity: 'MEDIUM'
      });
    }
  }

  // 7. 信息泄露测试
  async testInformationDisclosure() {
    console.log('\n🔍 信息泄露测试...');
    
    // 测试错误信息泄露
    const response = await this.makeRequest('GET', '/api/v1/nonexistent');
    if (response && response.data) {
      const responseText = JSON.stringify(response.data);
      if (responseText.includes('stack') || responseText.includes('Error:') || responseText.includes('at ')) {
        this.addResult({
          category: '信息泄露',
          test: '错误信息泄露',
          status: 'FAIL',
          message: '错误响应可能包含敏感信息',
          severity: 'MEDIUM',
          details: response.data
        });
      } else {
        this.addResult({
          category: '信息泄露',
          test: '错误信息泄露',
          status: 'PASS',
          message: '错误信息被正确处理'
        });
      }
    }

    // 测试服务器信息泄露
    const infoResponse = await this.makeRequest('GET', '/info');
    if (infoResponse && infoResponse.data && infoResponse.data.data) {
      const serverInfo = infoResponse.data.data;
      if (serverInfo.version || serverInfo.author) {
        this.addResult({
          category: '信息泄露',
          test: '服务器信息暴露',
          status: 'WARNING',
          message: '服务器版本和作者信息被暴露',
          severity: 'LOW',
          details: serverInfo
        });
      }
    }
  }

  // 8. 安全头部测试
  async testSecurityHeaders() {
    console.log('\n🔍 安全头部测试...');
    
    const response = await this.makeRequest('GET', '/');
    if (response) {
      const headers = response.headers;
      
      const securityHeaders = {
        'x-content-type-options': 'nosniff',
        'x-frame-options': 'DENY',
        'x-xss-protection': '1; mode=block',
        'strict-transport-security': 'max-age=31536000',
        'content-security-policy': true,
        'referrer-policy': 'strict-origin-when-cross-origin'
      };

      for (const [header, expectedValue] of Object.entries(securityHeaders)) {
        if (headers[header]) {
          this.addResult({
            category: '安全头部',
            test: `${header}头部`,
            status: 'PASS',
            message: `安全头部存在: ${headers[header]}`
          });
        } else {
          this.addResult({
            category: '安全头部',
            test: `${header}头部`,
            status: 'WARNING',
            message: '缺少重要的安全头部',
            severity: 'MEDIUM'
          });
        }
      }
    }
  }

  // 9. CORS测试
  async testCORS() {
    console.log('\n🔍 CORS配置测试...');
    
    const response = await this.makeRequest('OPTIONS', '/', null, {
      'Origin': 'http://evil.com',
      'Access-Control-Request-Method': 'GET'
    });

    if (response) {
      const corsHeader = response.headers['access-control-allow-origin'];
      if (corsHeader === '*') {
        this.addResult({
          category: 'CORS',
          test: 'CORS配置',
          status: 'WARNING',
          message: 'CORS配置过于宽松，允许所有源',
          severity: 'MEDIUM'
        });
      } else {
        this.addResult({
          category: 'CORS',
          test: 'CORS配置',
          status: 'PASS',
          message: 'CORS配置相对安全'
        });
      }
    }
  }

  // 10. 缓存投毒测试
  async testCachePoisoning() {
    console.log('\n🔍 缓存投毒测试...');
    
    // 测试Host头部操作
    const response = await this.makeRequest('GET', '/api/v1/search?source=netease&q=test', null, {
      'Host': 'evil.com'
    });

    if (response) {
      this.addResult({
        category: '缓存安全',
        test: 'Host头部操作',
        status: 'INFO',
        message: '测试Host头部操作的影响',
        details: { status: response.status }
      });
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📊 安全测试报告');
    console.log('='.repeat(50));
    
    const summary = {
      total: this.results.length,
      pass: this.results.filter(r => r.status === 'PASS').length,
      fail: this.results.filter(r => r.status === 'FAIL').length,
      warning: this.results.filter(r => r.status === 'WARNING').length,
      info: this.results.filter(r => r.status === 'INFO').length
    };

    const severitySummary = {
      critical: this.results.filter(r => r.severity === 'CRITICAL').length,
      high: this.results.filter(r => r.severity === 'HIGH').length,
      medium: this.results.filter(r => r.severity === 'MEDIUM').length,
      low: this.results.filter(r => r.severity === 'LOW').length
    };

    console.log(`\n📈 测试统计:`);
    console.log(`  总测试数: ${summary.total}`);
    console.log(`  ✅ 通过: ${summary.pass}`);
    console.log(`  ❌ 失败: ${summary.fail}`);
    console.log(`  ⚠️  警告: ${summary.warning}`);
    console.log(`  📋 信息: ${summary.info}`);

    console.log(`\n🚨 风险等级统计:`);
    console.log(`  🔴 严重: ${severitySummary.critical}`);
    console.log(`  🟠 高危: ${severitySummary.high}`);
    console.log(`  🟡 中危: ${severitySummary.medium}`);
    console.log(`  🟢 低危: ${severitySummary.low}`);

    // 高风险问题汇总
    const highRiskIssues = this.results.filter(r => 
      r.status === 'FAIL' || (r.severity && ['CRITICAL', 'HIGH'].includes(r.severity))
    );

    if (highRiskIssues.length > 0) {
      console.log(`\n🚨 高风险问题:`);
      highRiskIssues.forEach(issue => {
        console.log(`  • [${issue.category}] ${issue.test}: ${issue.message}`);
      });
    }

    // 安全建议
    console.log(`\n💡 安全建议:`);
    if (severitySummary.critical > 0) {
      console.log(`  • 立即修复严重安全问题`);
    }
    if (severitySummary.high > 0) {
      console.log(`  • 优先修复高危安全问题`);
    }
    if (summary.warning > 0) {
      console.log(`  • 审查并改进警告项目`);
    }
    console.log(`  • 定期进行安全测试`);
    console.log(`  • 实施安全开发生命周期(SDLC)`);
    console.log(`  • 考虑使用Web应用防火墙(WAF)`);

    return {
      summary,
      severitySummary,
      highRiskIssues,
      allResults: this.results
    };
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🔒 开始安全渗透测试...');
    console.log(`🎯 目标: ${this.baseUrl}`);
    
    const connected = await this.testBasicConnectivity();
    if (!connected) {
      console.log('❌ 无法连接到目标服务器，测试终止');
      return;
    }

    await this.testHttpMethods();
    await this.testInputValidation();
    await this.testParameterPollution();
    await this.testHeaderInjection();
    await this.testRateLimit();
    await this.testInformationDisclosure();
    await this.testSecurityHeaders();
    await this.testCORS();
    await this.testCachePoisoning();

    return this.generateReport();
  }
}

// 运行测试
async function main() {
  const tester = new SecurityTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { SecurityTester };
