import slowDown from 'express-slow-down';
import { config } from '../config/environment';
import { logger } from '../utils/logger';

/**
 * 慢速HTTP攻击防护中间件
 * 对频繁请求的客户端进行延迟响应，防止慢速攻击
 */
export const speedLimiter = slowDown({
  windowMs: parseInt(process.env.SLOW_DOWN_WINDOW || '900000'), // 15分钟窗口
  delayAfter: parseInt(process.env.SLOW_DOWN_DELAY_AFTER || '100'), // 100个请求后开始延迟
  delayMs: () => parseInt(process.env.SLOW_DOWN_DELAY_MS || '500'), // 每个请求延迟500ms
  maxDelayMs: parseInt(process.env.SLOW_DOWN_MAX_DELAY || '20000'), // 最大延迟20秒
  skipFailedRequests: true, // 跳过失败的请求
  skipSuccessfulRequests: false, // 不跳过成功的请求
  validate: { delayMs: false }, // 禁用警告
  
  // 自定义键生成器，基于IP和User-Agent
  keyGenerator: (req) => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    return `${ip}:${userAgent.substring(0, 50)}`;
  },

  // 自定义跳过条件
  skip: (req) => {
    // 跳过健康检查请求
    if (req.path === '/health' || req.path === '/metrics') {
      return true;
    }

    // 跳过本地请求（开发环境）
    if (config.nodeEnv === 'development' && (req.ip === '127.0.0.1' || req.ip === '::1')) {
      return true;
    }

    return false;
  },

  // 延迟回调 - 注释掉不支持的选项
  // onLimitReached: (req: any) => {
  //   logger.warn('Slow down limit reached', {
  //     ip: req.ip,
  //     userAgent: req.get('User-Agent'),
  //     url: req.url,
  //     method: req.method,
  //     timestamp: new Date().toISOString()
  //   });
  // }

});

/**
 * 连接超时防护中间件
 * 防止慢速连接攻击
 */
export const connectionTimeout = (req: any, res: any, next: any) => {
  // 设置请求超时
  const timeout = parseInt(process.env.REQUEST_TIMEOUT || '30000'); // 30秒
  
  const timeoutId = setTimeout(() => {
    if (!res.headersSent) {
      logger.warn('Request timeout', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
        method: req.method,
        timeout,
        timestamp: new Date().toISOString(),
      });
      
      res.status(408).json({
        code: 408,
        message: 'Request Timeout',
        data: {
          timeout_ms: timeout,
          reason: 'Request processing took too long',
        },
        timestamp: new Date().toISOString(),
      });
    }
  }, timeout);
  
  // 清理超时定时器
  res.on('finish', () => {
    clearTimeout(timeoutId);
  });
  
  res.on('close', () => {
    clearTimeout(timeoutId);
  });
  
  next();
};
