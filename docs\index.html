<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UNM API Server - 文档中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .doc-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid #667eea;
        }
        
        .doc-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .doc-card h3 {
            color: #667eea;
            margin-bottom: 12px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .doc-card p {
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .doc-links {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .doc-link {
            display: inline-block;
            padding: 6px 12px;
            background: #f8f9fa;
            color: #667eea;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            border: 1px solid #e9ecef;
        }
        
        .doc-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        
        .quick-start {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .quick-start h2 {
            color: #667eea;
            margin-bottom: 16px;
            font-size: 1.6rem;
        }
        
        .quick-start-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }
        
        .quick-item {
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }
        
        .quick-item h4 {
            color: #333;
            margin-bottom: 8px;
        }
        
        .quick-item a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .quick-item a:hover {
            text-decoration: underline;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }
        
        .emoji {
            font-size: 1.2em;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .docs-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><span class="emoji">🎵</span> UNM API Server</h1>
            <p>现代化音乐API服务 - 文档中心</p>
        </header>
        
        <section class="quick-start">
            <h2><span class="emoji">🚀</span> 快速开始</h2>
            <div class="quick-start-grid">
                <div class="quick-item">
                    <h4>新手入门</h4>
                    <a href="../README.md">项目概览和快速安装</a>
                </div>
                <div class="quick-item">
                    <h4>API使用</h4>
                    <a href="API.md">API接口文档和示例</a>
                </div>
                <div class="quick-item">
                    <h4>部署上线</h4>
                    <a href="DEPLOYMENT.md">生产环境部署指南</a>
                </div>
                <div class="quick-item">
                    <h4>参与开发</h4>
                    <a href="DEVELOPMENT.md">开发环境和贡献指南</a>
                </div>
            </div>
        </section>
        
        <div class="docs-grid">
            <div class="doc-card">
                <h3><span class="emoji">📖</span> API文档</h3>
                <p>完整的API接口说明，包括传统兼容API和现代RESTful API，支持音乐搜索、播放链接获取、专辑图片和歌词等功能。</p>
                <div class="doc-links">
                    <a href="API.md" class="doc-link">完整API文档</a>
                    <a href="API.md#传统兼容api" class="doc-link">传统API</a>
                    <a href="API.md#现代restful-api" class="doc-link">RESTful API</a>
                    <a href="API.md#错误码说明" class="doc-link">错误码</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">🚀</span> 部署指南</h3>
                <p>详细的部署文档，包括Docker容器化部署、传统服务器部署、反向代理配置和SSL/TLS安全配置。</p>
                <div class="doc-links">
                    <a href="DEPLOYMENT.md" class="doc-link">部署指南</a>
                    <a href="DEPLOYMENT.md#docker-部署" class="doc-link">Docker部署</a>
                    <a href="DEPLOYMENT.md#反向代理配置" class="doc-link">反向代理</a>
                    <a href="DEPLOYMENT.md#ssltls-配置" class="doc-link">SSL配置</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">💻</span> 开发指南</h3>
                <p>开发环境搭建、代码规范、测试指南和贡献流程，帮助开发者快速参与项目开发。</p>
                <div class="doc-links">
                    <a href="DEVELOPMENT.md" class="doc-link">开发指南</a>
                    <a href="DEVELOPMENT.md#开发环境搭建" class="doc-link">环境搭建</a>
                    <a href="DEVELOPMENT.md#代码规范" class="doc-link">代码规范</a>
                    <a href="DEVELOPMENT.md#测试" class="doc-link">测试指南</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">🏗️</span> 架构文档</h3>
                <p>技术架构设计文档，包括整体架构、分层设计、数据流、性能优化和扩展性设计。</p>
                <div class="doc-links">
                    <a href="ARCHITECTURE.md" class="doc-link">架构文档</a>
                    <a href="ARCHITECTURE.md#整体架构图" class="doc-link">整体架构</a>
                    <a href="ARCHITECTURE.md#分层架构详解" class="doc-link">分层架构</a>
                    <a href="ARCHITECTURE.md#性能架构" class="doc-link">性能优化</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">🔒</span> 安全指南</h3>
                <p>全面的安全配置指南，包括认证授权、访问控制、安全头部、频率限制和威胁检测。</p>
                <div class="doc-links">
                    <a href="SECURITY.md" class="doc-link">安全指南</a>
                    <a href="SECURITY.md#认证和授权" class="doc-link">认证授权</a>
                    <a href="SECURITY.md#安全头部配置" class="doc-link">安全头部</a>
                    <a href="SECURITY.md#安全监控" class="doc-link">安全监控</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">🔧</span> 故障排除</h3>
                <p>常见问题的诊断和解决方案，包括启动问题、API错误、性能问题和紧急情况处理。</p>
                <div class="doc-links">
                    <a href="TROUBLESHOOTING.md" class="doc-link">故障排除</a>
                    <a href="TROUBLESHOOTING.md#常见问题" class="doc-link">常见问题</a>
                    <a href="TROUBLESHOOTING.md#诊断工具" class="doc-link">诊断工具</a>
                    <a href="TROUBLESHOOTING.md#性能优化" class="doc-link">性能优化</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">📝</span> 项目文档</h3>
                <p>项目管理相关文档，包括需求文档、设计文档、任务分解和版本更新记录。</p>
                <div class="doc-links">
                    <a href="../requirements.md" class="doc-link">需求文档</a>
                    <a href="../design.md" class="doc-link">设计文档</a>
                    <a href="../tasks.md" class="doc-link">任务文档</a>
                    <a href="../CHANGELOG.md" class="doc-link">更新日志</a>
                </div>
            </div>
            
            <div class="doc-card">
                <h3><span class="emoji">🛠️</span> 工具和脚本</h3>
                <p>项目提供的各种工具和脚本，包括配置验证、安全测试、性能优化和文档生成。</p>
                <div class="doc-links">
                    <a href="../package.json" class="doc-link">NPM脚本</a>
                    <a href="../.env.template" class="doc-link">环境配置</a>
                    <a href="../Dockerfile" class="doc-link">Docker配置</a>
                    <a href="README.md" class="doc-link">文档导航</a>
                </div>
            </div>
        </div>
        
        <footer class="footer">
            <p>
                <span class="emoji">📚</span> 
                UNM API Server 文档中心 | 
                <a href="https://github.com/your-username/unm-api-server" style="color: white;">GitHub</a> | 
                最后更新: <span id="lastUpdate"></span>
            </p>
        </footer>
    </div>
    
    <script>
        // 设置最后更新时间
        document.getElementById('lastUpdate').textContent = new Date().toLocaleDateString('zh-CN');
        
        // 添加平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // 添加卡片点击效果
        document.querySelectorAll('.doc-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
