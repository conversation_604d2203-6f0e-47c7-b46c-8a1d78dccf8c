import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * HTTP方法过滤中间件
 * 禁用不安全的HTTP方法，防止XST攻击等安全风险
 */
export const methodFilter = (req: Request, res: Response, next: NextFunction): void | Response => {
  // 允许的HTTP方法
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD'];
  
  // 禁用的不安全方法
  const forbiddenMethods = ['TRACE', 'TRACK', 'CONNECT'];
  
  if (forbiddenMethods.includes(req.method)) {
    logger.warn('Forbidden HTTP method attempted', {
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date().toISOString(),
    });
    
    return res.status(405).json({
      code: 405,
      message: 'Method Not Allowed - This HTTP method is disabled for security reasons',
      data: {
        method: req.method,
        allowed_methods: allowedMethods,
      },
      timestamp: new Date().toISOString(),
    });
  }
  
  // 检查是否为允许的方法
  if (!allowedMethods.includes(req.method)) {
    logger.warn('Unsupported HTTP method attempted', {
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date().toISOString(),
    });
    
    return res.status(405).json({
      code: 405,
      message: 'Method Not Allowed',
      data: {
        method: req.method,
        allowed_methods: allowedMethods,
      },
      timestamp: new Date().toISOString(),
    });
  }
  
  next();
};
