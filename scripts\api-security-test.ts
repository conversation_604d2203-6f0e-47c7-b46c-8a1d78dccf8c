#!/usr/bin/env tsx

/**
 * API特定安全测试脚本
 * 专门测试音乐API的安全性
 */

import axios from 'axios';

class APISecurityTester {
  private baseUrl: string;
  private results: any[] = [];

  constructor(baseUrl: string = 'http://localhost:5678') {
    this.baseUrl = baseUrl;
  }

  private log(category: string, test: string, status: string, message: string, severity?: string) {
    const result = { category, test, status, message, severity };
    this.results.push(result);
    
    const statusIcon = { 'PASS': '✅', 'FAIL': '❌', 'WARNING': '⚠️', 'INFO': '📋' }[status];
    const severityColor = severity ? {
      'LOW': '\x1b[32m', 'MEDIUM': '\x1b[33m', 'HIGH': '\x1b[31m', 'CRITICAL': '\x1b[35m'
    }[severity] : '';
    const resetColor = '\x1b[0m';
    
    console.log(`${statusIcon} [${category}] ${severityColor}${test}${resetColor}: ${message}`);
  }

  private async request(method: string, path: string, data?: any, headers?: any) {
    try {
      return await axios({
        method,
        url: `${this.baseUrl}${path}`,
        data,
        headers: { 'User-Agent': 'APISecurityTester/1.0', ...headers },
        timeout: 10000,
        validateStatus: () => true
      });
    } catch (error) {
      return null;
    }
  }

  // 1. API端点枚举测试
  async testAPIEndpointEnumeration() {
    console.log('\n🔍 API端点枚举测试...');
    
    const endpoints = [
      '/api/v1/search',
      '/api/v1/songs/123/url',
      '/api/v1/songs/123/lyrics',
      '/api/v1/albums/123/picture',
      '/api/v1/unm/songs/123/url',
      '/api/v1/unm/info',
      '/api/v1/cache/stats',
      '/health',
      '/info',
      '/sources'
    ];

    for (const endpoint of endpoints) {
      const response = await this.request('GET', endpoint);
      if (response) {
        if (response.status === 200) {
          this.log('API枚举', `端点发现: ${endpoint}`, 'INFO', `端点可访问 (${response.status})`);
        } else if (response.status === 404) {
          this.log('API枚举', `端点测试: ${endpoint}`, 'PASS', '端点不存在或被正确保护');
        } else {
          this.log('API枚举', `端点测试: ${endpoint}`, 'INFO', `状态码: ${response.status}`);
        }
      }
    }
  }

  // 2. 参数注入测试
  async testParameterInjection() {
    console.log('\n🔍 参数注入测试...');
    
    // NoSQL注入测试
    const noSqlPayloads = [
      '{"$ne": null}',
      '{"$gt": ""}',
      '{"$where": "this.name == this.name"}',
      '{"$regex": ".*"}'
    ];

    for (const payload of noSqlPayloads) {
      const response = await this.request('GET', `/api/v1/search?source=netease&q=${encodeURIComponent(payload)}`);
      if (response && response.status === 200 && response.data.code === 200) {
        this.log('参数注入', 'NoSQL注入', 'WARNING', `可能存在NoSQL注入: ${payload}`, 'HIGH');
      } else {
        this.log('参数注入', 'NoSQL注入', 'PASS', 'NoSQL注入payload被正确处理');
      }
    }

    // 命令注入测试
    const cmdPayloads = [
      '; ls -la',
      '| whoami',
      '&& cat /etc/passwd',
      '`id`',
      '$(whoami)'
    ];

    for (const payload of cmdPayloads) {
      const response = await this.request('GET', `/api/v1/search?source=netease&q=${encodeURIComponent(payload)}`);
      if (response && response.data && typeof response.data === 'string' && 
          (response.data.includes('root:') || response.data.includes('uid=') || response.data.includes('total '))) {
        this.log('参数注入', '命令注入', 'FAIL', `可能存在命令注入: ${payload}`, 'CRITICAL');
      } else {
        this.log('参数注入', '命令注入', 'PASS', '命令注入payload被正确处理');
      }
    }
  }

  // 3. 业务逻辑测试
  async testBusinessLogic() {
    console.log('\n🔍 业务逻辑安全测试...');
    
    // 测试极大的limit值
    const response1 = await this.request('GET', '/api/v1/search?source=netease&q=test&limit=999999');
    if (response1 && response1.status === 200) {
      this.log('业务逻辑', '参数边界测试', 'WARNING', '可能允许过大的limit值，存在资源耗尽风险', 'MEDIUM');
    } else {
      this.log('业务逻辑', '参数边界测试', 'PASS', '大limit值被正确拒绝');
    }

    // 测试负数参数
    const response2 = await this.request('GET', '/api/v1/search?source=netease&q=test&limit=-1&offset=-1');
    if (response2 && response2.status === 200) {
      this.log('业务逻辑', '负数参数测试', 'WARNING', '可能允许负数参数', 'MEDIUM');
    } else {
      this.log('业务逻辑', '负数参数测试', 'PASS', '负数参数被正确拒绝');
    }

    // 测试空参数
    const response3 = await this.request('GET', '/api/v1/search?source=&q=');
    if (response3 && response3.status === 200) {
      this.log('业务逻辑', '空参数测试', 'WARNING', '可能允许空参数', 'LOW');
    } else {
      this.log('业务逻辑', '空参数测试', 'PASS', '空参数被正确拒绝');
    }
  }

  // 4. 缓存安全测试
  async testCacheSecurity() {
    console.log('\n🔍 缓存安全测试...');
    
    // 测试缓存键操作
    const maliciousKeys = [
      '../../../etc/passwd',
      'cache:admin:password',
      'user:1:session',
      '../../config'
    ];

    for (const key of maliciousKeys) {
      const response = await this.request('GET', `/api/v1/cache/item/${encodeURIComponent(key)}`);
      if (response && response.status === 200) {
        this.log('缓存安全', '缓存键操作', 'WARNING', `可能存在缓存键操作风险: ${key}`, 'MEDIUM');
      } else {
        this.log('缓存安全', '缓存键操作', 'PASS', '恶意缓存键被正确拒绝');
      }
    }

    // 测试缓存管理权限
    const response = await this.request('DELETE', '/api/v1/cache/all');
    if (response && response.status === 200) {
      this.log('缓存安全', '缓存管理权限', 'FAIL', '缓存管理接口无权限控制', 'HIGH');
    } else {
      this.log('缓存安全', '缓存管理权限', 'PASS', '缓存管理接口有适当保护');
    }
  }

  // 5. 文件上传安全测试
  async testFileUploadSecurity() {
    console.log('\n🔍 文件上传安全测试...');
    
    // 测试恶意文件上传
    const maliciousFiles = [
      { name: 'shell.php', content: '<?php system($_GET["cmd"]); ?>', type: 'application/x-php' },
      { name: 'script.js', content: 'alert("XSS")', type: 'application/javascript' },
      { name: 'test.exe', content: 'MZ\x90\x00', type: 'application/x-msdownload' }
    ];

    for (const file of maliciousFiles) {
      const formData = new FormData();
      formData.append('file', new Blob([file.content], { type: file.type }), file.name);
      
      const response = await this.request('POST', '/upload', formData, {
        'Content-Type': 'multipart/form-data'
      });
      
      if (response && response.status === 200) {
        this.log('文件上传', `恶意文件上传: ${file.name}`, 'FAIL', '恶意文件上传成功', 'HIGH');
      } else {
        this.log('文件上传', `恶意文件上传: ${file.name}`, 'PASS', '恶意文件被正确拒绝');
      }
    }
  }

  // 6. API版本安全测试
  async testAPIVersionSecurity() {
    console.log('\n🔍 API版本安全测试...');
    
    const versions = ['v0', 'v2', 'v1.1', 'beta', 'admin', 'internal'];
    
    for (const version of versions) {
      const response = await this.request('GET', `/api/${version}/search?source=netease&q=test`);
      if (response && response.status === 200) {
        this.log('API版本', `版本枚举: ${version}`, 'WARNING', `发现可访问的API版本: ${version}`, 'LOW');
      } else {
        this.log('API版本', `版本枚举: ${version}`, 'PASS', '未授权版本被正确拒绝');
      }
    }
  }

  // 7. 响应操作测试
  async testResponseManipulation() {
    console.log('\n🔍 响应操作测试...');
    
    // 测试JSONP回调注入
    const response1 = await this.request('GET', '/api/v1/search?source=netease&q=test&callback=alert(1)');
    if (response1 && response1.data && typeof response1.data === 'string' && response1.data.includes('alert(1)')) {
      this.log('响应操作', 'JSONP注入', 'FAIL', 'JSONP回调可能被注入', 'HIGH');
    } else {
      this.log('响应操作', 'JSONP注入', 'PASS', 'JSONP回调被正确处理');
    }

    // 测试内容类型操作
    const response2 = await this.request('GET', '/api/v1/search?source=netease&q=test', null, {
      'Accept': 'text/html,application/xhtml+xml'
    });
    if (response2 && response2.headers['content-type'] && response2.headers['content-type'].includes('text/html')) {
      this.log('响应操作', '内容类型操作', 'WARNING', '响应内容类型可能被操作', 'MEDIUM');
    } else {
      this.log('响应操作', '内容类型操作', 'PASS', '内容类型保持一致');
    }
  }

  // 8. 时序攻击测试
  async testTimingAttacks() {
    console.log('\n🔍 时序攻击测试...');
    
    const validId = '123456';
    const invalidId = '999999';
    
    // 测试有效ID的响应时间
    const start1 = Date.now();
    await this.request('GET', `/api/v1/songs/${validId}/url?source=netease`);
    const time1 = Date.now() - start1;
    
    // 测试无效ID的响应时间
    const start2 = Date.now();
    await this.request('GET', `/api/v1/songs/${invalidId}/url?source=netease`);
    const time2 = Date.now() - start2;
    
    const timeDiff = Math.abs(time1 - time2);
    if (timeDiff > 1000) { // 如果时间差超过1秒
      this.log('时序攻击', '响应时间分析', 'WARNING', `响应时间差异较大: ${timeDiff}ms`, 'LOW');
    } else {
      this.log('时序攻击', '响应时间分析', 'PASS', '响应时间相对一致');
    }
  }

  // 9. 资源耗尽测试
  async testResourceExhaustion() {
    console.log('\n🔍 资源耗尽测试...');
    
    // 测试大量并发请求
    const promises = [];
    for (let i = 0; i < 50; i++) {
      promises.push(this.request('GET', '/api/v1/search?source=netease&q=test'));
    }
    
    const start = Date.now();
    const responses = await Promise.all(promises);
    const duration = Date.now() - start;
    
    const successCount = responses.filter(r => r && r.status === 200).length;
    const rateLimitedCount = responses.filter(r => r && r.status === 429).length;
    
    if (rateLimitedCount === 0 && duration < 5000) {
      this.log('资源耗尽', '并发请求测试', 'WARNING', '可能缺乏足够的并发限制', 'MEDIUM');
    } else {
      this.log('资源耗尽', '并发请求测试', 'PASS', '并发请求被适当限制');
    }
    
    this.log('资源耗尽', '性能统计', 'INFO', `50个请求耗时${duration}ms，成功${successCount}个，限制${rateLimitedCount}个`);
  }

  // 10. 数据泄露测试
  async testDataLeakage() {
    console.log('\n🔍 数据泄露测试...');
    
    // 测试调试信息泄露
    const response1 = await this.request('GET', '/api/v1/search?source=netease&q=test&debug=1');
    if (response1 && response1.data && JSON.stringify(response1.data).includes('debug')) {
      this.log('数据泄露', '调试信息泄露', 'WARNING', '可能存在调试信息泄露', 'MEDIUM');
    } else {
      this.log('数据泄露', '调试信息泄露', 'PASS', '无调试信息泄露');
    }

    // 测试内部路径泄露
    const response2 = await this.request('GET', '/api/v1/nonexistent');
    if (response2 && response2.data && JSON.stringify(response2.data).match(/[A-Z]:\\/)) {
      this.log('数据泄露', '路径信息泄露', 'WARNING', '错误信息可能包含内部路径', 'LOW');
    } else {
      this.log('数据泄露', '路径信息泄露', 'PASS', '无内部路径泄露');
    }
  }

  // 生成详细报告
  generateDetailedReport() {
    console.log('\n📊 API安全测试详细报告');
    console.log('='.repeat(60));
    
    const categories = [...new Set(this.results.map(r => r.category))];
    
    for (const category of categories) {
      const categoryResults = this.results.filter(r => r.category === category);
      const pass = categoryResults.filter(r => r.status === 'PASS').length;
      const fail = categoryResults.filter(r => r.status === 'FAIL').length;
      const warning = categoryResults.filter(r => r.status === 'WARNING').length;
      const info = categoryResults.filter(r => r.status === 'INFO').length;
      
      console.log(`\n📋 ${category}:`);
      console.log(`  ✅ 通过: ${pass}  ❌ 失败: ${fail}  ⚠️  警告: ${warning}  📋 信息: ${info}`);
      
      const issues = categoryResults.filter(r => r.status === 'FAIL' || r.severity);
      if (issues.length > 0) {
        console.log(`  🚨 问题:`);
        issues.forEach(issue => {
          console.log(`    • ${issue.test}: ${issue.message}`);
        });
      }
    }

    const criticalIssues = this.results.filter(r => r.severity === 'CRITICAL');
    const highIssues = this.results.filter(r => r.severity === 'HIGH');
    const mediumIssues = this.results.filter(r => r.severity === 'MEDIUM');

    console.log(`\n🎯 安全评分:`);
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const score = Math.round((passedTests / totalTests) * 100);
    
    console.log(`  总体安全评分: ${score}/100`);
    if (criticalIssues.length > 0) console.log(`  🔴 严重问题: ${criticalIssues.length}`);
    if (highIssues.length > 0) console.log(`  🟠 高危问题: ${highIssues.length}`);
    if (mediumIssues.length > 0) console.log(`  🟡 中危问题: ${mediumIssues.length}`);

    return { score, criticalIssues, highIssues, mediumIssues, allResults: this.results };
  }

  // 运行所有API安全测试
  async runAllTests() {
    console.log('🔒 开始API安全渗透测试...');
    console.log(`🎯 目标: ${this.baseUrl}`);
    
    await this.testAPIEndpointEnumeration();
    await this.testParameterInjection();
    await this.testBusinessLogic();
    await this.testCacheSecurity();
    await this.testFileUploadSecurity();
    await this.testAPIVersionSecurity();
    await this.testResponseManipulation();
    await this.testTimingAttacks();
    await this.testResourceExhaustion();
    await this.testDataLeakage();

    return this.generateDetailedReport();
  }
}

// 运行测试
async function main() {
  const tester = new APISecurityTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { APISecurityTester };
