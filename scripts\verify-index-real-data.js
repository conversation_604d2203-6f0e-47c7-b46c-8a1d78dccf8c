#!/usr/bin/env node

/**
 * 验证index.html中的所有数据都是真实的API数据
 */

const fs = require('fs');
const path = require('path');

class IndexDataVerifier {
    constructor() {
        this.issues = [];
        this.indexPath = path.join(__dirname, '../public/index.html');
        this.mainJsPath = path.join(__dirname, '../public/assets/js/main.js');
    }

    /**
     * 运行验证
     */
    async verify() {
        console.log('🔍 验证index.html中的真实数据使用...\n');

        // 检查HTML中的硬编码数据
        await this.checkHtmlHardcodedData();
        
        // 检查JavaScript中的API调用
        await this.checkJavaScriptApiCalls();

        // 输出结果
        this.outputResults();
    }

    /**
     * 检查HTML中的硬编码数据
     */
    async checkHtmlHardcodedData() {
        const content = fs.readFileSync(this.indexPath, 'utf-8');
        
        console.log('📄 检查 index.html...');
        
        // 检查统计数据是否有硬编码值
        const hardcodedPatterns = [
            { pattern: /<span[^>]*id="supported-sources"[^>]*>(\d+)</g, message: '支持音源数量硬编码' },
            { pattern: /<span[^>]*id="core-apis"[^>]*>(\d+)</g, message: '核心API数量硬编码' },
            { pattern: /<span[^>]*id="service-availability"[^>]*>(\d+\.?\d*%)</g, message: '服务可用性硬编码' },
            { pattern: /<span[^>]*id="avg-response-time"[^>]*>([^<]+)</g, message: '平均响应时间硬编码' }
        ];

        hardcodedPatterns.forEach(({ pattern, message }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const value = match[1];
                if (value && value !== '...' && value !== '加载中' && value !== '加载中...') {
                    this.issues.push({
                        file: 'index.html',
                        type: 'HARDCODED_STAT',
                        message: `${message}: "${value}"`,
                        severity: 'HIGH'
                    });
                } else {
                    console.log(`  ✅ 统计数据使用占位符: ${value}`);
                }
            }
        });

        // 检查功能描述中的硬编码数字
        const descriptionPatterns = [
            { pattern: /支持多音源搜索.*?(\d+)个主流音乐平台/g, message: '功能描述中硬编码音源数量' },
            { pattern: /从(\d+)个支持的音源中选择/g, message: '快速开始中硬编码音源数量' }
        ];

        descriptionPatterns.forEach(({ pattern, message }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const value = match[1];
                this.issues.push({
                    file: 'index.html',
                    type: 'HARDCODED_DESC',
                    message: `${message}: "${value}"`,
                    severity: 'MEDIUM'
                });
            }
        });

        // 检查示例API调用中的硬编码ID
        const apiPatterns = [
            { pattern: /GET \/api[^{]*?(\d{6,})/g, message: '示例API中硬编码歌曲ID' }
        ];

        apiPatterns.forEach(({ pattern, message }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const value = match[1];
                this.issues.push({
                    file: 'index.html',
                    type: 'HARDCODED_API',
                    message: `${message}: "${value}"`,
                    severity: 'MEDIUM'
                });
            }
        });

        // 检查是否有动态更新的元素ID
        const dynamicElements = [
            'supported-sources',
            'core-apis', 
            'service-availability',
            'avg-response-time',
            'sources-count-text',
            'sources-count-text-2',
            'example-api-url'
        ];

        dynamicElements.forEach(elementId => {
            if (content.includes(`id="${elementId}"`)) {
                console.log(`  ✅ 找到动态元素: ${elementId}`);
            } else {
                this.issues.push({
                    file: 'index.html',
                    type: 'MISSING_DYNAMIC',
                    message: `缺少动态元素ID: ${elementId}`,
                    severity: 'HIGH'
                });
            }
        });
    }

    /**
     * 检查JavaScript中的API调用
     */
    async checkJavaScriptApiCalls() {
        const content = fs.readFileSync(this.mainJsPath, 'utf-8');
        
        console.log('📄 检查 main.js...');
        
        // 检查是否有真实的API调用
        const apiCalls = [
            '/info',
            '/health', 
            '/sources',
            '/api/v1/search'
        ];

        let foundApiCalls = 0;
        apiCalls.forEach(api => {
            if (content.includes(api)) {
                console.log(`  ✅ 找到API调用: ${api}`);
                foundApiCalls++;
            }
        });

        if (foundApiCalls < apiCalls.length) {
            this.issues.push({
                file: 'main.js',
                type: 'MISSING_API',
                message: `缺少API调用，只找到 ${foundApiCalls}/${apiCalls.length} 个`,
                severity: 'HIGH'
            });
        }

        // 检查是否有更新动态内容的方法
        const updateMethods = [
            'updateSourcesInfo',
            'updateStats',
            'updateExampleApiUrl',
            'loadRealTimeStats'
        ];

        updateMethods.forEach(method => {
            if (content.includes(method)) {
                console.log(`  ✅ 找到更新方法: ${method}`);
            } else {
                this.issues.push({
                    file: 'main.js',
                    type: 'MISSING_METHOD',
                    message: `缺少更新方法: ${method}`,
                    severity: 'MEDIUM'
                });
            }
        });

        // 检查是否有硬编码的数据
        const hardcodedPatterns = [
            { pattern: /textContent\s*=\s*['"`]\d+['"`]/g, message: '硬编码数字' },
            { pattern: /textContent\s*=\s*['"`]\d+\.\d+%['"`]/g, message: '硬编码百分比' }
        ];

        hardcodedPatterns.forEach(({ pattern, message }) => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    // 过滤掉合理的硬编码（如核心API数量4）
                    if (!match.includes('4') && !match.includes('0')) {
                        this.issues.push({
                            file: 'main.js',
                            type: 'HARDCODED_JS',
                            message: `${message}: ${match}`,
                            severity: 'MEDIUM'
                        });
                    }
                });
            }
        });
    }

    /**
     * 输出验证结果
     */
    outputResults() {
        console.log('\n📊 验证结果:');
        console.log('='.repeat(50));

        if (this.issues.length === 0) {
            console.log('🎉 太棒了！index.html中所有数据都是真实的API数据！');
            console.log('✅ 没有发现硬编码数据或模拟内容。');
            return;
        }

        // 按严重程度分组
        const groupedIssues = this.issues.reduce((groups, issue) => {
            if (!groups[issue.severity]) {
                groups[issue.severity] = [];
            }
            groups[issue.severity].push(issue);
            return groups;
        }, {});

        // 输出问题
        ['HIGH', 'MEDIUM', 'LOW'].forEach(severity => {
            if (groupedIssues[severity]) {
                console.log(`\n🚨 ${severity} 级别问题 (${groupedIssues[severity].length}个):`);
                groupedIssues[severity].forEach(issue => {
                    console.log(`  📁 ${issue.file}`);
                    console.log(`     ${issue.type}: ${issue.message}`);
                });
            }
        });

        console.log(`\n📈 总计发现 ${this.issues.length} 个问题需要修复。`);
        
        // 提供修复建议
        console.log('\n💡 修复建议:');
        console.log('- HIGH级别: 立即修复，确保所有数据都从真实API获取');
        console.log('- MEDIUM级别: 尽快修复，提升数据真实性');
        console.log('- LOW级别: 可选修复，优化用户体验');
    }
}

// 运行验证
const verifier = new IndexDataVerifier();
verifier.verify().catch(console.error);
