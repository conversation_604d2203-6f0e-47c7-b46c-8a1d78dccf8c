import rateLimit from 'express-rate-limit';
import { config } from './../config/environment';
import { sendRateLimitError } from './../utils/response';

// 频率限制配置
export const rateLimitMiddleware = rateLimit({
  windowMs: config.rateLimitWindow, // 时间窗口
  max: config.rateLimitMax * 3, // 增加最大请求数以支持前端页面加载
  message: {
    code: 429,
    message: config.rateLimitMessage,
    data: null,
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true, // 返回标准的 `RateLimit-*` 头
  legacyHeaders: false, // 禁用 `X-RateLimit-*` 头
  handler: (_req, res) => {
    sendRateLimitError(res);
  },
  skip: (req) => {
    // 跳过健康检查、根路径、静态资源和基础API的频率限制
    return req.path === '/health' ||
           req.path === '/' ||
           req.path === '/info' ||
           req.path === '/sources' ||
           req.path.startsWith('/assets/') ||
           req.path.endsWith('.css') ||
           req.path.endsWith('.js') ||
           req.path.endsWith('.html');
  },
});
