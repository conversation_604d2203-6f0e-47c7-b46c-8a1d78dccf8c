import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

/**
 * 安全监控中间件
 * 检测和记录可疑的安全活动
 */

// 可疑模式定义
const suspiciousPatterns = {
  // 路径遍历攻击
  pathTraversal: [
    /\.\.\//g,
    /\.\.\\/g,
    /\.\.%2f/gi,
    /\.\.%5c/gi,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
  ],
  
  // XSS攻击
  xss: [
    /<script[^>]*>/gi,
    /<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe[^>]*>/gi,
    /<object[^>]*>/gi,
    /<embed[^>]*>/gi,
  ],
  
  // SQL注入
  sqlInjection: [
    /union.*select/gi,
    /select.*from/gi,
    /insert.*into/gi,
    /delete.*from/gi,
    /update.*set/gi,
    /drop.*table/gi,
    /exec\s*\(/gi,
    /sp_executesql/gi,
    /xp_cmdshell/gi,
  ],
  
  // 命令注入
  commandInjection: [
    /;\s*(ls|dir|cat|type|whoami|id|pwd|uname)/gi,
    /\|\s*(ls|dir|cat|type|whoami|id|pwd|uname)/gi,
    /&&\s*(ls|dir|cat|type|whoami|id|pwd|uname)/gi,
    /`[^`]*`/g,
    /\$\([^)]*\)/g,
  ],
  
  // NoSQL注入
  nosqlInjection: [
    /\$ne\s*:/gi,
    /\$gt\s*:/gi,
    /\$lt\s*:/gi,
    /\$where\s*:/gi,
    /\$regex\s*:/gi,
    /\$or\s*:/gi,
    /\$and\s*:/gi,
  ],
  
  // LDAP注入
  ldapInjection: [
    /\(\|\(/gi,
    /\(\&\(/gi,
    /\(\!\(/gi,
    /\*\)\(/gi,
  ],
};

// 可疑文件扩展名
const suspiciousExtensions = [
  '.php', '.asp', '.aspx', '.jsp', '.cgi', '.pl', '.py', '.rb',
  '.exe', '.bat', '.cmd', '.sh', '.ps1', '.vbs', '.jar',
];

export const securityMonitor = (req: Request, res: Response, next: NextFunction): void | Response => {
  const startTime = Date.now();
  let suspiciousActivity = false;
  const detectedThreats: string[] = [];
  
  // 收集请求数据
  const requestData = {
    url: req.url,
    method: req.method,
    headers: req.headers,
    query: req.query,
    body: req.body,
    params: req.params,
  };
  
  const requestString = JSON.stringify(requestData);
  
  // 检查各种攻击模式
  for (const [threatType, patterns] of Object.entries(suspiciousPatterns)) {
    for (const pattern of patterns) {
      if (pattern.test(requestString)) {
        suspiciousActivity = true;
        detectedThreats.push(threatType);
        break;
      }
    }
  }
  
  // 检查可疑文件扩展名
  const urlPath = req.url.toLowerCase();
  const hasSuspiciousExtension = suspiciousExtensions.some(ext => 
    urlPath.includes(ext),
  );
  
  if (hasSuspiciousExtension) {
    suspiciousActivity = true;
    detectedThreats.push('suspicious_file_extension');
  }
  
  // 检查异常长的URL
  if (req.url.length > 2048) {
    suspiciousActivity = true;
    detectedThreats.push('oversized_url');
  }
  
  // 检查异常多的参数
  const paramCount = Object.keys(req.query).length + Object.keys(req.params).length;
  if (paramCount > 50) {
    suspiciousActivity = true;
    detectedThreats.push('excessive_parameters');
  }
  
  // 检查二进制数据
  if (req.body && typeof req.body === 'string') {
    const binaryPattern = /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\xFF]/;
    if (binaryPattern.test(req.body)) {
      suspiciousActivity = true;
      detectedThreats.push('binary_data');
    }
  }
  
  // 记录可疑活动
  if (suspiciousActivity) {
    logger.warn('Suspicious security activity detected', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      threats: detectedThreats,
      headers: req.headers,
      query: req.query,
      bodySize: req.body ? JSON.stringify(req.body).length : 0,
      timestamp: new Date().toISOString(),
    });
    
    // 如果配置为阻止可疑请求
    if (process.env.BLOCK_SUSPICIOUS_REQUESTS === 'true') {
      return res.status(403).json({
        code: 403,
        message: 'Forbidden - Suspicious activity detected',
        data: {
          threats: detectedThreats,
          reason: 'Security policy violation',
        },
        timestamp: new Date().toISOString(),
      });
    }
  }
  
  // 监控响应
  const originalSend = res.send;
  res.send = function(data) {
    const responseTime = Date.now() - startTime;
    
    // 记录响应信息
    if (config.logLevel === 'debug' || suspiciousActivity) {
      logger.info('Request completed', {
        ip: req.ip,
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        responseTime,
        suspicious: suspiciousActivity,
        threats: detectedThreats,
        timestamp: new Date().toISOString(),
      });
    }
    
    // 检查异常响应时间
    if (responseTime > 10000) { // 超过10秒
      logger.warn('Slow response detected', {
        ip: req.ip,
        url: req.url,
        method: req.method,
        responseTime,
        statusCode: res.statusCode,
        timestamp: new Date().toISOString(),
      });
    }
    
    return originalSend.call(this, data);
  };
  
  next();
};
