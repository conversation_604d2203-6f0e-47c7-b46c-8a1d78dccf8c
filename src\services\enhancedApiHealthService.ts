import axios from 'axios';
import { logger } from '../utils/logger';

/**
 * 熔断器状态
 */
enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

/**
 * 熔断器配置
 */
interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
}

/**
 * 熔断器实现
 */
class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private successCount: number = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (Date.now() - this.lastFailureTime > this.config.recoveryTimeout) {
        this.state = CircuitBreakerState.HALF_OPEN;
        this.successCount = 0;
        logger.info('Circuit breaker half-open, attempting recovery');
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 3) { // 连续3次成功后关闭熔断器
        this.state = CircuitBreakerState.CLOSED;
        logger.info('Circuit breaker closed after successful recovery');
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitBreakerState.OPEN;
      logger.warn('Circuit breaker opened due to failures', {
        failureCount: this.failureCount,
        threshold: this.config.failureThreshold,
      });
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  getStats() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime,
    };
  }
}

/**
 * API端点信息
 */
export interface ApiEndpoint {
  url: string;
  name: string;
  priority: number;
  timeout: number;
  retryCount: number;
}

/**
 * API健康状态
 */
export interface ApiHealthStatus {
  url: string;
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  averageResponseTime: number;
  lastChecked: Date;
  errorCount: number;
  successCount: number;
  successRate: number;
  healthScore: number;
  circuitBreakerState: CircuitBreakerState;
}

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  isHealthy: boolean;
  apis: ApiHealthStatus[];
  recommendedApi: ApiEndpoint | undefined;
  totalApis: number;
  healthyApis: number;
}

/**
 * 增强的API健康检查服务
 */
export class EnhancedApiHealthService {
  private static instance: EnhancedApiHealthService;
  private apiEndpoints: ApiEndpoint[] = [];
  private healthStatus: Map<string, ApiHealthStatus> = new Map();
  private responseTimeHistory: Map<string, number[]> = new Map();
  private circuitBreakers: Map<string, CircuitBreaker> = new Map();
  private checkInterval: NodeJS.Timeout | null = null;
  
  private readonly CHECK_INTERVAL = 30000; // 30秒检查一次
  private readonly MAX_HISTORY_SIZE = 10; // 保留最近10次响应时间
  private readonly CIRCUIT_BREAKER_CONFIG: CircuitBreakerConfig = {
    failureThreshold: 5,
    recoveryTimeout: 60000, // 1分钟恢复时间
    monitoringPeriod: 300000, // 5分钟监控周期
  };

  private constructor() {
    this.initializeApis();
    this.startHealthChecks();
  }

  public static getInstance(): EnhancedApiHealthService {
    if (!EnhancedApiHealthService.instance) {
      EnhancedApiHealthService.instance = new EnhancedApiHealthService();
    }
    return EnhancedApiHealthService.instance;
  }

  /**
   * 初始化API端点列表
   */
  private initializeApis(): void {
    this.apiEndpoints = [
      {
        url: 'https://music-api.gdstudio.xyz/api.php',
        name: 'GD Studio API',
        priority: 1,
        timeout: 5000,
        retryCount: 2,
      },
      {
        url: 'https://music.gdstudio.xyz/api.php',
        name: 'GD Studio Backup API',
        priority: 2,
        timeout: 5000,
        retryCount: 2,
      },
      // 可以添加更多API端点
    ];

    // 初始化健康状态和熔断器
    this.apiEndpoints.forEach(api => {
      this.healthStatus.set(api.url, {
        url: api.url,
        name: api.name,
        status: 'healthy',
        responseTime: 0,
        averageResponseTime: 0,
        lastChecked: new Date(),
        errorCount: 0,
        successCount: 0,
        successRate: 1.0,
        healthScore: 1.0,
        circuitBreakerState: CircuitBreakerState.CLOSED,
      });

      this.responseTimeHistory.set(api.url, []);
      this.circuitBreakers.set(api.url, new CircuitBreaker(this.CIRCUIT_BREAKER_CONFIG));
    });

    logger.info('Enhanced API health service initialized', {
      apiCount: this.apiEndpoints.length,
      checkInterval: this.CHECK_INTERVAL,
    });
  }

  /**
   * 开始健康检查
   */
  private startHealthChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    // 立即执行一次检查
    this.performHealthCheck();

    // 设置定期检查
    this.checkInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.CHECK_INTERVAL);

    logger.info('API health checks started');
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const checkPromises = this.apiEndpoints.map(api => this.checkApiHealth(api));
    await Promise.allSettled(checkPromises);
    
    // 更新推荐API
    this.updateRecommendedApi();
  }

  /**
   * 检查单个API的健康状态
   */
  private async checkApiHealth(api: ApiEndpoint): Promise<void> {
    const startTime = Date.now();
    const circuitBreaker = this.circuitBreakers.get(api.url)!;

    try {
      await circuitBreaker.execute(async () => {
        const response = await axios.get(api.url, {
          timeout: api.timeout,
          params: { types: 'search', source: 'netease', name: 'test', count: 1 },
        });

        if (response.status !== 200) {
          throw new Error(`HTTP ${response.status}`);
        }
      });

      // 成功情况
      const responseTime = Date.now() - startTime;
      this.updateSuccessMetrics(api.url, responseTime);

    } catch (error) {
      // 失败情况
      const responseTime = Date.now() - startTime;
      this.updateFailureMetrics(api.url, responseTime, error);
    }
  }

  /**
   * 更新成功指标
   */
  private updateSuccessMetrics(url: string, responseTime: number): void {
    const status = this.healthStatus.get(url)!;
    const history = this.responseTimeHistory.get(url)!;
    const circuitBreaker = this.circuitBreakers.get(url)!;

    // 更新响应时间历史
    history.push(responseTime);
    if (history.length > this.MAX_HISTORY_SIZE) {
      history.shift();
    }

    // 计算平均响应时间
    const averageResponseTime = history.reduce((sum, time) => sum + time, 0) / history.length;

    // 更新状态
    status.responseTime = responseTime;
    status.averageResponseTime = averageResponseTime;
    status.lastChecked = new Date();
    status.successCount++;
    status.successRate = status.successCount / (status.successCount + status.errorCount);
    status.circuitBreakerState = circuitBreaker.getState();

    // 计算健康分数
    status.healthScore = this.calculateHealthScore(status);

    // 确定状态
    if (status.healthScore >= 0.8) {
      status.status = 'healthy';
    } else if (status.healthScore >= 0.5) {
      status.status = 'degraded';
    } else {
      status.status = 'unhealthy';
    }

    logger.debug('API health check success', {
      url,
      responseTime,
      averageResponseTime,
      healthScore: status.healthScore,
      status: status.status,
    });
  }

  /**
   * 更新失败指标
   */
  private updateFailureMetrics(url: string, responseTime: number, error: any): void {
    const status = this.healthStatus.get(url)!;
    const circuitBreaker = this.circuitBreakers.get(url)!;

    status.responseTime = responseTime;
    status.lastChecked = new Date();
    status.errorCount++;
    status.successRate = status.successCount / (status.successCount + status.errorCount);
    status.circuitBreakerState = circuitBreaker.getState();

    // 计算健康分数
    status.healthScore = this.calculateHealthScore(status);
    status.status = 'unhealthy';

    logger.warn('API health check failed', {
      url,
      error: error.message,
      errorCount: status.errorCount,
      successRate: status.successRate,
      circuitBreakerState: status.circuitBreakerState,
    });
  }

  /**
   * 计算健康分数
   */
  private calculateHealthScore(status: ApiHealthStatus): number {
    let score = status.successRate;

    // 响应时间影响
    if (status.averageResponseTime > 0) {
      const responseTimeFactor = Math.max(0, 1 - (status.averageResponseTime - 1000) / 5000);
      score *= responseTimeFactor;
    }

    // 熔断器状态影响
    if (status.circuitBreakerState === CircuitBreakerState.OPEN) {
      score *= 0.1;
    } else if (status.circuitBreakerState === CircuitBreakerState.HALF_OPEN) {
      score *= 0.5;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * 更新推荐API
   */
  private updateRecommendedApi(): void {
    const healthyApis = this.apiEndpoints
      .filter(api => {
        const status = this.healthStatus.get(api.url);
        return status && status.status !== 'unhealthy';
      })
      .sort((a, b) => {
        const statusA = this.healthStatus.get(a.url)!;
        const statusB = this.healthStatus.get(b.url)!;
        
        // 优先按健康分数排序
        if (statusB.healthScore !== statusA.healthScore) {
          return statusB.healthScore - statusA.healthScore;
        }
        
        // 然后按优先级排序
        return a.priority - b.priority;
      });

    if (healthyApis.length > 0) {
      const recommended = healthyApis[0];
      if (recommended) {
        logger.debug('Recommended API updated', {
          url: recommended.url,
          name: recommended.name,
          healthScore: this.healthStatus.get(recommended.url)?.healthScore,
        });
      }
    }
  }

  /**
   * 获取推荐的API
   */
  getRecommendedApi(): string {
    const healthyApis = this.apiEndpoints
      .filter(api => {
        const status = this.healthStatus.get(api.url);
        return status && status.status !== 'unhealthy';
      })
      .sort((a, b) => {
        const statusA = this.healthStatus.get(a.url)!;
        const statusB = this.healthStatus.get(b.url)!;
        return statusB.healthScore - statusA.healthScore;
      });

    return healthyApis.length > 0 ? healthyApis[0]?.url || this.apiEndpoints[0]?.url || '' : this.apiEndpoints[0]?.url || '';
  }

  /**
   * 获取健康检查结果
   */
  getHealthCheckResult(): HealthCheckResult {
    const apis = Array.from(this.healthStatus.values());
    const healthyApis = apis.filter(api => api.status === 'healthy');
    const recommendedApiUrl = this.getRecommendedApi();
    const recommendedApi = this.apiEndpoints.find(api => api.url === recommendedApiUrl);

    return {
      isHealthy: healthyApis.length > 0,
      apis,
      recommendedApi,
      totalApis: apis.length,
      healthyApis: healthyApis.length,
    };
  }

  /**
   * 手动触发健康检查
   */
  async triggerHealthCheck(): Promise<HealthCheckResult> {
    await this.performHealthCheck();
    return this.getHealthCheckResult();
  }

  /**
   * 停止健康检查
   */
  stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      logger.info('API health checks stopped');
    }
  }
}

// 创建全局实例
export const enhancedApiHealthService = EnhancedApiHealthService.getInstance();
