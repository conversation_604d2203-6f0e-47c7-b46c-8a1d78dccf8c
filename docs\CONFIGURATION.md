# 🔧 配置文档

本文档详细说明了 UNM API Server 的所有配置选项。所有配置都通过环境变量进行设置，**禁止硬编码**。

## 📋 配置文件

项目使用 `.env` 文件进行配置。请复制 `.env.complete` 文件到 `.env` 并根据需要修改配置。

```bash
cp .env.complete .env
```

## 🏗️ 服务器配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `NODE_ENV` | `development` | string | 运行环境 (development/production/test) |
| `PORT` | `5678` | number | 服务器监听端口 |
| `SERVER_NAME` | `UNM API Server` | string | 服务器名称 |
| `SERVER_VERSION` | `1.0.0` | string | 服务器版本 |
| `SERVER_AUTHOR` | `UNM API Server Contributors` | string | 服务器作者 |
| `SERVER_DESCRIPTION` | `UnblockNeteaseMusic API Server - Modern RESTful music API service` | string | 服务器描述 |

## 🌐 外部API配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `MUSIC_API_URL` | `https://music-api.gdstudio.xyz/api.php` | string | 外部音乐API地址 |
| `HTTP_TIMEOUT` | `10000` | number | HTTP请求超时时间(毫秒) |

## 🤖 User-Agent配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `USER_AGENTS` | 多个User-Agent字符串 | string | 用`|`分隔的User-Agent列表 |

## 🔗 CORS配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `CORS_ORIGIN` | `*` | string | 允许的源 |
| `CORS_METHODS` | `GET,POST,PUT,DELETE,OPTIONS` | string | 允许的HTTP方法 |
| `CORS_HEADERS` | `Content-Type,Authorization,X-Requested-With` | string | 允许的请求头 |

## 💾 缓存配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `CACHE_TTL` | `300` | number | 默认缓存TTL(秒) |
| `ENABLE_CACHE` | `true` | boolean | 是否启用缓存 |
| `CACHE_MAX_SIZE` | `1000` | number | 缓存最大项目数 |
| `CACHE_CLEANUP_INTERVAL` | `300000` | number | 缓存清理间隔(毫秒) |
| `CACHE_SEARCH_TTL` | `600` | number | 搜索缓存TTL(秒) |
| `CACHE_MUSIC_URL_TTL` | `1800` | number | 音乐URL缓存TTL(秒) |
| `CACHE_LYRICS_TTL` | `3600` | number | 歌词缓存TTL(秒) |
| `CACHE_PICTURE_TTL` | `86400` | number | 图片缓存TTL(秒) |
| `CACHE_UNM_TTL` | `1200` | number | UNM缓存TTL(秒) |

## 🚦 频率限制配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `RATE_LIMIT_WINDOW` | `900000` | number | 频率限制时间窗口(毫秒) |
| `RATE_LIMIT_MAX` | `100` | number | 时间窗口内最大请求数 |
| `RATE_LIMIT_MESSAGE` | `Too many requests, please try again later` | string | 频率限制错误消息 |

## 📝 日志配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `LOG_LEVEL` | `info` | string | 日志级别 (debug/info/warn/error) |
| `LOG_FILE` | `` | string | 日志文件路径(空则只输出到控制台) |

## 🎵 音乐服务配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `DEFAULT_SOURCE` | `netease` | string | 默认音源 |
| `SUPPORTED_SOURCES` | `netease,qq,xiami,baidu,kugou,kuwo,migu,joox,youtube,bilibili,pyncmd` | string | 支持的音源列表 |
| `STABLE_SOURCES` | `netease,qq,pyncmd` | string | 稳定音源列表 |

## 🔌 API配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `API_VERSION` | `v1` | string | API版本 |
| `API_BASE_PATH` | `/api` | string | API基础路径 |
| `MAX_SEARCH_LIMIT` | `100` | number | 最大搜索限制 |
| `MAX_BATCH_SIZE` | `50` | number | 最大批处理大小 |
| `DEFAULT_SEARCH_LIMIT` | `20` | number | 默认搜索限制 |
| `DEFAULT_SEARCH_OFFSET` | `0` | number | 默认搜索偏移 |

## 🎧 UNM配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `UNM_DEFAULT_SOURCES` | `pyncmd,netease,qq` | string | UNM默认音源 |
| `UNM_SUPPORTED_QUALITIES` | `128,192,320,740,999` | string | UNM支持的音质 |
| `UNM_DEFAULT_QUALITY` | `999` | number | UNM默认音质 |
| `UNM_SUPPORTED_SIZES` | `300,500` | string | UNM支持的图片尺寸 |
| `UNM_DEFAULT_SIZE` | `300` | number | UNM默认图片尺寸 |

## 🔒 安全配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `HELMET_ENABLED` | `true` | boolean | 是否启用Helmet安全中间件 |
| `REQUEST_SIZE_LIMIT` | `10mb` | string | 请求体大小限制 |

## 📁 文件配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `UPLOAD_MAX_SIZE` | `10485760` | number | 上传文件最大大小(字节) |
| `UPLOAD_ALLOWED_TYPES` | `image/jpeg,image/png,image/gif` | string | 允许的上传文件类型 |
| `STATIC_PATH` | `/public` | string | 静态文件路径 |
| `STATIC_MAX_AGE` | `86400000` | number | 静态文件缓存时间(毫秒) |

## 🏥 健康检查配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `HEALTH_CHECK_INTERVAL` | `30000` | number | 健康检查间隔(毫秒) |
| `HEALTH_CHECK_TIMEOUT` | `5000` | number | 健康检查超时(毫秒) |

## 🔥 预热配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `WARMUP_QUERIES` | `示例歌手1,示例歌手2,示例歌手3` | string | 预热查询关键词（示例格式） |
| `WARMUP_ENABLED` | `false` | boolean | 是否启用缓存预热 |

## ✅ 验证配置

| 环境变量 | 默认值 | 类型 | 描述 |
|---------|--------|------|------|
| `MIN_SEARCH_LENGTH` | `1` | number | 最小搜索长度 |
| `MAX_SEARCH_LENGTH` | `100` | number | 最大搜索长度 |
| `MIN_ID_LENGTH` | `1` | number | 最小ID长度 |
| `MAX_ID_LENGTH` | `50` | number | 最大ID长度 |
| `ID_PATTERN` | `^[a-zA-Z0-9_-]+$` | string | ID格式正则表达式 |
| `NUMERIC_ID_PATTERN` | `^[0-9]+$` | string | 数字ID格式正则表达式 |

## 📝 错误消息配置

所有错误消息都可以通过环境变量自定义：

- `ERROR_INVALID_PARAMS` - 无效参数错误
- `ERROR_MISSING_PARAM` - 缺少参数错误
- `ERROR_INVALID_SOURCE` - 无效音源错误
- `ERROR_INVALID_QUALITY` - 无效音质错误
- `ERROR_INVALID_SIZE` - 无效尺寸错误
- `ERROR_SONG_NOT_FOUND` - 歌曲未找到错误
- `ERROR_URL_NOT_FOUND` - URL未找到错误
- `ERROR_PICTURE_NOT_FOUND` - 图片未找到错误
- `ERROR_LYRICS_NOT_FOUND` - 歌词未找到错误
- `ERROR_SERVICE_UNAVAILABLE` - 服务不可用错误
- `ERROR_TIMEOUT` - 超时错误
- `ERROR_NETWORK_ERROR` - 网络错误
- `ERROR_UNKNOWN` - 未知错误

## 🎉 成功消息配置

所有成功消息都可以通过环境变量自定义：

- `SUCCESS_DEFAULT` - 默认成功消息
- `SUCCESS_SEARCH_COMPLETED` - 搜索完成消息
- `SUCCESS_URL_RETRIEVED` - URL获取成功消息
- `SUCCESS_PICTURE_RETRIEVED` - 图片获取成功消息
- `SUCCESS_LYRICS_RETRIEVED` - 歌词获取成功消息
- `SUCCESS_CACHE_CLEARED` - 缓存清理成功消息
- `SUCCESS_WARMUP_COMPLETED` - 预热完成消息

## 🔧 配置验证

运行配置验证脚本来检查配置是否正确：

```bash
npm run validate-config
```

或者：

```bash
npx tsx scripts/validate-config.ts
```

## 📚 最佳实践

1. **环境分离**: 为不同环境(开发/测试/生产)使用不同的配置文件
2. **敏感信息**: 将敏感信息(如API密钥)存储在环境变量中，不要提交到版本控制
3. **配置验证**: 定期运行配置验证脚本确保配置正确
4. **文档更新**: 添加新配置项时及时更新此文档
5. **默认值**: 为所有配置项提供合理的默认值
6. **类型安全**: 使用TypeScript确保配置类型安全

## 🚨 注意事项

- **禁止硬编码**: 所有配置值必须从环境变量获取，禁止在代码中硬编码
- **配置优先级**: 环境变量 > .env文件 > 默认值
- **重启要求**: 修改配置后需要重启服务器才能生效
- **安全考虑**: 生产环境中不要使用默认的安全配置
