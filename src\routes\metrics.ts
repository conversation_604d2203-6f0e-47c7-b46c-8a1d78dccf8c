import { Router, Request, Response } from 'express';
import { metricsCollector } from '../utils/metricsCollector';
import { enhancedCacheService } from '../services/enhancedCacheService';
import { enhancedApiHealthService } from '../services/enhancedApiHealthService';
import { logger } from '../utils/logger';

const router = Router();

/**
 * 获取所有指标摘要
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const timeWindow = req.query.window ? parseInt(req.query.window as string) : undefined;
    
    // 获取各种指标
    const metrics = metricsCollector.getMetricsSummary();
    const aggregated = metricsCollector.getAggregatedMetrics(timeWindow);
    const cacheStats = enhancedCacheService.getStats();
    const cacheHealth = await enhancedCacheService.healthCheck();
    const apiHealth = enhancedApiHealthService.getHealthCheckResult();

    res.json({
      code: 200,
      message: 'Metrics retrieved successfully',
      data: {
        timestamp: new Date().toISOString(),
        timeWindow: timeWindow || 'all',
        metrics: {
          ...metrics,
          aggregated
        },
        cache: {
          stats: cacheStats,
          health: cacheHealth,
          size: enhancedCacheService.getSize()
        },
        api: {
          health: apiHealth,
          recommended: enhancedApiHealthService.getRecommendedApi()
        },
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          version: process.version,
          platform: process.platform,
          arch: process.arch
        }
      },
      timestamp: new Date().toISOString()
    });

    logger.debug('Metrics endpoint accessed', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timeWindow
    });

  } catch (error) {
    logger.error('Error retrieving metrics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to retrieve metrics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取聚合指标
 */
router.get('/metrics/aggregated', (req: Request, res: Response) => {
  try {
    const timeWindow = req.query.window ? parseInt(req.query.window as string) : undefined;
    const aggregated = metricsCollector.getAggregatedMetrics(timeWindow);

    res.json({
      code: 200,
      message: 'Aggregated metrics retrieved successfully',
      data: aggregated,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error retrieving aggregated metrics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to retrieve aggregated metrics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取端点统计信息
 */
router.get('/metrics/endpoints', (_req: Request, res: Response) => {
  try {
    const endpointStats = metricsCollector.getEndpointStats();

    res.json({
      code: 200,
      message: 'Endpoint statistics retrieved successfully',
      data: {
        endpoints: endpointStats,
        totalEndpoints: endpointStats.length,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error retrieving endpoint statistics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to retrieve endpoint statistics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取系统指标
 */
router.get('/metrics/system', (_req: Request, res: Response) => {
  try {
    const systemMetrics = metricsCollector.getSystemMetrics();

    res.json({
      code: 200,
      message: 'System metrics retrieved successfully',
      data: {
        current: systemMetrics,
        process: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          version: process.version,
          platform: process.platform,
          arch: process.arch,
          pid: process.pid
        },
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error retrieving system metrics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to retrieve system metrics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取缓存指标
 */
router.get('/metrics/cache', async (_req: Request, res: Response) => {
  try {
    const cacheStats = enhancedCacheService.getStats();
    const cacheHealth = await enhancedCacheService.healthCheck();
    const cacheSize = enhancedCacheService.getSize();
    const cacheKeys = enhancedCacheService.getKeys();

    res.json({
      code: 200,
      message: 'Cache metrics retrieved successfully',
      data: {
        stats: cacheStats,
        health: cacheHealth,
        size: cacheSize,
        keys: {
          total: cacheKeys.length,
          sample: cacheKeys.slice(0, 10) // 只显示前10个键作为示例
        },
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error retrieving cache metrics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to retrieve cache metrics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取API健康状态
 */
router.get('/metrics/api-health', async (_req: Request, res: Response) => {
  try {
    const healthResult = enhancedApiHealthService.getHealthCheckResult();
    const recommendedApi = enhancedApiHealthService.getRecommendedApi();

    res.json({
      code: 200,
      message: 'API health metrics retrieved successfully',
      data: {
        ...healthResult,
        recommendedApi,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error retrieving API health metrics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to retrieve API health metrics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 重置指标
 */
router.post('/metrics/reset', (req: Request, res: Response) => {
  try {
    metricsCollector.reset();
    enhancedCacheService.resetStats();

    res.json({
      code: 200,
      message: 'Metrics reset successfully',
      data: {
        resetTime: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });

    logger.info('Metrics reset', {
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

  } catch (error) {
    logger.error('Error resetting metrics', { error });
    
    res.status(500).json({
      code: 500,
      message: 'Failed to reset metrics',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
