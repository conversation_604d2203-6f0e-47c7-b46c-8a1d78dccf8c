#!/usr/bin/env tsx

/**
 * 环境配置文件对比脚本
 * 比较 .env, .env.example, .env.complete 三个文件的一致性
 */

import * as fs from 'fs';
import * as path from 'path';

interface EnvVar {
  key: string;
  value: string;
  hasValue: boolean;
  lineNumber: number;
}

function parseEnvFile(filePath: string): Map<string, EnvVar> {
  const envVars = new Map<string, EnvVar>();
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return envVars;
  }
  
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    // 跳过注释和空行
    if (line.trim().startsWith('#') || line.trim() === '') {
      return;
    }
    
    if (line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=');
      
      envVars.set(key, {
        key,
        value,
        hasValue: value.trim() !== '',
        lineNumber: index + 1
      });
    }
  });
  
  return envVars;
}

function compareEnvFiles(): void {
  console.log('🔍 开始比较环境配置文件...\n');
  
  const envFile = parseEnvFile('.env');
  const exampleFile = parseEnvFile('.env.example');
  const completeFile = parseEnvFile('.env.complete');
  
  // 收集所有键
  const allKeys = new Set<string>();
  envFile.forEach((_, key) => allKeys.add(key));
  exampleFile.forEach((_, key) => allKeys.add(key));
  completeFile.forEach((_, key) => allKeys.add(key));
  
  console.log(`📊 总配置项数量: ${allKeys.size}\n`);
  
  // 检查一致性
  let missingInEnv = 0;
  let missingInExample = 0;
  let missingInComplete = 0;
  let valueDifferences = 0;
  
  const sortedKeys = Array.from(allKeys).sort();
  
  console.log('📋 配置项对比:\n');
  console.log('键名'.padEnd(30) + ' | .env | .example | .complete | 状态');
  console.log('-'.repeat(70));
  
  sortedKeys.forEach(key => {
    const inEnv = envFile.has(key);
    const inExample = exampleFile.has(key);
    const inComplete = completeFile.has(key);
    
    const envStatus = inEnv ? '✅' : '❌';
    const exampleStatus = inExample ? '✅' : '❌';
    const completeStatus = inComplete ? '✅' : '❌';
    
    let status = '✅ 完整';
    
    if (!inEnv) {
      missingInEnv++;
      status = '❌ 缺少.env';
    } else if (!inExample) {
      missingInExample++;
      status = '❌ 缺少.example';
    } else if (!inComplete) {
      missingInComplete++;
      status = '❌ 缺少.complete';
    }
    
    // 检查值的差异（仅对于有值的配置项）
    if (inEnv && inExample && inComplete) {
      const envVar = envFile.get(key)!;
      const exampleVar = exampleFile.get(key)!;
      const completeVar = completeFile.get(key)!;
      
      // 如果example或complete有值，但与env不同，标记为差异
      if (envVar.hasValue && exampleVar.hasValue && envVar.value !== exampleVar.value) {
        status = '⚠️  值不同(.example)';
        valueDifferences++;
      } else if (envVar.hasValue && completeVar.hasValue && envVar.value !== completeVar.value) {
        status = '⚠️  值不同(.complete)';
        valueDifferences++;
      }
    }
    
    console.log(
      key.padEnd(30) + 
      ' | ' + envStatus.padEnd(4) + 
      ' | ' + exampleStatus.padEnd(8) + 
      ' | ' + completeStatus.padEnd(9) + 
      ' | ' + status
    );
  });
  
  console.log('\n📊 对比结果:');
  console.log(`  总配置项: ${allKeys.size}`);
  console.log(`  .env 缺失: ${missingInEnv}`);
  console.log(`  .env.example 缺失: ${missingInExample}`);
  console.log(`  .env.complete 缺失: ${missingInComplete}`);
  console.log(`  值差异: ${valueDifferences}`);
  
  // 详细报告缺失项
  if (missingInEnv > 0) {
    console.log('\n❌ .env 中缺失的配置项:');
    sortedKeys.forEach(key => {
      if (!envFile.has(key)) {
        const exampleValue = exampleFile.get(key)?.value || '';
        const completeValue = completeFile.get(key)?.value || '';
        console.log(`  - ${key}=${exampleValue || completeValue}`);
      }
    });
  }
  
  if (missingInExample > 0) {
    console.log('\n❌ .env.example 中缺失的配置项:');
    sortedKeys.forEach(key => {
      if (!exampleFile.has(key)) {
        const envValue = envFile.get(key)?.value || '';
        console.log(`  - ${key}=${envValue}`);
      }
    });
  }
  
  if (missingInComplete > 0) {
    console.log('\n❌ .env.complete 中缺失的配置项:');
    sortedKeys.forEach(key => {
      if (!completeFile.has(key)) {
        const envValue = envFile.get(key)?.value || '';
        console.log(`  - ${key}=${envValue}`);
      }
    });
  }
  
  // 总结
  const totalIssues = missingInEnv + missingInExample + missingInComplete + valueDifferences;
  
  if (totalIssues === 0) {
    console.log('\n🎉 所有环境配置文件完全一致！');
  } else {
    console.log(`\n⚠️  发现 ${totalIssues} 个不一致问题，建议修复`);
  }
  
  console.log('\n💡 建议:');
  console.log('  1. .env.example 应包含所有配置项，但值为示例值');
  console.log('  2. .env.complete 应包含所有配置项，值为完整的生产环境配置');
  console.log('  3. .env 应包含当前环境的实际配置值');
  console.log('  4. 运行 npm run fix-env-format 确保格式正确');
}

function main() {
  compareEnvFiles();
}

if (require.main === module) {
  main();
}

export { compareEnvFiles, parseEnvFile };
