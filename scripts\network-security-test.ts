#!/usr/bin/env tsx

/**
 * 网络层安全测试脚本
 * 测试网络协议、TLS配置、端口安全等
 */

import axios from 'axios';
import { spawn } from 'child_process';
import { promisify } from 'util';

class NetworkSecurityTester {
  private baseUrl: string;
  private host: string;
  private port: number;
  private results: any[] = [];

  constructor(baseUrl: string = 'http://localhost:5678') {
    this.baseUrl = baseUrl;
    const url = new URL(baseUrl);
    this.host = url.hostname;
    this.port = parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80);
  }

  private log(category: string, test: string, status: string, message: string, severity?: string) {
    const result = { category, test, status, message, severity };
    this.results.push(result);
    
    const statusIcon = { 'PASS': '✅', 'FAIL': '❌', 'WARNING': '⚠️', 'INFO': '📋' }[status];
    const severityColor = severity ? {
      'LOW': '\x1b[32m', 'MEDIUM': '\x1b[33m', 'HIGH': '\x1b[31m', 'CRITICAL': '\x1b[35m'
    }[severity] : '';
    const resetColor = '\x1b[0m';
    
    console.log(`${statusIcon} [${category}] ${severityColor}${test}${resetColor}: ${message}`);
  }

  private async execCommand(command: string, args: string[]): Promise<string> {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { timeout: 10000 });
      let output = '';
      let error = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(error || `Command failed with code ${code}`));
        }
      });

      process.on('error', (err) => {
        reject(err);
      });
    });
  }

  // 1. 端口扫描测试
  async testPortScanning() {
    console.log('\n🔍 端口扫描测试...');
    
    const commonPorts = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3000, 3306, 5432, 6379, 8080, 8443];
    const openPorts = [];

    for (const port of commonPorts) {
      try {
        const response = await axios.get(`http://${this.host}:${port}`, { timeout: 2000 });
        openPorts.push(port);
        this.log('端口扫描', `端口${port}`, 'WARNING', '端口开放', 'LOW');
      } catch (error) {
        // 端口关闭或无响应
      }
    }

    if (openPorts.length > 1) {
      this.log('端口扫描', '开放端口统计', 'WARNING', `发现${openPorts.length}个开放端口: ${openPorts.join(', ')}`, 'MEDIUM');
    } else {
      this.log('端口扫描', '开放端口统计', 'PASS', '端口暴露面较小');
    }
  }

  // 2. HTTP协议安全测试
  async testHTTPProtocolSecurity() {
    console.log('\n🔍 HTTP协议安全测试...');
    
    try {
      // 测试HTTP/1.0支持
      const response1 = await axios.get(this.baseUrl, {
        headers: { 'Connection': 'close' },
        httpVersion: '1.0'
      });
      this.log('HTTP协议', 'HTTP/1.0支持', 'WARNING', 'HTTP/1.0协议被支持，可能存在安全风险', 'LOW');
    } catch (error) {
      this.log('HTTP协议', 'HTTP/1.0支持', 'PASS', 'HTTP/1.0协议被正确拒绝');
    }

    // 测试HTTP管道化
    try {
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(axios.get(this.baseUrl, { 
          headers: { 'Connection': 'keep-alive' },
          timeout: 5000 
        }));
      }
      await Promise.all(promises);
      this.log('HTTP协议', 'HTTP管道化', 'INFO', 'HTTP管道化正常工作');
    } catch (error) {
      this.log('HTTP协议', 'HTTP管道化', 'WARNING', 'HTTP管道化可能存在问题', 'LOW');
    }

    // 测试大请求头
    try {
      const largeHeader = 'x'.repeat(8192);
      await axios.get(this.baseUrl, {
        headers: { 'X-Large-Header': largeHeader },
        timeout: 5000
      });
      this.log('HTTP协议', '大请求头处理', 'WARNING', '可能允许过大的请求头', 'MEDIUM');
    } catch (error) {
      this.log('HTTP协议', '大请求头处理', 'PASS', '大请求头被正确拒绝');
    }
  }

  // 3. TLS/SSL安全测试
  async testTLSSecurity() {
    console.log('\n🔍 TLS/SSL安全测试...');
    
    if (this.baseUrl.startsWith('https://')) {
      try {
        // 测试TLS版本
        const tlsVersions = ['TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3'];
        for (const version of tlsVersions) {
          try {
            await axios.get(this.baseUrl, {
              httpsAgent: new (require('https').Agent)({
                secureProtocol: version.replace('.', '_') + '_method'
              })
            });
            if (version === 'TLSv1' || version === 'TLSv1.1') {
              this.log('TLS安全', `${version}支持`, 'FAIL', `不安全的TLS版本被支持: ${version}`, 'HIGH');
            } else {
              this.log('TLS安全', `${version}支持`, 'PASS', `安全的TLS版本: ${version}`);
            }
          } catch (error) {
            if (version === 'TLSv1' || version === 'TLSv1.1') {
              this.log('TLS安全', `${version}支持`, 'PASS', `不安全的TLS版本被正确拒绝: ${version}`);
            }
          }
        }
      } catch (error) {
        this.log('TLS安全', 'TLS配置测试', 'INFO', 'TLS配置测试失败，可能是HTTP服务');
      }
    } else {
      this.log('TLS安全', 'HTTPS支持', 'WARNING', '服务未启用HTTPS', 'MEDIUM');
    }
  }

  // 4. DNS安全测试
  async testDNSSecurity() {
    console.log('\n🔍 DNS安全测试...');
    
    try {
      // 测试DNS解析
      const { exec } = require('child_process');
      const promisifiedExec = promisify(exec);
      
      const result = await promisifiedExec(`nslookup ${this.host}`);
      if (result.stdout.includes('Non-authoritative answer')) {
        this.log('DNS安全', 'DNS解析', 'PASS', 'DNS解析正常');
      }
      
      // 检查DNS记录类型
      const recordTypes = ['A', 'AAAA', 'MX', 'TXT', 'CNAME'];
      for (const type of recordTypes) {
        try {
          const dnsResult = await promisifiedExec(`nslookup -type=${type} ${this.host}`);
          if (dnsResult.stdout.includes('answer:')) {
            this.log('DNS安全', `${type}记录`, 'INFO', `发现${type}记录`);
          }
        } catch (error) {
          // DNS记录不存在
        }
      }
    } catch (error) {
      this.log('DNS安全', 'DNS测试', 'WARNING', 'DNS测试失败，可能是本地服务', 'LOW');
    }
  }

  // 5. 网络拓扑测试
  async testNetworkTopology() {
    console.log('\n🔍 网络拓扑测试...');
    
    try {
      // 测试路由跟踪
      if (this.host !== 'localhost' && this.host !== '127.0.0.1') {
        const { exec } = require('child_process');
        const promisifiedExec = promisify(exec);
        
        const traceResult = await promisifiedExec(`tracert ${this.host}`, { timeout: 10000 });
        const hops = traceResult.stdout.split('\n').filter(line => line.trim().match(/^\s*\d+/)).length;
        
        if (hops > 10) {
          this.log('网络拓扑', '路由跳数', 'WARNING', `路由跳数较多: ${hops}跳`, 'LOW');
        } else {
          this.log('网络拓扑', '路由跳数', 'PASS', `路由跳数正常: ${hops}跳`);
        }
      } else {
        this.log('网络拓扑', '路由跟踪', 'INFO', '本地服务，跳过路由跟踪');
      }
    } catch (error) {
      this.log('网络拓扑', '路由跟踪', 'WARNING', '路由跟踪失败', 'LOW');
    }
  }

  // 6. 带宽和性能测试
  async testBandwidthAndPerformance() {
    console.log('\n🔍 带宽和性能测试...');
    
    // 测试响应时间
    const responseTimes = [];
    for (let i = 0; i < 10; i++) {
      const start = Date.now();
      try {
        await axios.get(this.baseUrl, { timeout: 5000 });
        responseTimes.push(Date.now() - start);
      } catch (error) {
        responseTimes.push(5000); // 超时
      }
    }
    
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    if (avgResponseTime > 2000) {
      this.log('性能测试', '响应时间', 'WARNING', `平均响应时间较慢: ${avgResponseTime.toFixed(2)}ms`, 'LOW');
    } else {
      this.log('性能测试', '响应时间', 'PASS', `平均响应时间: ${avgResponseTime.toFixed(2)}ms`);
    }

    // 测试并发性能
    const concurrentRequests = 20;
    const start = Date.now();
    const promises = [];
    
    for (let i = 0; i < concurrentRequests; i++) {
      promises.push(axios.get(this.baseUrl, { timeout: 10000 }).catch(() => null));
    }
    
    const results = await Promise.all(promises);
    const duration = Date.now() - start;
    const successCount = results.filter(r => r !== null).length;
    
    this.log('性能测试', '并发处理', 'INFO', 
      `${concurrentRequests}个并发请求耗时${duration}ms，成功${successCount}个`);
    
    if (successCount < concurrentRequests * 0.8) {
      this.log('性能测试', '并发稳定性', 'WARNING', '并发处理成功率较低', 'MEDIUM');
    } else {
      this.log('性能测试', '并发稳定性', 'PASS', '并发处理稳定');
    }
  }

  // 7. 网络协议攻击测试
  async testProtocolAttacks() {
    console.log('\n🔍 网络协议攻击测试...');
    
    // 测试慢速HTTP攻击
    try {
      const slowRequest = axios.get(this.baseUrl, {
        timeout: 30000,
        headers: {
          'Connection': 'keep-alive',
          'Content-Length': '1000000'
        }
      });
      
      // 等待5秒后取消请求
      setTimeout(() => {
        // 模拟慢速发送
      }, 5000);
      
      await slowRequest;
      this.log('协议攻击', '慢速HTTP攻击', 'WARNING', '可能容易受到慢速HTTP攻击', 'MEDIUM');
    } catch (error) {
      this.log('协议攻击', '慢速HTTP攻击', 'PASS', '慢速HTTP攻击被正确处理');
    }

    // 测试HTTP请求走私
    try {
      await axios.post(this.baseUrl, 'test', {
        headers: {
          'Content-Length': '4',
          'Transfer-Encoding': 'chunked'
        }
      });
      this.log('协议攻击', 'HTTP请求走私', 'WARNING', '可能存在HTTP请求走私风险', 'HIGH');
    } catch (error) {
      this.log('协议攻击', 'HTTP请求走私', 'PASS', 'HTTP请求走私被正确防护');
    }
  }

  // 8. 防火墙和过滤测试
  async testFirewallAndFiltering() {
    console.log('\n🔍 防火墙和过滤测试...');
    
    // 测试恶意User-Agent过滤
    const maliciousUserAgents = [
      'sqlmap/1.0',
      'Nikto/2.1.6',
      'Nessus',
      'OpenVAS',
      'w3af.org'
    ];

    for (const userAgent of maliciousUserAgents) {
      try {
        const response = await axios.get(this.baseUrl, {
          headers: { 'User-Agent': userAgent },
          timeout: 5000
        });
        
        if (response.status === 200) {
          this.log('防火墙过滤', `恶意UA过滤: ${userAgent}`, 'WARNING', '恶意User-Agent未被过滤', 'LOW');
        }
      } catch (error) {
        this.log('防火墙过滤', `恶意UA过滤: ${userAgent}`, 'PASS', '恶意User-Agent被正确过滤');
      }
    }

    // 测试IP地理位置过滤
    const suspiciousHeaders = {
      'X-Forwarded-For': '*******, *******',
      'X-Real-IP': '***********',
      'X-Originating-IP': '********'
    };

    for (const [header, value] of Object.entries(suspiciousHeaders)) {
      try {
        await axios.get(this.baseUrl, {
          headers: { [header]: value },
          timeout: 5000
        });
        this.log('防火墙过滤', `IP头部过滤: ${header}`, 'INFO', 'IP头部被正常处理');
      } catch (error) {
        this.log('防火墙过滤', `IP头部过滤: ${header}`, 'INFO', 'IP头部可能被过滤');
      }
    }
  }

  // 生成网络安全报告
  generateNetworkSecurityReport() {
    console.log('\n📊 网络安全测试报告');
    console.log('='.repeat(60));
    
    const categories = [...new Set(this.results.map(r => r.category))];
    let totalScore = 0;
    let maxScore = 0;
    
    for (const category of categories) {
      const categoryResults = this.results.filter(r => r.category === category);
      const pass = categoryResults.filter(r => r.status === 'PASS').length;
      const fail = categoryResults.filter(r => r.status === 'FAIL').length;
      const warning = categoryResults.filter(r => r.status === 'WARNING').length;
      const info = categoryResults.filter(r => r.status === 'INFO').length;
      
      console.log(`\n📋 ${category}:`);
      console.log(`  ✅ 通过: ${pass}  ❌ 失败: ${fail}  ⚠️  警告: ${warning}  📋 信息: ${info}`);
      
      // 计算分数
      const categoryScore = pass * 2 + warning * 1 + info * 1;
      const categoryMaxScore = (pass + fail + warning) * 2 + info * 1;
      totalScore += categoryScore;
      maxScore += categoryMaxScore;
      
      const issues = categoryResults.filter(r => r.status === 'FAIL' || r.severity);
      if (issues.length > 0) {
        console.log(`  🚨 问题:`);
        issues.forEach(issue => {
          console.log(`    • ${issue.test}: ${issue.message}`);
        });
      }
    }

    const networkSecurityScore = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
    
    console.log(`\n🎯 网络安全评分: ${networkSecurityScore}/100`);
    
    const criticalIssues = this.results.filter(r => r.severity === 'CRITICAL').length;
    const highIssues = this.results.filter(r => r.severity === 'HIGH').length;
    const mediumIssues = this.results.filter(r => r.severity === 'MEDIUM').length;
    const lowIssues = this.results.filter(r => r.severity === 'LOW').length;

    if (criticalIssues > 0) console.log(`  🔴 严重问题: ${criticalIssues}`);
    if (highIssues > 0) console.log(`  🟠 高危问题: ${highIssues}`);
    if (mediumIssues > 0) console.log(`  🟡 中危问题: ${mediumIssues}`);
    if (lowIssues > 0) console.log(`  🟢 低危问题: ${lowIssues}`);

    console.log(`\n💡 网络安全建议:`);
    console.log(`  • 启用HTTPS和强TLS配置`);
    console.log(`  • 实施Web应用防火墙(WAF)`);
    console.log(`  • 配置适当的安全头部`);
    console.log(`  • 限制不必要的端口暴露`);
    console.log(`  • 实施DDoS防护`);
    console.log(`  • 定期进行网络安全评估`);

    return { 
      score: networkSecurityScore, 
      criticalIssues, 
      highIssues, 
      mediumIssues, 
      lowIssues,
      allResults: this.results 
    };
  }

  // 运行所有网络安全测试
  async runAllTests() {
    console.log('🔒 开始网络安全渗透测试...');
    console.log(`🎯 目标: ${this.baseUrl}`);
    
    await this.testPortScanning();
    await this.testHTTPProtocolSecurity();
    await this.testTLSSecurity();
    await this.testDNSSecurity();
    await this.testNetworkTopology();
    await this.testBandwidthAndPerformance();
    await this.testProtocolAttacks();
    await this.testFirewallAndFiltering();

    return this.generateNetworkSecurityReport();
  }
}

// 运行测试
async function main() {
  const tester = new NetworkSecurityTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { NetworkSecurityTester };
