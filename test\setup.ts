// Jest测试环境设置
import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.LOG_LEVEL = 'error';
process.env.LOG_FILE = ''; // 禁用文件日志

// Mock @unblockneteasemusic/server to avoid initialization issues
jest.mock('@unblockneteasemusic/server', () => ({
  match: jest.fn().mockImplementation(async (_songId, _sources, _quality) => {
    return null;
  })
}));

// 全局测试超时
jest.setTimeout(10000);
