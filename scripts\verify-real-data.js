#!/usr/bin/env node

/**
 * 验证所有前端页面使用的都是真实API数据，没有模拟数据
 */

const fs = require('fs');
const path = require('path');

class RealDataVerifier {
    constructor() {
        this.issues = [];
        this.publicDir = path.join(__dirname, '../public');
    }

    /**
     * 运行验证
     */
    async verify() {
        console.log('🔍 开始验证前端代码中的真实数据使用...\n');

        // 检查JavaScript文件
        await this.checkJavaScriptFiles();
        
        // 检查HTML文件
        await this.checkHTMLFiles();

        // 输出结果
        this.outputResults();
    }

    /**
     * 检查JavaScript文件
     */
    async checkJavaScriptFiles() {
        const jsDir = path.join(this.publicDir, 'assets/js');
        const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));

        for (const file of jsFiles) {
            const filePath = path.join(jsDir, file);
            const content = fs.readFileSync(filePath, 'utf-8');
            
            console.log(`📄 检查 ${file}...`);
            
            // 检查模拟数据模式
            this.checkForMockPatterns(filePath, content);
            
            // 检查API调用
            this.checkAPIUsage(filePath, content);
            
            // 检查硬编码数据
            this.checkHardcodedData(filePath, content);
        }
    }

    /**
     * 检查HTML文件
     */
    async checkHTMLFiles() {
        const htmlFiles = fs.readdirSync(this.publicDir).filter(file => file.endsWith('.html'));

        for (const file of htmlFiles) {
            const filePath = path.join(this.publicDir, file);
            const content = fs.readFileSync(filePath, 'utf-8');
            
            console.log(`📄 检查 ${file}...`);
            
            // 检查硬编码的统计数据
            this.checkHardcodedStats(filePath, content);
        }
    }

    /**
     * 检查模拟数据模式
     */
    checkForMockPatterns(filePath, content) {
        const mockPatterns = [
            { pattern: /Math\.random\(\)/g, message: '使用了Math.random()生成模拟数据' },
            { pattern: /Math\.floor\(Math\.random\(\)/g, message: '使用了随机数生成模拟数据' },
            { pattern: /fake|mock|simulate/gi, message: '包含模拟数据相关关键词' },
            { pattern: /示例数据|测试数据|模拟数据/g, message: '包含中文模拟数据标识' },
            { pattern: /setTimeout.*random/gi, message: '使用定时器生成随机模拟数据' }
        ];

        mockPatterns.forEach(({ pattern, message }) => {
            const matches = content.match(pattern);
            if (matches) {
                this.issues.push({
                    file: path.relative(this.publicDir, filePath),
                    type: 'MOCK_DATA',
                    message: `${message}: ${matches.join(', ')}`,
                    severity: 'HIGH'
                });
            }
        });
    }

    /**
     * 检查API使用情况
     */
    checkAPIUsage(filePath, content) {
        const apiPatterns = [
            { pattern: /fetch\(['"`]([^'"`]+)['"`]\)/g, type: 'API_CALL' },
            { pattern: /\$\.get\(['"`]([^'"`]+)['"`]\)/g, type: 'JQUERY_API' },
            { pattern: /axios\.get\(['"`]([^'"`]+)['"`]\)/g, type: 'AXIOS_API' }
        ];

        let hasRealAPICalls = false;
        
        apiPatterns.forEach(({ pattern, type }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const url = match[1];
                if (url.startsWith('/api/') || url.startsWith('/health') || url.startsWith('/info') || url.startsWith('/sources')) {
                    hasRealAPICalls = true;
                    console.log(`  ✅ 发现真实API调用: ${url}`);
                }
            }
        });

        if (!hasRealAPICalls && content.includes('fetch(')) {
            this.issues.push({
                file: path.relative(this.publicDir, filePath),
                type: 'NO_REAL_API',
                message: '文件包含fetch调用但没有发现真实API端点',
                severity: 'MEDIUM'
            });
        }
    }

    /**
     * 检查硬编码数据
     */
    checkHardcodedData(filePath, content) {
        // 检查是否有硬编码的统计数据更新
        const hardcodedPatterns = [
            { pattern: /\.textContent\s*=\s*['"`]\d+['"`]/g, message: '硬编码数字数据' },
            { pattern: /\.innerHTML\s*=\s*['"`][^'"`]*\d+[^'"`]*['"`]/g, message: '硬编码HTML内容' }
        ];

        hardcodedPatterns.forEach(({ pattern, message }) => {
            const matches = content.match(pattern);
            if (matches) {
                // 过滤掉合理的硬编码（如固定的API数量）
                const suspiciousMatches = matches.filter(match => 
                    !match.includes('4') && // 核心API数量是固定的
                    !match.includes('loading') &&
                    !match.includes('加载中')
                );
                
                if (suspiciousMatches.length > 0) {
                    this.issues.push({
                        file: path.relative(this.publicDir, filePath),
                        type: 'HARDCODED_DATA',
                        message: `${message}: ${suspiciousMatches.slice(0, 3).join(', ')}`,
                        severity: 'MEDIUM'
                    });
                }
            }
        });
    }

    /**
     * 检查HTML中的硬编码统计数据
     */
    checkHardcodedStats(filePath, content) {
        // 检查统计数据是否有对应的ID用于动态更新
        const statPatterns = [
            { pattern: /<span[^>]*class="stat-number"[^>]*>(\d+)</g, message: '统计数字' },
            { pattern: /<span[^>]*class="stat-number"[^>]*>([^<]+)</g, message: '统计数据' }
        ];

        statPatterns.forEach(({ pattern, message }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const value = match[1];
                const fullMatch = match[0];
                
                // 检查是否有ID用于动态更新
                if (!fullMatch.includes('id=')) {
                    this.issues.push({
                        file: path.relative(this.publicDir, filePath),
                        type: 'STATIC_STAT',
                        message: `${message} "${value}" 没有ID，无法动态更新`,
                        severity: 'LOW'
                    });
                } else {
                    console.log(`  ✅ 统计数据有ID可动态更新: ${value}`);
                }
            }
        });
    }

    /**
     * 输出验证结果
     */
    outputResults() {
        console.log('\n📊 验证结果:');
        console.log('='.repeat(50));

        if (this.issues.length === 0) {
            console.log('🎉 太棒了！没有发现模拟数据或硬编码问题。');
            console.log('✅ 所有前端代码都使用真实API数据。');
            return;
        }

        // 按严重程度分组
        const groupedIssues = this.issues.reduce((groups, issue) => {
            if (!groups[issue.severity]) {
                groups[issue.severity] = [];
            }
            groups[issue.severity].push(issue);
            return groups;
        }, {});

        // 输出问题
        ['HIGH', 'MEDIUM', 'LOW'].forEach(severity => {
            if (groupedIssues[severity]) {
                console.log(`\n🚨 ${severity} 级别问题 (${groupedIssues[severity].length}个):`);
                groupedIssues[severity].forEach(issue => {
                    console.log(`  📁 ${issue.file}`);
                    console.log(`     ${issue.type}: ${issue.message}`);
                });
            }
        });

        console.log(`\n📈 总计发现 ${this.issues.length} 个问题需要修复。`);
        
        // 提供修复建议
        console.log('\n💡 修复建议:');
        console.log('- HIGH级别: 立即修复，使用真实API调用替换模拟数据');
        console.log('- MEDIUM级别: 尽快修复，确保数据来源真实');
        console.log('- LOW级别: 可选修复，提升用户体验');
    }
}

// 运行验证
const verifier = new RealDataVerifier();
verifier.verify().catch(console.error);
