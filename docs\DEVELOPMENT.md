# 💻 开发指南

欢迎参与 UNM API Server 的开发！本文档将帮助您快速搭建开发环境并了解项目的开发规范。

## 🛠️ 开发环境搭建

### 系统要求

- **Node.js**: 18.0.0 或更高版本
- **npm**: 8.0.0 或更高版本 (或 yarn 1.22.0+)
- **Git**: 2.20.0 或更高版本
- **VS Code**: 推荐使用 (可选)

### 1. 克隆项目

```bash
# 克隆主仓库
git clone https://github.com/your-username/unm-api-server.git
cd unm-api-server

# 或者克隆您的 fork
git clone https://github.com/your-fork/unm-api-server.git
cd unm-api-server

# 添加上游仓库
git remote add upstream https://github.com/original-repo/unm-api-server.git
```

### 2. 安装依赖

```bash
# 安装项目依赖
npm install

# 或使用 yarn
yarn install
```

### 3. 环境配置

```bash
# 复制环境配置文件
cp .env.template .env

# 编辑环境变量 (可选)
nano .env
```

开发环境推荐配置：

```bash
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug
ENABLE_CACHE=true
CACHE_TTL=60
RATE_LIMIT_MAX=1000
```

### 4. 启动开发服务器

```bash
# 启动开发服务器 (带热重载)
npm run dev

# 或者使用 yarn
yarn dev
```

服务器将在 `http://localhost:3000` 启动，并支持热重载。

## 📁 项目结构详解

```
unm-api-server/
├── src/                      # 源代码目录
│   ├── app.ts               # Express 应用配置
│   ├── server.ts            # 服务器启动入口
│   ├── config/              # 配置文件
│   │   ├── environment.ts   # 环境变量配置
│   │   └── sources.ts       # 音源配置
│   ├── routes/              # 路由层
│   │   ├── index.ts         # 路由入口
│   │   ├── music.ts         # 音乐API路由
│   │   ├── health.ts        # 健康检查路由
│   │   └── info.ts          # 系统信息路由
│   ├── services/            # 服务层
│   │   ├── musicService.ts  # 音乐服务
│   │   ├── cacheService.ts  # 缓存服务
│   │   ├── httpService.ts   # HTTP客户端服务
│   │   └── unmService.ts    # UNM核心服务
│   ├── middleware/          # 中间件
│   │   ├── cors.ts          # CORS中间件
│   │   ├── rateLimit.ts     # 频率限制中间件
│   │   ├── errorHandler.ts  # 错误处理中间件
│   │   └── validation.ts    # 参数验证中间件
│   ├── utils/               # 工具函数
│   │   ├── logger.ts        # 日志工具
│   │   ├── response.ts      # 响应格式化
│   │   ├── validation.ts    # 验证工具
│   │   └── userAgent.ts     # User-Agent工具
│   └── types/               # TypeScript 类型定义
│       ├── api.ts           # API相关类型
│       ├── music.ts         # 音乐相关类型
│       └── common.ts        # 通用类型
├── tests/                   # 测试文件
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   └── e2e/               # 端到端测试
├── docs/                   # 文档目录
├── scripts/                # 脚本文件
├── public/                 # 静态文件
├── logs/                   # 日志文件 (运行时生成)
├── dist/                   # 编译输出 (构建时生成)
├── .env.template           # 环境变量模板
├── .env                    # 环境变量 (本地)
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript 配置
├── .eslintrc.js           # ESLint 配置
├── .prettierrc            # Prettier 配置
├── jest.config.js         # Jest 测试配置
└── Dockerfile             # Docker 配置
```

## 🔧 开发工具配置

### VS Code 推荐扩展

创建 `.vscode/extensions.json` 文件：

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-docker"
  ]
}
```

### VS Code 设置

创建 `.vscode/settings.json` 文件：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  }
}
```

### 调试配置

创建 `.vscode/launch.json` 文件：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/server.ts",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "runtimeArgs": ["-r", "tsx/cjs"],
      "env": {
        "NODE_ENV": "development",
        "LOG_LEVEL": "debug"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector"
    },
    {
      "name": "Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand", "--no-cache"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
```

## 📝 代码规范

### TypeScript 规范

1. **类型定义**：
   ```typescript
   // ✅ 好的做法
   interface SearchParams {
     keyword: string;
     source?: string;
     limit?: number;
   }
   
   // ❌ 避免使用 any
   function search(params: any): any {
     // ...
   }
   
   // ✅ 使用具体类型
   function search(params: SearchParams): Promise<SearchResult[]> {
     // ...
   }
   ```

2. **导入规范**：
   ```typescript
   // ✅ 使用相对路径导入本地模块
   import { logger } from '../utils/logger';
   import { APIError } from '../types/common';
   
   // ✅ 使用绝对路径导入第三方模块
   import express from 'express';
   import axios from 'axios';
   ```

3. **错误处理**：
   ```typescript
   // ✅ 使用自定义错误类
   throw new APIError(400, 'Invalid parameter');
   
   // ✅ 异步函数错误处理
   try {
     const result = await someAsyncOperation();
     return result;
   } catch (error) {
     logger.error('Operation failed:', error);
     throw new APIError(500, 'Internal server error');
   }
   ```

### 命名规范

- **文件名**: 使用 camelCase (如 `musicService.ts`)
- **类名**: 使用 PascalCase (如 `MusicService`)
- **函数名**: 使用 camelCase (如 `getMusicUrl`)
- **常量**: 使用 UPPER_SNAKE_CASE (如 `DEFAULT_CACHE_TTL`)
- **接口**: 使用 PascalCase，可选择性添加 `I` 前缀

### 注释规范

```typescript
/**
 * 搜索音乐
 * @param params 搜索参数
 * @returns 搜索结果数组
 * @throws {APIError} 当参数无效时抛出错误
 */
async function search(params: SearchParams): Promise<SearchResult[]> {
  // 验证参数
  if (!params.keyword) {
    throw new APIError(400, 'Keyword is required');
  }
  
  // 执行搜索
  const results = await httpService.search(params);
  return results;
}
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行特定测试文件
npm test -- musicService.test.ts

# 监听模式运行测试
npm run test:watch
```

### 编写测试

#### 单元测试示例

```typescript
// tests/unit/services/musicService.test.ts
import { MusicService } from '../../../src/services/musicService';
import { httpService } from '../../../src/services/httpService';

jest.mock('../../../src/services/httpService');

describe('MusicService', () => {
  let musicService: MusicService;
  
  beforeEach(() => {
    musicService = new MusicService();
    jest.clearAllMocks();
  });
  
  describe('search', () => {
    it('should return search results', async () => {
      // Arrange
      const mockResults = [
        { id: '1', name: 'Test Song', artist: 'Test Artist' }
      ];
      (httpService.search as jest.Mock).mockResolvedValue(mockResults);
      
      // Act
      const results = await musicService.search({
        keyword: 'test',
        source: 'netease'
      });
      
      // Assert
      expect(results).toEqual(mockResults);
      expect(httpService.search).toHaveBeenCalledWith({
        keyword: 'test',
        source: 'netease'
      });
    });
    
    it('should throw error for invalid parameters', async () => {
      // Act & Assert
      await expect(musicService.search({ keyword: '' }))
        .rejects.toThrow('Keyword is required');
    });
  });
});
```

#### 集成测试示例

```typescript
// tests/integration/routes/music.test.ts
import request from 'supertest';
import app from '../../../src/app';

describe('Music API Routes', () => {
  describe('GET /api.php', () => {
    it('should search music successfully', async () => {
      const response = await request(app)
        .get('/api.php')
        .query({
          types: 'search',
          source: 'netease',
          name: 'test',
          count: 1
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('code', 200);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
    
    it('should return 400 for missing parameters', async () => {
      const response = await request(app)
        .get('/api.php')
        .query({ types: 'search' });
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('code', 400);
    });
  });
});
```

## 🔍 调试技巧

### 1. 日志调试

```typescript
import { logger } from '../utils/logger';

// 不同级别的日志
logger.debug('Debug information', { data: someData });
logger.info('Information message');
logger.warn('Warning message', { warning: details });
logger.error('Error occurred', { error: errorObject });
```

### 2. 断点调试

在 VS Code 中设置断点，然后按 F5 启动调试模式。

### 3. 网络请求调试

```typescript
// 启用 axios 请求日志
import axios from 'axios';

if (process.env.NODE_ENV === 'development') {
  axios.interceptors.request.use(request => {
    console.log('Starting Request:', request);
    return request;
  });
  
  axios.interceptors.response.use(response => {
    console.log('Response:', response);
    return response;
  });
}
```

## 📦 构建和发布

### 构建项目

```bash
# 清理旧的构建文件
npm run clean

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 构建项目
npm run build
```

### 发布流程

1. **更新版本号**：
   ```bash
   npm version patch  # 补丁版本
   npm version minor  # 次要版本
   npm version major  # 主要版本
   ```

2. **生成变更日志**：
   ```bash
   # 手动更新 CHANGELOG.md
   # 或使用自动化工具
   npm run changelog
   ```

3. **创建发布标签**：
   ```bash
   git tag -a v1.0.0 -m "Release version 1.0.0"
   git push origin v1.0.0
   ```

## 🤝 贡献流程

### 1. 准备工作

```bash
# Fork 项目到您的 GitHub 账户
# 克隆您的 fork
git clone https://github.com/your-username/unm-api-server.git

# 添加上游仓库
git remote add upstream https://github.com/original-repo/unm-api-server.git
```

### 2. 开发流程

```bash
# 同步上游代码
git fetch upstream
git checkout main
git merge upstream/main

# 创建功能分支
git checkout -b feature/your-feature-name

# 进行开发
# ... 编写代码 ...

# 提交更改
git add .
git commit -m "feat: add your feature description"

# 推送到您的 fork
git push origin feature/your-feature-name
```

### 3. 提交 Pull Request

1. 在 GitHub 上创建 Pull Request
2. 填写详细的描述和变更说明
3. 确保所有检查通过
4. 等待代码审查

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能添加
git commit -m "feat: add music search caching"

# 错误修复
git commit -m "fix: resolve memory leak in cache service"

# 文档更新
git commit -m "docs: update API documentation"

# 代码重构
git commit -m "refactor: improve error handling logic"

# 性能优化
git commit -m "perf: optimize database queries"

# 测试相关
git commit -m "test: add unit tests for music service"
```

## 📋 开发检查清单

在提交代码前，请确保：

- [ ] 代码通过 ESLint 检查 (`npm run lint`)
- [ ] 代码通过 TypeScript 类型检查 (`npm run type-check`)
- [ ] 所有测试通过 (`npm test`)
- [ ] 测试覆盖率达到要求
- [ ] 添加了必要的文档和注释
- [ ] 更新了相关的 API 文档
- [ ] 遵循了项目的代码规范
- [ ] 提交信息符合规范
- [ ] 没有包含敏感信息 (密钥、密码等)

## 🆘 获取帮助

如果您在开发过程中遇到问题：

1. 查看 [故障排除文档](TROUBLESHOOTING.md)
2. 搜索 [已有 Issues](https://github.com/your-username/unm-api-server/issues)
3. 在 [Discussions](https://github.com/your-username/unm-api-server/discussions) 中提问
4. 创建新的 [Issue](https://github.com/your-username/unm-api-server/issues/new)

## 📚 学习资源

- [Node.js 官方文档](https://nodejs.org/docs/)
- [Express.js 官方文档](https://expressjs.com/)
- [TypeScript 官方文档](https://www.typescriptlang.org/docs/)
- [Jest 测试框架](https://jestjs.io/docs/getting-started)
- [ESLint 配置指南](https://eslint.org/docs/user-guide/configuring/)

感谢您对项目的贡献！🎉
