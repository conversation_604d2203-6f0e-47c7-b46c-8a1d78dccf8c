import express from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { logger } from './utils/logger';
import { corsMiddleware } from './middleware/cors';
import { rateLimitMiddleware } from './middleware/rateLimit';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { config } from './config/environment';

import { headerSizeLimit } from './middleware/headerSizeLimit';
import { speedLimiter, connectionTimeout } from './middleware/slowDown';
import { userAgentFilter } from './middleware/userAgentFilter';
import { securityMonitor } from './middleware/securityMonitor';
import { httpsRedirect } from './utils/httpsServer';
import {
  traceMethodBlock,
  protocolVersionCheck,
  allowedMethodsOnly,
  securityHeaders,
} from './middleware/securityEnhancement';

// 创建Express应用
const app = express();

// 基础中间件配置
if (config.helmetEnabled) {
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ['\'self\''],
        styleSrc: [
          '\'self\'',
          '\'unsafe-inline\'',
          'https://cdnjs.cloudflare.com',
          'https://fonts.googleapis.com',
        ],
        scriptSrc: [
          '\'self\'',
          '\'unsafe-inline\'',
          '\'unsafe-eval\'', // 仅在开发环境需要时启用
        ],
        scriptSrcAttr: ['\'unsafe-inline\''], // 允许内联事件处理器
        imgSrc: [
          '\'self\'',
          'data:',
          'https:',
          'http:', // 允许HTTP图片（音乐封面可能来自HTTP源）
        ],
        connectSrc: [
          '\'self\'',
          'https://music-api.gdstudio.xyz', // 外部音乐API
          'https:', // 允许HTTPS连接
          'wss:', 'ws:', // WebSocket连接
        ],
        fontSrc: [
          '\'self\'',
          'https://fonts.gstatic.com',
          'https://cdnjs.cloudflare.com',
          'data:', // 允许data URI字体
        ],
        objectSrc: ['\'none\''],
        mediaSrc: [
          '\'self\'',
          'https:',
          'http:', // 音乐文件可能来自HTTP源
          'blob:', // 允许blob URL
          'data:', // 允许data URI
        ],
        frameSrc: ['\'none\''],
        baseUri: ['\'self\''],
        formAction: ['\'self\''],
        upgradeInsecureRequests: config.nodeEnv === 'production' ? [] : null,
        reportUri: config.nodeEnv === 'production' ? ['/csp-report'] : null,
      },
    },
    crossOriginEmbedderPolicy: false,
    crossOriginOpenerPolicy: false,
    crossOriginResourcePolicy: { policy: 'cross-origin' },
    hsts: config.nodeEnv === 'production' ? {
      maxAge: 31536000, // 1年
      includeSubDomains: true,
      preload: true,
    } : false,
    // 增强的安全头部
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    permittedCrossDomainPolicies: false,
  }));
}

app.use(compression());
app.use(corsMiddleware);
app.use(morgan('combined', {
  stream: {
    write: (message: string) => {
      logger.info(message.trim());
    },
  },
}));

// 安全中间件 - 按优先级顺序应用（移除重复项）
app.use(httpsRedirect); // HTTPS重定向（生产环境）
app.use(traceMethodBlock); // 禁用TRACE方法
app.use(protocolVersionCheck); // HTTP协议版本检查
app.use(allowedMethodsOnly); // 限制允许的HTTP方法
app.use(securityHeaders); // 安全响应头
app.use(connectionTimeout); // 连接超时保护
app.use(headerSizeLimit); // 请求头大小限制
app.use(userAgentFilter); // User-Agent过滤
app.use(securityMonitor); // 安全监控
app.use(speedLimiter); // 慢速攻击防护

// 请求解析中间件
app.use(express.json({ limit: config.requestSizeLimit }));
app.use(express.urlencoded({ extended: true, limit: config.requestSizeLimit }));

// 导入路由
import musicRouter from './routes/music';
import healthRouter from './routes/health';
import infoRouter from './routes/info';
import unmRouter from './routes/unm';
import cacheRouter from './routes/cache';
import frontendRouter from './routes/frontend';
import metricsRouter from './routes/metrics';

// 静态文件服务 - 必须在所有路由之前
app.use(express.static('public', {
  maxAge: config.staticMaxAge,
  setHeaders: (res, path) => {
    // 确保正确的MIME类型
    if (path.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css');
    } else if (path.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript');
    }
  },
}));

// 频率限制中间件
app.use(rateLimitMiddleware);

// 播放器页面
app.get('/player', (_req, res) => {
  res.sendFile('player.html', { root: 'public' });
});

// 注册路由 - API路由优先级高于前端路由
app.use('/', musicRouter);
app.use('/', healthRouter);
app.use('/', infoRouter);
app.use('/', unmRouter);
app.use('/', cacheRouter);
app.use('/', metricsRouter);
// 前端路由放在最后，避免拦截API请求
app.use('/', frontendRouter);

// 404处理中间件
app.use(notFoundHandler);

// 错误处理中间件
app.use(errorHandler);

export default app;
