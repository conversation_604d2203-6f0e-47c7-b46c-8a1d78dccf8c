// 标准API响应格式
export interface APIResponse<T = any> {
  code: number;        // HTTP状态码 (200=成功)
  message: string;     // 人类可读状态信息
  data: T | null;      // 响应数据或null
  timestamp: string;   // ISO格式时间戳
}

// API请求参数模型
export interface APIParams {
  types: 'search' | 'url' | 'pic' | 'lyric';
  source?: string;     // 音乐源
  name?: string;       // 搜索关键字
  id?: string;         // 资源ID
  count?: string;      // 页面长度
  pages?: string;      // 页码
  br?: string;         // 音质
  size?: string;       // 图片尺寸
}

// 搜索结果数据模型
export interface SearchResult {
  id: string;          // 曲目ID (track_id)
  name: string;        // 歌曲名称
  artist: string[];    // 歌手列表
  album: string;       // 专辑名称
  pic_id: string;      // 专辑图ID
  url_id: string;      // URL ID (已废弃，保持兼容性)
  lyric_id: string;    // 歌词ID
  source: string;      // 音乐源标识
}

// 音乐链接数据模型
export interface MusicUrl {
  url: string;         // 音乐播放链接
  br: number;          // 实际音质 (kbps)
  size: number;        // 文件大小 (KB)
  type?: string;       // 文件格式 (mp3/flac)
  source?: string;     // 实际音源 (来自UNM)
}

// 专辑图数据模型
export interface AlbumPicture {
  url: string;         // 图片链接
}

// 歌词数据模型
export interface Lyrics {
  lyric: string;       // LRC格式原语种歌词
  tlyric?: string;     // LRC格式中文翻译歌词
}

// 服务器信息数据模型
export interface ServerInfo {
  name: string;            // 服务名称
  version: string;         // 服务版本
  author: string;          // 作者信息
  description: string;     // 服务描述
  supported_sources: string[]; // 支持的音源列表
  stable_sources: string[];    // 稳定音源列表
  cache_enabled?: boolean;       // 缓存是否启用
  cache_stats?: any;            // 缓存统计信息
}

// 健康检查数据模型
export interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  uptime: number;      // 运行时间 (秒)
  timestamp: string;   // 检查时间
  version: string;     // 服务版本
  memory_usage: number; // 内存使用量 (MB)
  cpu_usage?: number;  // CPU使用率 (%)
}

// 错误响应模型
export interface ErrorResponse {
  code: number;        // 错误状态码
  message: string;     // 错误信息
  error?: string;      // 详细错误描述
  timestamp: string;   // 错误时间
}
