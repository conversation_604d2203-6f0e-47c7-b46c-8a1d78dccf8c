import { Router, Request, Response, NextFunction } from 'express';
import { musicService } from './../services/musicService';
import { APIResponse } from './../types/api';
import { APIError } from './../types/common';
import { logger } from './../utils/logger';
import { createSuccessResponse } from './../utils/response';
import { isValidSource } from './../config/sources';
import { config } from '../config/environment';
import {
  searchCacheMiddleware,
  musicUrlCacheMiddleware,
  lyricsCacheMiddleware,
  pictureCacheMiddleware,
} from '../middleware/cache';

const router = Router();

/**
 * 传统API端点 - 向后兼容
 * GET /api.php?types=search&source=netease&name=test&count=20
 */
router.get('/api.php', async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { types, source = 'netease', name, id, count, offset, quality, br, size } = req.query;

    if (!types) {
      throw new APIError(400, 'Parameter "types" is required');
    }

    switch (types) {
    case 'search':
      if (!name) {
        throw new APIError(400, 'Parameter "name" is required for search');
      }
      const searchResult = await musicService.search({
        source: source as string,
        keyword: name as string,
        limit: count ? parseInt(count as string) : 20,
        offset: offset ? parseInt(offset as string) : 0,
      });
      res.json(searchResult);
      return;

    case 'url':
      if (!id) {
        throw new APIError(400, 'Parameter "id" is required for url');
      }
      const urlResult = await musicService.getMusicUrl({
        source: source as string,
        id: id as string,
        quality: (br || quality) ? parseInt((br || quality) as string) : 999,
      });
      res.json(urlResult);
      return;

    case 'pic':
      if (!id) {
        throw new APIError(400, 'Parameter "id" is required for pic');
      }
      const picResult = await musicService.getPicture({
        source: source as string,
        id: id as string,
        size: size ? parseInt(size as string) : 300,
      });
      res.json(picResult);
      return;

    case 'lyric':
      if (!id) {
        throw new APIError(400, 'Parameter "id" is required for lyric');
      }
      const lyricResult = await musicService.getLyric({
        source: source as string,
        id: id as string,
      });
      res.json(lyricResult);
      return;

    default:
      throw new APIError(400, `Unsupported type: ${types}`);
    }
  } catch (error) {
    next(error);
  }
});

/**
 * 现代化RESTful音乐API端点
 */

/**
 * 搜索音乐
 * GET /api/v1/search?source=netease&q=周杰伦&limit=20&offset=0
 */
router.get('/api/v1/search', searchCacheMiddleware, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { source = config.defaultSource, q, limit = config.defaultSearchLimit.toString(), offset = config.defaultSearchOffset.toString() } = req.query;

    // 参数验证
    if (!q || typeof q !== 'string') {
      throw new APIError(400, `${config.errorMessages.missingParam}: q`);
    }

    if (!isValidSource(source as string)) {
      throw new APIError(400, `${config.errorMessages.invalidSource}: ${source}`);
    }

    const limitNum = parseInt(limit as string);
    const offsetNum = parseInt(offset as string);

    if (isNaN(limitNum) || limitNum < 1 || limitNum > config.maxSearchLimit) {
      throw new APIError(400, `Parameter "limit" must be between 1 and ${config.maxSearchLimit}`);
    }

    if (isNaN(offsetNum) || offsetNum < 0) {
      throw new APIError(400, 'Parameter "offset" must be >= 0');
    }

    // 搜索长度验证
    const queryStr = q;
    if (queryStr.length < config.validation.minSearchLength || queryStr.length > config.validation.maxSearchLength) {
      throw new APIError(400, `Search query length must be between ${config.validation.minSearchLength} and ${config.validation.maxSearchLength} characters`);
    }

    logger.info('Search request received:', { 
      source, 
      query: q, 
      limit: limitNum, 
      offset: offsetNum,
      ip: req.ip, 
    });

    const result = await musicService.search({
      source: source as string,
      keyword: q,
      limit: limitNum,
      offset: offsetNum,
    });

    const responseTime = Date.now() - startTime;
    logger.info(`Search completed in ${responseTime}ms`, { 
      resultCount: result.length,
      responseTime, 
    });

    const response: APIResponse = createSuccessResponse(result);
    res.json(response);

  } catch (error) {
    logger.error('Search request failed:', error);
    next(error);
  }
});

/**
 * 获取音乐播放链接
 * GET /api/v1/songs/:id/url?source=netease&quality=320
 */
router.get('/api/v1/songs/:id/url', musicUrlCacheMiddleware, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { id } = req.params;
    const { source = 'netease', quality = '999' } = req.query;

    // 参数验证
    if (!id || !/^[a-zA-Z0-9_-]+$/.test(id)) {
      throw new APIError(400, 'Invalid song ID format');
    }

    if (!isValidSource(source as string)) {
      throw new APIError(400, `Invalid source: ${source}`);
    }

    const qualityNum = parseInt(quality as string);
    const validQualities = [128, 192, 320, 740, 999];
    if (isNaN(qualityNum) || !validQualities.includes(qualityNum)) {
      throw new APIError(400, `Invalid quality. Must be one of: ${validQualities.join(', ')}`);
    }

    logger.info('Music URL request received:', { 
      id, 
      source, 
      quality: qualityNum,
      ip: req.ip, 
    });

    const result = await musicService.getMusicUrl({
      source: source as string,
      id,
      quality: qualityNum,
    });

    const responseTime = Date.now() - startTime;
    logger.info(`Music URL retrieved in ${responseTime}ms`, { responseTime });

    const response: APIResponse = createSuccessResponse(result);
    res.json(response);

  } catch (error) {
    logger.error('Music URL request failed:', error);
    next(error);
  }
});

/**
 * 获取专辑封面
 * GET /api/v1/albums/:id/picture?source=netease&size=300
 */
router.get('/api/v1/albums/:id/picture', pictureCacheMiddleware, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { id } = req.params;
    const { source = 'netease', size = '300' } = req.query;

    // 参数验证
    if (!id || !/^[a-zA-Z0-9_-]+$/.test(id)) {
      throw new APIError(400, 'Invalid album ID format');
    }

    if (!isValidSource(source as string)) {
      throw new APIError(400, `Invalid source: ${source}`);
    }

    const sizeNum = parseInt(size as string);
    const validSizes = [300, 500];
    if (isNaN(sizeNum) || !validSizes.includes(sizeNum)) {
      throw new APIError(400, `Invalid size. Must be one of: ${validSizes.join(', ')}`);
    }

    logger.info('Album picture request received:', { 
      id, 
      source, 
      size: sizeNum,
      ip: req.ip, 
    });

    const result = await musicService.getPicture({
      source: source as string,
      id,
      size: sizeNum,
    });

    const responseTime = Date.now() - startTime;
    logger.info(`Album picture retrieved in ${responseTime}ms`, { responseTime });

    const response: APIResponse = createSuccessResponse(result);
    res.json(response);

  } catch (error) {
    logger.error('Album picture request failed:', error);
    next(error);
  }
});

/**
 * 获取歌词
 * GET /api/v1/songs/:id/lyrics?source=netease
 */
router.get('/api/v1/songs/:id/lyrics', lyricsCacheMiddleware, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const { id } = req.params;
    const { source = 'netease' } = req.query;

    // 参数验证
    if (!id || !/^[a-zA-Z0-9_-]+$/.test(id)) {
      throw new APIError(400, 'Invalid song ID format');
    }

    if (!isValidSource(source as string)) {
      throw new APIError(400, `Invalid source: ${source}`);
    }

    logger.info('Lyrics request received:', { 
      id, 
      source,
      ip: req.ip, 
    });

    const result = await musicService.getLyric({
      source: source as string,
      id,
    });

    const responseTime = Date.now() - startTime;
    logger.info(`Lyrics retrieved in ${responseTime}ms`, { responseTime });

    const response: APIResponse = createSuccessResponse(result);
    res.json(response);

  } catch (error) {
    logger.error('Lyrics request failed:', error);
    next(error);
  }
});

export default router;
