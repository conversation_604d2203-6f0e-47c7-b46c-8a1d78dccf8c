import { Router, Request, Response } from 'express';
import path from 'path';

const router = Router();

/**
 * 前端路由处理
 * 为PWA应用提供路由支持
 */

// 主页路由
router.get('/', (_req: Request, res: Response) => {
  res.sendFile('index.html', { 
    root: 'public',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
  });
});

// PWA应用路由 - SPA路由支持
router.get('/app/*', (_req: Request, res: Response) => {
  res.sendFile('index.html', {
    root: 'public',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
  });
});



// API文档页面
router.get('/docs', (_req: Request, res: Response) => {
  res.sendFile('docs.html', {
    root: 'public',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    },
  });
});





// PWA Manifest
router.get('/app/manifest.json', (_req: Request, res: Response) => {
  res.sendFile('app/manifest.json', { 
    root: 'public',
    headers: {
      'Content-Type': 'application/manifest+json',
      'Cache-Control': 'public, max-age=86400', // 24小时缓存
    },
  });
});

// Service Worker
router.get('/app/sw.js', (_req: Request, res: Response) => {
  res.sendFile('app/sw.js', { 
    root: 'public',
    headers: {
      'Content-Type': 'application/javascript',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Service-Worker-Allowed': '/',
    },
  });
});

// 静态资源路由增强
router.get('/app/assets/*', (req: Request, res: Response, next) => {
  const filePath = req.path.replace('/app/assets/', '');
  const fullPath = path.join('public/app/assets', filePath);
  
  // 设置缓存头
  const ext = path.extname(filePath).toLowerCase();
  let cacheControl = 'public, max-age=86400'; // 默认24小时
  
  switch (ext) {
  case '.css':
  case '.js':
    cacheControl = 'public, max-age=3600'; // 1小时
    break;
  case '.png':
  case '.jpg':
  case '.jpeg':
  case '.gif':
  case '.webp':
  case '.svg':
    cacheControl = 'public, max-age=604800'; // 7天
    break;
  case '.woff':
  case '.woff2':
  case '.ttf':
  case '.eot':
    cacheControl = 'public, max-age=2592000'; // 30天
    break;
  }
  
  res.setHeader('Cache-Control', cacheControl);
  res.sendFile(fullPath, { root: process.cwd() }, (err) => {
    if (err) {
      next();
    }
  });
});



// 健康状态UI页面
router.get('/health-ui', (_req: Request, res: Response) => {
  res.sendFile('health-ui.html', {
    root: 'public',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    },
  });
});

// 404处理 - 对于前端路由，返回主页让客户端路由处理
router.get('*', (req: Request, res: Response, next) => {
  // 如果是API请求，不处理（让后续中间件处理）
  if (req.path.startsWith('/api/')) {
    return next();
  }

  // 如果是静态资源请求，不处理（让静态文件中间件处理）
  if (req.path.startsWith('/assets/') ||
      req.path.startsWith('/app/') ||
      req.path.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    return next();
  }

  // 如果请求接受HTML，返回主页
  if (req.accepts('html')) {
    res.sendFile('index.html', {
      root: 'public',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } else {
    next();
  }
});

export default router;
