<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7916ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5a03d4;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="musicGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff006e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff8500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d4ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 应用图标 (主图标) -->
  <g id="app-icon" transform="translate(50, 50)">
    <rect width="100" height="100" rx="20" fill="url(#primaryGradient)"/>
    <path d="M35 30 L35 70 L45 70 L45 40 L65 40 L65 70 L75 70 L75 30 Z" fill="white"/>
    <circle cx="40" cy="75" r="5" fill="white"/>
    <circle cx="70" cy="75" r="5" fill="white"/>
    <text x="50" y="95" text-anchor="middle" fill="#843dff" font-family="Inter" font-size="8" font-weight="600">UNM</text>
  </g>
  
  <!-- 播放按钮 -->
  <g id="play-button" transform="translate(200, 50)">
    <circle cx="25" cy="25" r="25" fill="url(#primaryGradient)"/>
    <path d="M20 15 L20 35 L35 25 Z" fill="white"/>
  </g>
  
  <!-- 暂停按钮 -->
  <g id="pause-button" transform="translate(300, 50)">
    <circle cx="25" cy="25" r="25" fill="url(#primaryGradient)"/>
    <rect x="18" y="15" width="4" height="20" fill="white"/>
    <rect x="28" y="15" width="4" height="20" fill="white"/>
  </g>
  
  <!-- 上一首按钮 -->
  <g id="prev-button" transform="translate(400, 50)">
    <circle cx="25" cy="25" r="25" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
    <rect x="15" y="15" width="3" height="20" fill="white"/>
    <path d="M22 15 L22 35 L35 25 Z" fill="white"/>
  </g>
  
  <!-- 下一首按钮 -->
  <g id="next-button" transform="translate(500, 50)">
    <circle cx="25" cy="25" r="25" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)"/>
    <path d="M15 15 L15 35 L28 25 Z" fill="white"/>
    <rect x="32" y="15" width="3" height="20" fill="white"/>
  </g>
  
  <!-- 搜索图标 -->
  <g id="search-icon" transform="translate(50, 150)">
    <circle cx="20" cy="20" r="15" fill="none" stroke="#a3a3a3" stroke-width="2"/>
    <path d="M32 32 L38 38" stroke="#a3a3a3" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- 菜单图标 -->
  <g id="menu-icon" transform="translate(150, 150)">
    <rect x="10" y="15" width="30" height="3" rx="1.5" fill="white"/>
    <rect x="10" y="23" width="30" height="3" rx="1.5" fill="white"/>
    <rect x="10" y="31" width="30" height="3" rx="1.5" fill="white"/>
  </g>
  
  <!-- 关闭图标 -->
  <g id="close-icon" transform="translate(250, 150)">
    <path d="M15 15 L35 35 M35 15 L15 35" stroke="white" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- 主题切换图标 (月亮) -->
  <g id="moon-icon" transform="translate(350, 150)">
    <path d="M25 10 A15 15 0 0 0 25 40 A10 10 0 0 1 25 10 Z" fill="white"/>
  </g>
  
  <!-- 主题切换图标 (太阳) -->
  <g id="sun-icon" transform="translate(450, 150)">
    <circle cx="25" cy="25" r="8" fill="white"/>
    <g stroke="white" stroke-width="2" stroke-linecap="round">
      <path d="M25 5 L25 10"/>
      <path d="M25 40 L25 45"/>
      <path d="M45 25 L40 25"/>
      <path d="M10 25 L5 25"/>
      <path d="M38.5 11.5 L35.5 14.5"/>
      <path d="M14.5 35.5 L11.5 38.5"/>
      <path d="M38.5 38.5 L35.5 35.5"/>
      <path d="M14.5 14.5 L11.5 11.5"/>
    </g>
  </g>
  
  <!-- 音乐图标 -->
  <g id="music-icon" transform="translate(50, 250)">
    <path d="M20 35 L20 15 L35 12 L35 32" stroke="white" stroke-width="2" fill="none"/>
    <circle cx="17" cy="35" r="3" fill="white"/>
    <circle cx="32" cy="32" r="3" fill="white"/>
  </g>
  
  <!-- 专辑图标 -->
  <g id="album-icon" transform="translate(150, 250)">
    <circle cx="25" cy="25" r="20" fill="url(#musicGradient)"/>
    <circle cx="25" cy="25" r="8" fill="rgba(0,0,0,0.3)"/>
    <circle cx="25" cy="25" r="3" fill="white"/>
  </g>
  
  <!-- 心形图标 (收藏) -->
  <g id="heart-icon" transform="translate(250, 250)">
    <path d="M25 40 C25 40 10 30 10 20 C10 15 15 10 20 10 C22.5 10 25 12.5 25 12.5 C25 12.5 27.5 10 30 10 C35 10 40 15 40 20 C40 30 25 40 25 40 Z" fill="none" stroke="white" stroke-width="2"/>
  </g>
  
  <!-- 分享图标 -->
  <g id="share-icon" transform="translate(350, 250)">
    <circle cx="15" cy="15" r="5" fill="none" stroke="white" stroke-width="2"/>
    <circle cx="35" cy="25" r="5" fill="none" stroke="white" stroke-width="2"/>
    <circle cx="15" cy="35" r="5" fill="none" stroke="white" stroke-width="2"/>
    <path d="M20 17 L30 23" stroke="white" stroke-width="2"/>
    <path d="M20 33 L30 27" stroke="white" stroke-width="2"/>
  </g>
  
  <!-- 下载图标 -->
  <g id="download-icon" transform="translate(450, 250)">
    <path d="M25 10 L25 35" stroke="white" stroke-width="2" stroke-linecap="round"/>
    <path d="M18 28 L25 35 L32 28" stroke="white" stroke-width="2" stroke-linecap="round" fill="none"/>
    <path d="M15 40 L35 40" stroke="white" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- 设置图标 -->
  <g id="settings-icon" transform="translate(50, 350)">
    <circle cx="25" cy="25" r="8" fill="none" stroke="white" stroke-width="2"/>
    <g stroke="white" stroke-width="2" stroke-linecap="round">
      <path d="M25 5 L25 10"/>
      <path d="M25 40 L25 45"/>
      <path d="M45 25 L40 25"/>
      <path d="M10 25 L5 25"/>
      <path d="M38.5 11.5 L35.5 14.5"/>
      <path d="M14.5 35.5 L11.5 38.5"/>
      <path d="M38.5 38.5 L35.5 35.5"/>
      <path d="M14.5 14.5 L11.5 11.5"/>
    </g>
  </g>
  
  <!-- 统计图标 -->
  <g id="chart-icon" transform="translate(150, 350)">
    <rect x="15" y="30" width="4" height="15" fill="url(#primaryGradient)"/>
    <rect x="23" y="25" width="4" height="20" fill="url(#primaryGradient)"/>
    <rect x="31" y="20" width="4" height="25" fill="url(#primaryGradient)"/>
  </g>
  
  <!-- 文档图标 -->
  <g id="docs-icon" transform="translate(250, 350)">
    <rect x="15" y="10" width="20" height="30" rx="2" fill="none" stroke="white" stroke-width="2"/>
    <path d="M20 18 L30 18" stroke="white" stroke-width="1"/>
    <path d="M20 22 L30 22" stroke="white" stroke-width="1"/>
    <path d="M20 26 L28 26" stroke="white" stroke-width="1"/>
  </g>
  
  <!-- 主页图标 -->
  <g id="home-icon" transform="translate(350, 350)">
    <path d="M25 10 L15 20 L15 40 L35 40 L35 20 Z" fill="none" stroke="white" stroke-width="2" stroke-linejoin="round"/>
    <rect x="22" y="30" width="6" height="10" fill="none" stroke="white" stroke-width="2"/>
  </g>
  
  <!-- 音量图标 -->
  <g id="volume-icon" transform="translate(450, 350)">
    <path d="M15 20 L20 20 L25 15 L25 35 L20 30 L15 30 Z" fill="white"/>
    <path d="M30 18 Q35 22.5 30 32" stroke="white" stroke-width="2" fill="none"/>
    <path d="M33 15 Q40 22.5 33 35" stroke="white" stroke-width="2" fill="none"/>
  </g>
  
  <!-- 进度条组件 -->
  <g id="progress-bar" transform="translate(50, 450)">
    <rect x="0" y="23" width="200" height="4" rx="2" fill="rgba(255,255,255,0.2)"/>
    <rect x="0" y="23" width="80" height="4" rx="2" fill="url(#primaryGradient)"/>
    <circle cx="80" cy="25" r="6" fill="#843dff"/>
  </g>
  
  <!-- 搜索框组件 -->
  <g id="search-box" transform="translate(50, 500)">
    <rect x="0" y="0" width="300" height="52" rx="12" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)"/>
    <g transform="translate(16, 18)">
      <circle cx="8" cy="8" r="6" fill="none" stroke="#a3a3a3" stroke-width="1.5"/>
      <path d="M12 12 L16 16" stroke="#a3a3a3" stroke-width="1.5" stroke-linecap="round"/>
    </g>
    <text x="48" y="30" fill="#a3a3a3" font-family="Inter" font-size="16">搜索歌曲、歌手、专辑...</text>
  </g>
  
  <!-- 标签说明 -->
  <text x="50" y="30" fill="#843dff" font-family="Inter" font-size="14" font-weight="600">应用图标</text>
  <text x="200" y="30" fill="#843dff" font-family="Inter" font-size="14" font-weight="600">播放控制</text>
  <text x="50" y="130" fill="#843dff" font-family="Inter" font-size="14" font-weight="600">导航图标</text>
  <text x="50" y="230" fill="#843dff" font-family="Inter" font-size="14" font-weight="600">音乐相关</text>
  <text x="50" y="330" fill="#843dff" font-family="Inter" font-size="14" font-weight="600">功能图标</text>
  <text x="50" y="430" fill="#843dff" font-family="Inter" font-size="14" font-weight="600">UI组件</text>
</svg>
