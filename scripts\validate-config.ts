#!/usr/bin/env tsx

/**
 * 配置验证脚本
 * 验证所有配置项是否正确加载，确保没有硬编码
 */

import { config } from '../src/config/environment';
import { logger } from '../src/utils/logger';

interface ConfigValidation {
  category: string;
  items: Array<{
    key: string;
    value: any;
    expected?: any;
    type: string;
    required: boolean;
    description: string;
  }>;
}

const validations: ConfigValidation[] = [
  {
    category: '服务器配置',
    items: [
      { key: 'nodeEnv', value: config.nodeEnv, type: 'string', required: true, description: '运行环境' },
      { key: 'port', value: config.port, type: 'number', required: true, description: '服务端口' },
      { key: 'serverName', value: config.serverName, type: 'string', required: true, description: '服务器名称' },
      { key: 'serverVersion', value: config.serverVersion, type: 'string', required: true, description: '服务器版本' },
      { key: 'serverAuthor', value: config.serverAuthor, type: 'string', required: true, description: '服务器作者' },
      { key: 'serverDescription', value: config.serverDescription, type: 'string', required: true, description: '服务器描述' },
    ]
  },
  {
    category: '外部API配置',
    items: [
      { key: 'musicApiUrl', value: config.musicApiUrl, type: 'string', required: true, description: '音乐API地址' },
      { key: 'httpTimeout', value: config.httpTimeout, type: 'number', required: true, description: 'HTTP超时时间' },
    ]
  },
  {
    category: 'User-Agent配置',
    items: [
      { key: 'userAgents', value: config.userAgents, type: 'array', required: true, description: 'User-Agent列表' },
    ]
  },
  {
    category: 'CORS配置',
    items: [
      { key: 'corsOrigin', value: config.corsOrigin, type: 'string', required: true, description: 'CORS源' },
      { key: 'corsMethods', value: config.corsMethods, type: 'array', required: true, description: 'CORS方法' },
      { key: 'corsHeaders', value: config.corsHeaders, type: 'array', required: true, description: 'CORS头部' },
    ]
  },
  {
    category: '缓存配置',
    items: [
      { key: 'cacheTTL', value: config.cacheTTL, type: 'number', required: true, description: '默认缓存TTL' },
      { key: 'enableCache', value: config.enableCache, type: 'boolean', required: true, description: '启用缓存' },
      { key: 'cacheMaxSize', value: config.cacheMaxSize, type: 'number', required: true, description: '缓存最大大小' },
      { key: 'cacheCleanupInterval', value: config.cacheCleanupInterval, type: 'number', required: true, description: '缓存清理间隔' },
      { key: 'cacheSearchTTL', value: config.cacheSearchTTL, type: 'number', required: true, description: '搜索缓存TTL' },
      { key: 'cacheMusicUrlTTL', value: config.cacheMusicUrlTTL, type: 'number', required: true, description: '音乐URL缓存TTL' },
      { key: 'cacheLyricsTTL', value: config.cacheLyricsTTL, type: 'number', required: true, description: '歌词缓存TTL' },
      { key: 'cachePictureTTL', value: config.cachePictureTTL, type: 'number', required: true, description: '图片缓存TTL' },
      { key: 'cacheUnmTTL', value: config.cacheUnmTTL, type: 'number', required: true, description: 'UNM缓存TTL' },
    ]
  },
  {
    category: '频率限制配置',
    items: [
      { key: 'rateLimitWindow', value: config.rateLimitWindow, type: 'number', required: true, description: '频率限制窗口' },
      { key: 'rateLimitMax', value: config.rateLimitMax, type: 'number', required: true, description: '频率限制最大值' },
      { key: 'rateLimitMessage', value: config.rateLimitMessage, type: 'string', required: true, description: '频率限制消息' },
    ]
  },
  {
    category: '音乐服务配置',
    items: [
      { key: 'defaultSource', value: config.defaultSource, type: 'string', required: true, description: '默认音源' },
      { key: 'supportedSources', value: config.supportedSources, type: 'array', required: true, description: '支持的音源' },
      { key: 'stableSources', value: config.stableSources, type: 'array', required: true, description: '稳定音源' },
    ]
  },
  {
    category: 'API配置',
    items: [
      { key: 'apiVersion', value: config.apiVersion, type: 'string', required: true, description: 'API版本' },
      { key: 'apiBasePath', value: config.apiBasePath, type: 'string', required: true, description: 'API基础路径' },
      { key: 'maxSearchLimit', value: config.maxSearchLimit, type: 'number', required: true, description: '最大搜索限制' },
      { key: 'maxBatchSize', value: config.maxBatchSize, type: 'number', required: true, description: '最大批处理大小' },
      { key: 'defaultSearchLimit', value: config.defaultSearchLimit, type: 'number', required: true, description: '默认搜索限制' },
      { key: 'defaultSearchOffset', value: config.defaultSearchOffset, type: 'number', required: true, description: '默认搜索偏移' },
    ]
  },
  {
    category: 'UNM配置',
    items: [
      { key: 'unmDefaultSources', value: config.unmDefaultSources, type: 'array', required: true, description: 'UNM默认音源' },
      { key: 'unmSupportedQualities', value: config.unmSupportedQualities, type: 'array', required: true, description: 'UNM支持的质量' },
      { key: 'unmDefaultQuality', value: config.unmDefaultQuality, type: 'number', required: true, description: 'UNM默认质量' },
      { key: 'unmSupportedSizes', value: config.unmSupportedSizes, type: 'array', required: true, description: 'UNM支持的尺寸' },
      { key: 'unmDefaultSize', value: config.unmDefaultSize, type: 'number', required: true, description: 'UNM默认尺寸' },
    ]
  },
  {
    category: '验证配置',
    items: [
      { key: 'validation.minSearchLength', value: config.validation.minSearchLength, type: 'number', required: true, description: '最小搜索长度' },
      { key: 'validation.maxSearchLength', value: config.validation.maxSearchLength, type: 'number', required: true, description: '最大搜索长度' },
      { key: 'validation.minIdLength', value: config.validation.minIdLength, type: 'number', required: true, description: '最小ID长度' },
      { key: 'validation.maxIdLength', value: config.validation.maxIdLength, type: 'number', required: true, description: '最大ID长度' },
    ]
  }
];

function validateConfig(): void {
  console.log('🔍 开始配置验证...\n');
  
  let totalItems = 0;
  let validItems = 0;
  let errors: string[] = [];
  let warnings: string[] = [];

  for (const validation of validations) {
    console.log(`📋 ${validation.category}:`);
    
    for (const item of validation.items) {
      totalItems++;
      
      const actualType = Array.isArray(item.value) ? 'array' : typeof item.value;
      const isValidType = actualType === item.type;
      const hasValue = item.value !== undefined && item.value !== null && item.value !== '';
      
      if (item.required && !hasValue) {
        errors.push(`❌ ${item.key}: 必需配置项缺失`);
        console.log(`  ❌ ${item.key}: 必需配置项缺失`);
      } else if (!isValidType) {
        errors.push(`❌ ${item.key}: 类型错误，期望 ${item.type}，实际 ${actualType}`);
        console.log(`  ❌ ${item.key}: 类型错误，期望 ${item.type}，实际 ${actualType}`);
      } else {
        validItems++;
        const displayValue = actualType === 'array' ? `[${item.value.length} items]` : 
                           actualType === 'string' && item.value.length > 50 ? `${item.value.substring(0, 50)}...` :
                           item.value;
        console.log(`  ✅ ${item.key}: ${displayValue}`);
      }
    }
    console.log('');
  }

  // 检查硬编码
  console.log('🔍 检查硬编码...');
  const hardcodedChecks = [
    { name: '端口号', value: config.port, shouldNotBe: [3000, 8080] },
    { name: '服务器名称', value: config.serverName, shouldNotBe: ['Express Server', 'Node Server'] },
    { name: '缓存TTL', value: config.cacheTTL, shouldNotBe: [300] },
  ];

  for (const check of hardcodedChecks) {
    if (check.shouldNotBe.includes(check.value)) {
      warnings.push(`⚠️  ${check.name}: 可能使用了硬编码值 ${check.value}`);
    }
  }

  // 输出结果
  console.log('📊 验证结果:');
  console.log(`  总配置项: ${totalItems}`);
  console.log(`  有效配置项: ${validItems}`);
  console.log(`  错误数量: ${errors.length}`);
  console.log(`  警告数量: ${warnings.length}`);
  console.log(`  成功率: ${Math.round((validItems / totalItems) * 100)}%\n`);

  if (errors.length > 0) {
    console.log('❌ 配置错误:');
    errors.forEach(error => console.log(`  ${error}`));
    console.log('');
  }

  if (warnings.length > 0) {
    console.log('⚠️  配置警告:');
    warnings.forEach(warning => console.log(`  ${warning}`));
    console.log('');
  }

  if (errors.length === 0) {
    console.log('🎉 配置验证通过！所有配置项都正确加载，没有硬编码问题。');
  } else {
    console.log('💥 配置验证失败！请修复上述错误。');
    process.exit(1);
  }
}

// 运行验证
validateConfig();
