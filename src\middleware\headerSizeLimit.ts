import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

/**
 * 请求头大小限制中间件
 * 防止大请求头导致的DoS攻击
 */
export const headerSizeLimit = (req: Request, res: Response, next: NextFunction): void | Response => {
  // 从配置获取最大头部大小，默认8KB
  const maxHeaderSize = parseInt(process.env.MAX_HEADER_SIZE || '8192');
  const maxHeaderCount = parseInt(process.env.MAX_HEADER_COUNT || '100'); // 最大头部数量

  // 计算请求头总大小（使用Buffer.byteLength获取准确字节数）
  const headerString = JSON.stringify(req.headers);
  const headerSize = Buffer.byteLength(headerString, 'utf8');

  // 检查头部数量限制
  const headerCount = Object.keys(req.headers).length;
  if (headerCount > maxHeaderCount) {
    logger.warn('Too many request headers detected', {
      headerCount,
      maxHeaderCount,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      timestamp: new Date().toISOString()
    });

    return res.status(431).json({
      code: 431,
      message: 'Too Many Request Headers',
      data: {
        current_count: headerCount,
        max_count: maxHeaderCount
      },
      timestamp: new Date().toISOString()
    });
  }
  
  if (headerSize > maxHeaderSize) {
    logger.warn('Large request headers detected', {
      headerSize,
      maxHeaderSize,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      headers: Object.keys(req.headers),
      timestamp: new Date().toISOString()
    });
    
    return res.status(431).json({
      code: 431,
      message: 'Request Header Fields Too Large',
      data: {
        current_size: headerSize,
        max_size: maxHeaderSize,
        size_unit: 'bytes'
      },
      timestamp: new Date().toISOString()
    });
  }
  
  // 检查单个头部字段大小
  const maxSingleHeaderSize = Math.floor(maxHeaderSize / 4); // 单个头部不超过总限制的1/4
  
  for (const [headerName, headerValue] of Object.entries(req.headers)) {
    const singleHeaderSize = JSON.stringify(headerValue).length;
    
    if (singleHeaderSize > maxSingleHeaderSize) {
      logger.warn('Large single request header detected', {
        headerName,
        headerSize: singleHeaderSize,
        maxSingleHeaderSize,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
        timestamp: new Date().toISOString()
      });
      
      return res.status(431).json({
        code: 431,
        message: `Request Header Field '${headerName}' Too Large`,
        data: {
          header_name: headerName,
          current_size: singleHeaderSize,
          max_size: maxSingleHeaderSize,
          size_unit: 'bytes'
        },
        timestamp: new Date().toISOString()
      });
    }
  }
  
  // 记录头部大小信息（仅调试模式）
  if (config.logLevel === 'debug') {
    logger.debug('Request headers size check', {
      headerSize,
      maxHeaderSize,
      headerCount: Object.keys(req.headers).length,
      url: req.url
    });
  }
  
  next();
};
