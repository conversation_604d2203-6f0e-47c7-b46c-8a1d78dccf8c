# 🏗️ 架构文档

本文档详细介绍了 UNM API Server 的技术架构、设计原则和核心组件。

## 📋 架构概览

UNM API Server 采用分层架构设计，基于 Node.js + Express.js + TypeScript 技术栈，提供高性能、可扩展的音乐API服务。

### 核心设计原则

1. **分层架构** - 清晰的职责分离
2. **依赖注入** - 松耦合的组件设计
3. **错误优先** - 完善的错误处理机制
4. **缓存优先** - 多层缓存提升性能
5. **安全优先** - 全面的安全防护措施
6. **监控优先** - 完整的监控和日志系统

## 🏛️ 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Web客户端]
        B[移动客户端]
        C[第三方应用]
    end
    
    subgraph "负载均衡层"
        D[Nginx/Apache]
    end
    
    subgraph "应用层"
        E[Express.js 服务器]
        F[中间件栈]
        G[路由层]
        H[服务层]
    end
    
    subgraph "缓存层"
        I[内存缓存]
        J[Redis缓存]
    end
    
    subgraph "外部服务"
        K[网易云音乐API]
        L[QQ音乐API]
        M[其他音源API]
        N[UNM核心库]
    end
    
    subgraph "监控层"
        O[日志系统]
        P[指标收集]
        Q[健康检查]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    H --> J
    H --> K
    H --> L
    H --> M
    H --> N
    E --> O
    E --> P
    E --> Q
```

## 📦 技术栈

### 核心技术

| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| Node.js | 18+ | 运行时环境 | JavaScript运行时 |
| Express.js | 4.18+ | Web框架 | HTTP服务器框架 |
| TypeScript | 5.0+ | 编程语言 | 类型安全的JavaScript |
| @unblockneteasemusic/server | 0.27+ | 核心库 | 音乐解锁核心功能 |

### 中间件和工具

| 技术 | 用途 | 说明 |
|------|------|------|
| Helmet | 安全防护 | HTTP安全头部 |
| CORS | 跨域支持 | 跨域资源共享 |
| Morgan | 访问日志 | HTTP请求日志 |
| Winston | 应用日志 | 结构化日志记录 |
| Express-rate-limit | 频率限制 | API访问频率控制 |
| Compression | 响应压缩 | Gzip压缩 |
| Node-cache | 内存缓存 | 本地缓存存储 |
| IORedis | Redis客户端 | 分布式缓存 |
| Joi | 参数验证 | 输入数据验证 |
| Axios | HTTP客户端 | 外部API调用 |

## 🔧 分层架构详解

### 1. 表示层 (Presentation Layer)

**职责**: 处理HTTP请求和响应，参数验证，格式化输出

**组件**:
- **路由层** (`src/routes/`)
  - `music.ts` - 音乐API路由
  - `health.ts` - 健康检查路由
  - `info.ts` - 系统信息路由

**设计模式**:
```typescript
// 路由处理器模式
router.get('/api/v1/search', 
  validateSearchParams,     // 参数验证中间件
  searchCacheMiddleware,    // 缓存中间件
  async (req, res, next) => {
    try {
      const result = await musicService.search(req.query);
      res.json(createSuccessResponse(result));
    } catch (error) {
      next(error);
    }
  }
);
```

### 2. 业务逻辑层 (Business Logic Layer)

**职责**: 实现核心业务逻辑，协调各种服务

**组件**:
- **服务层** (`src/services/`)
  - `musicService.ts` - 音乐业务逻辑
  - `cacheService.ts` - 缓存管理
  - `httpService.ts` - HTTP客户端封装
  - `unmService.ts` - UNM核心服务封装

**设计模式**:
```typescript
// 服务层模式
export class MusicService {
  constructor(
    private httpService: HttpService,
    private cacheService: CacheService,
    private unmService: UnmService
  ) {}
  
  async search(params: SearchParams): Promise<SearchResult[]> {
    // 1. 参数验证
    this.validateSearchParams(params);
    
    // 2. 缓存检查
    const cached = await this.cacheService.get(cacheKey);
    if (cached) return cached;
    
    // 3. 外部API调用
    const results = await this.httpService.search(params);
    
    // 4. 降级处理
    if (!results.length) {
      return await this.unmService.search(params);
    }
    
    // 5. 缓存结果
    await this.cacheService.set(cacheKey, results);
    
    return results;
  }
}
```

### 3. 数据访问层 (Data Access Layer)

**职责**: 管理外部数据源访问，缓存策略

**组件**:
- **HTTP服务** - 外部API调用
- **缓存服务** - 多层缓存管理
- **UNM服务** - 核心库封装

**缓存架构**:
```mermaid
graph LR
    A[请求] --> B{L1缓存}
    B -->|命中| C[返回结果]
    B -->|未命中| D{L2缓存}
    D -->|命中| E[回填L1] --> C
    D -->|未命中| F[外部API]
    F --> G[缓存结果] --> C
```

### 4. 基础设施层 (Infrastructure Layer)

**职责**: 提供横切关注点，如日志、监控、配置

**组件**:
- **配置管理** (`src/config/`)
- **工具函数** (`src/utils/`)
- **中间件** (`src/middleware/`)
- **类型定义** (`src/types/`)

## 🔄 数据流架构

### 请求处理流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant N as Nginx
    participant E as Express
    participant M as 中间件
    participant R as 路由
    participant S as 服务层
    participant Cache as 缓存
    participant API as 外部API
    
    C->>N: HTTP请求
    N->>E: 转发请求
    E->>M: 安全检查
    M->>M: CORS验证
    M->>M: 频率限制
    M->>M: 参数验证
    M->>R: 路由匹配
    R->>S: 调用服务
    S->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>S: 返回缓存数据
    else 缓存未命中
        S->>API: 调用外部API
        API-->>S: 返回数据
        S->>Cache: 存储缓存
    end
    S-->>R: 返回结果
    R-->>E: 格式化响应
    E-->>N: HTTP响应
    N-->>C: 返回结果
```

### 错误处理流程

```mermaid
graph TD
    A[错误发生] --> B{错误类型}
    B -->|API错误| C[APIError]
    B -->|验证错误| D[ValidationError]
    B -->|网络错误| E[NetworkError]
    B -->|系统错误| F[SystemError]
    
    C --> G[错误处理中间件]
    D --> G
    E --> G
    F --> G
    
    G --> H[记录日志]
    G --> I[格式化错误响应]
    G --> J[返回客户端]
    
    H --> K[Winston日志]
    K --> L[日志文件]
    K --> M[监控系统]
```

## 🔒 安全架构

### 安全层次

1. **网络层安全**
   - HTTPS/TLS加密
   - 防火墙配置
   - DDoS防护

2. **应用层安全**
   - CORS策略
   - 安全头部 (Helmet)
   - 输入验证和清理
   - 频率限制

3. **数据层安全**
   - 敏感数据加密
   - 安全的缓存策略

### 安全中间件栈

```typescript
// 安全中间件配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100次请求
  message: 'Too many requests from this IP'
}));
```

## 📊 性能架构

### 缓存策略

1. **L1缓存 (内存)**
   - 容量: 1000个条目
   - TTL: 5分钟
   - 用途: 热点数据快速访问

2. **L2缓存 (Redis)**
   - 容量: 无限制
   - TTL: 30分钟
   - 用途: 分布式缓存共享

3. **缓存键设计**
   ```typescript
   // 缓存键命名规范
   const cacheKey = `${operation}:${source}:${hash(params)}`;
   
   // 示例
   "search:netease:a1b2c3d4"
   "url:qq:e5f6g7h8"
   "pic:kuwo:i9j0k1l2"
   ```

### 性能优化策略

1. **连接池管理**
   ```typescript
   // HTTP连接池配置
   const agent = new https.Agent({
     keepAlive: true,
     maxSockets: 50,
     maxFreeSockets: 10,
     timeout: 60000
   });
   ```

2. **响应压缩**
   ```typescript
   app.use(compression({
     filter: (req, res) => {
       if (req.headers['x-no-compression']) {
         return false;
       }
       return compression.filter(req, res);
     },
     level: 6,
     threshold: 1024
   }));
   ```

3. **异步处理**
   ```typescript
   // 并发处理多个音源
   const results = await Promise.allSettled([
     this.searchFromSource('netease', params),
     this.searchFromSource('qq', params),
     this.searchFromSource('kuwo', params)
   ]);
   ```

## 📈 监控架构

### 监控指标

1. **业务指标**
   - API请求量
   - 响应时间
   - 错误率
   - 缓存命中率

2. **系统指标**
   - CPU使用率
   - 内存使用率
   - 磁盘I/O
   - 网络流量

3. **应用指标**
   - 活跃连接数
   - 队列长度
   - 垃圾回收频率

### 监控实现

```typescript
// 指标收集器
export class MetricsCollector {
  private metrics = new Map<string, number>();
  
  incrementCounter(name: string, value = 1) {
    const current = this.metrics.get(name) || 0;
    this.metrics.set(name, current + value);
  }
  
  recordTiming(name: string, duration: number) {
    this.metrics.set(`${name}_duration`, duration);
  }
  
  getMetrics() {
    return Object.fromEntries(this.metrics);
  }
}

// 使用示例
const startTime = Date.now();
try {
  const result = await musicService.search(params);
  metricsCollector.incrementCounter('search_success');
  return result;
} catch (error) {
  metricsCollector.incrementCounter('search_error');
  throw error;
} finally {
  const duration = Date.now() - startTime;
  metricsCollector.recordTiming('search_duration', duration);
}
```

## 🔄 扩展性设计

### 水平扩展

1. **无状态设计**
   - 所有状态存储在外部 (Redis)
   - 支持多实例部署

2. **负载均衡**
   ```nginx
   upstream unm_api {
       server 127.0.0.1:3000 weight=1;
       server 127.0.0.1:3001 weight=1;
       server 127.0.0.1:3002 weight=1;
   }
   ```

3. **数据库分片**
   - 按音源分片
   - 按用户ID分片

### 垂直扩展

1. **服务拆分**
   - 搜索服务
   - 音乐链接服务
   - 图片服务
   - 歌词服务

2. **微服务架构**
   ```mermaid
   graph TB
       A[API Gateway] --> B[搜索服务]
       A --> C[音乐服务]
       A --> D[图片服务]
       A --> E[歌词服务]
       
       B --> F[搜索数据库]
       C --> G[音乐数据库]
       D --> H[图片存储]
       E --> I[歌词数据库]
   ```

## 🔧 配置管理

### 配置层次

1. **默认配置** - 代码中的默认值
2. **环境配置** - `.env` 文件
3. **运行时配置** - 环境变量
4. **动态配置** - 配置中心 (未来)

### 配置结构

```typescript
interface AppConfig {
  server: {
    port: number;
    host: string;
    env: string;
  };
  cache: {
    ttl: number;
    maxSize: number;
    redisUrl?: string;
  };
  security: {
    corsOrigin: string[];
    rateLimit: {
      windowMs: number;
      max: number;
    };
  };
  logging: {
    level: string;
    file?: string;
  };
  music: {
    defaultSource: string;
    supportedSources: string[];
    timeout: number;
  };
}
```

## 📋 架构决策记录 (ADR)

### ADR-001: 选择 Express.js 而非 Fastify

**状态**: 已接受

**背景**: 需要选择Web框架

**决策**: 选择 Express.js

**理由**:
- 成熟的生态系统
- 丰富的中间件支持
- 团队熟悉度高
- 社区支持完善

**后果**:
- 性能略低于 Fastify
- 但稳定性和兼容性更好

### ADR-002: 采用分层架构

**状态**: 已接受

**背景**: 需要确定应用架构模式

**决策**: 采用分层架构

**理由**:
- 职责分离清晰
- 易于测试和维护
- 支持团队协作开发

**后果**:
- 增加了一定的复杂性
- 但提高了代码质量和可维护性

## 🚀 未来架构演进

### 短期目标 (3-6个月)

1. **性能优化**
   - 实现连接池
   - 优化缓存策略
   - 添加CDN支持

2. **监控增强**
   - 集成 Prometheus
   - 添加 Grafana 仪表板
   - 实现智能告警

### 中期目标 (6-12个月)

1. **微服务化**
   - 拆分核心服务
   - 实现服务发现
   - 添加API网关

2. **容器化**
   - Kubernetes 部署
   - 自动扩缩容
   - 滚动更新

### 长期目标 (1-2年)

1. **云原生**
   - Serverless 架构
   - 事件驱动设计
   - 多云部署

2. **AI增强**
   - 智能推荐
   - 自动优化
   - 预测性缓存

---

本架构文档将随着项目的发展持续更新和完善。
