# 🔒 安全修复建议

基于安全渗透测试结果，以下是针对发现问题的具体修复建议。

## 📊 安全评估结果

- **总体评分**: 81/100
- **风险等级**: 中低风险
- **测试项目**: 93个
- **通过率**: 68%

## 🚨 需要修复的安全问题

### 1. 高风险问题 (立即修复)

#### ❌ TRACE方法未被禁用
**问题**: HTTP TRACE方法未被禁用，可能存在XST攻击风险
**影响**: 可能导致跨站追踪攻击
**修复方案**:

```typescript
// src/app.ts
app.use((req, res, next) => {
  if (req.method === 'TRACE') {
    return res.status(405).json({
      code: 405,
      message: 'Method Not Allowed',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
  next();
});
```

### 2. 中等风险问题 (优先修复)

#### ⚠️ 缺少CSP安全头部
**问题**: 缺少Content-Security-Policy头部
**影响**: 可能存在XSS攻击风险
**修复方案**:

```typescript
// src/app.ts
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false,
  crossOriginResourcePolicy: { policy: 'cross-origin' },
}));
```

#### ⚠️ 未启用HTTPS
**问题**: 服务未启用HTTPS
**影响**: 数据传输不安全，可能被中间人攻击
**修复方案**:

1. **生成SSL证书**:
```bash
# 开发环境 - 自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 生产环境 - 使用Let's Encrypt
certbot certonly --standalone -d yourdomain.com
```

2. **配置HTTPS服务器**:
```typescript
// src/server.ts
import https from 'https';
import fs from 'fs';

if (config.nodeEnv === 'production') {
  const options = {
    key: fs.readFileSync(config.sslKeyPath),
    cert: fs.readFileSync(config.sslCertPath)
  };
  
  const httpsServer = https.createServer(options, app);
  httpsServer.listen(config.httpsPort, () => {
    logger.info(`🔒 HTTPS Server running on port ${config.httpsPort}`);
  });
}
```

3. **添加HTTPS重定向**:
```typescript
// src/middleware/httpsRedirect.ts
export const httpsRedirect = (req: Request, res: Response, next: NextFunction) => {
  if (config.nodeEnv === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    return res.redirect(301, `https://${req.get('host')}${req.url}`);
  }
  next();
};
```

#### ⚠️ HTTP/1.0协议支持
**问题**: 支持不安全的HTTP/1.0协议
**影响**: 可能存在协议降级攻击
**修复方案**:

```typescript
// src/middleware/protocolCheck.ts
export const protocolCheck = (req: Request, res: Response, next: NextFunction) => {
  if (req.httpVersion === '1.0') {
    return res.status(426).json({
      code: 426,
      message: 'Upgrade Required - HTTP/1.1 or higher required',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
  next();
};
```

#### ⚠️ 大请求头处理
**问题**: 可能允许过大的请求头
**影响**: 可能导致DoS攻击
**修复方案**:

```typescript
// src/app.ts
app.use((req, res, next) => {
  const maxHeaderSize = 8192; // 8KB
  const headerSize = JSON.stringify(req.headers).length;
  
  if (headerSize > maxHeaderSize) {
    return res.status(431).json({
      code: 431,
      message: 'Request Header Fields Too Large',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
  next();
});
```

#### ⚠️ 慢速HTTP攻击防护
**问题**: 可能容易受到慢速HTTP攻击
**影响**: 可能导致服务器资源耗尽
**修复方案**:

```bash
npm install express-slow-down
```

```typescript
// src/middleware/slowDown.ts
import slowDown from 'express-slow-down';

export const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15分钟
  delayAfter: 100, // 允许100个快速请求
  delayMs: 500, // 每个请求延迟500ms
  maxDelayMs: 20000, // 最大延迟20秒
});
```

### 3. 低风险问题 (建议修复)

#### ⚠️ 恶意User-Agent过滤
**问题**: 恶意User-Agent未被过滤
**影响**: 可能被自动化攻击工具利用
**修复方案**:

```typescript
// src/middleware/userAgentFilter.ts
const maliciousUserAgents = [
  'sqlmap', 'nikto', 'nessus', 'openvas', 'w3af', 'burp', 'nmap',
  'masscan', 'zap', 'skipfish', 'wpscan', 'dirb', 'dirbuster'
];

export const userAgentFilter = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.get('User-Agent')?.toLowerCase() || '';
  
  const isMalicious = maliciousUserAgents.some(pattern => 
    userAgent.includes(pattern.toLowerCase())
  );
  
  if (isMalicious) {
    logger.warn('Malicious User-Agent detected', { 
      userAgent: req.get('User-Agent'),
      ip: req.ip 
    });
    
    return res.status(403).json({
      code: 403,
      message: 'Forbidden',
      data: null,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};
```

## 🛡️ 额外安全加固建议

### 1. 实施Web应用防火墙(WAF)

```bash
# 使用nginx作为反向代理和WAF
sudo apt install nginx nginx-module-njs

# 配置nginx WAF规则
# /etc/nginx/sites-available/unm-api
server {
    listen 80;
    server_name yourdomain.com;
    
    # 基础安全头部
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 限制请求大小
    client_max_body_size 10M;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # 限制请求方法
    if ($request_method !~ ^(GET|POST|PUT|DELETE|OPTIONS)$) {
        return 405;
    }
    
    # 过滤恶意User-Agent
    if ($http_user_agent ~* (sqlmap|nikto|nessus|openvas|w3af)) {
        return 403;
    }
    
    # 反向代理到Node.js应用
    location / {
        proxy_pass http://localhost:5678;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. 实施请求验证中间件

```typescript
// src/middleware/requestValidator.ts
import { body, query, param, validationResult } from 'express-validator';

export const validateSearchRequest = [
  query('source').isIn(config.supportedSources).withMessage('Invalid source'),
  query('q').isLength({ min: 1, max: 100 }).withMessage('Query must be 1-100 characters'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be >= 0'),
  
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: 'Validation failed',
        data: errors.array(),
        timestamp: new Date().toISOString()
      });
    }
    next();
  }
];
```

### 3. 实施安全监控

```typescript
// src/middleware/securityMonitor.ts
export const securityMonitor = (req: Request, res: Response, next: NextFunction) => {
  // 记录可疑活动
  const suspiciousPatterns = [
    /\.\.\//,  // 路径遍历
    /<script/i, // XSS
    /union.*select/i, // SQL注入
    /exec\(/i, // 命令注入
  ];
  
  const requestData = JSON.stringify({
    url: req.url,
    body: req.body,
    query: req.query,
    headers: req.headers
  });
  
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(requestData)
  );
  
  if (isSuspicious) {
    logger.warn('Suspicious request detected', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};
```

### 4. 配置环境变量

```bash
# .env
# HTTPS配置
ENABLE_HTTPS=true
HTTPS_PORT=443
SSL_KEY_PATH=/path/to/private-key.pem
SSL_CERT_PATH=/path/to/certificate.pem

# 安全配置
ENABLE_WAF=true
ENABLE_USER_AGENT_FILTER=true
ENABLE_SECURITY_MONITOR=true
MAX_HEADER_SIZE=8192
ENABLE_SLOW_DOWN=true

# CSP配置
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self'
CSP_STYLE_SRC='self' 'unsafe-inline'
CSP_IMG_SRC='self' data: https:
```

## 🔄 实施步骤

1. **立即修复高风险问题**
   - 禁用TRACE方法
   - 添加CSP头部

2. **部署HTTPS**
   - 获取SSL证书
   - 配置HTTPS服务器
   - 添加HTTP重定向

3. **加强输入验证**
   - 实施请求验证中间件
   - 添加User-Agent过滤
   - 限制请求头大小

4. **部署WAF**
   - 配置nginx反向代理
   - 实施基础WAF规则
   - 添加安全监控

5. **测试验证**
   - 重新运行安全测试
   - 验证修复效果
   - 监控安全日志

## 📊 预期改进效果

实施所有修复后，预期安全评分将提升至：
- **总体评分**: 90+/100
- **风险等级**: 低风险
- **严重问题**: 0个
- **高危问题**: 0个

## 🔍 持续安全维护

1. **定期安全评估**: 每月运行一次完整安全测试
2. **安全更新**: 及时更新依赖包和安全补丁
3. **日志监控**: 建立安全事件监控和告警
4. **渗透测试**: 每季度进行一次专业渗透测试
5. **安全培训**: 定期进行安全意识培训

## 📞 支持

如需安全修复支持，请：
1. 查看详细的安全测试报告
2. 参考配置文档
3. 运行 `npm run security-help` 查看可用命令
