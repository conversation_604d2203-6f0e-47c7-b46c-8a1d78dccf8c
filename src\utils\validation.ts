import { isValidSource } from './../config/sources';

/**
 * API参数验证结果接口
 */
export interface APIValidationResult {
  isValid: boolean;
  error?: string;
  params?: {
    types: string;
    source?: string;
    name?: string;
    id?: string;
    count?: string;
    pages?: string;
    br?: string;
    size?: string;
  };
}

/**
 * 验证API参数
 */
export function validateAPIParams(query: any): APIValidationResult {
  // 检查必需的types参数
  if (!query.types) {
    return {
      isValid: false,
      error: 'Parameter "types" is required',
    };
  }

  const types = query.types.toString().toLowerCase();
  const validTypes = ['search', 'url', 'pic', 'lyric'];
  
  if (!validTypes.includes(types)) {
    return {
      isValid: false,
      error: `Invalid types parameter. Must be one of: ${validTypes.join(', ')}`,
    };
  }

  // 验证音源参数
  const source = query.source?.toString();
  if (source && !isValidSource(source)) {
    return {
      isValid: false,
      error: `Invalid source parameter: ${source}`,
    };
  }

  // 根据types验证特定参数
  switch (types) {
    case 'search':
      if (!query.name) {
        return {
          isValid: false,
          error: 'Parameter "name" is required for search',
        };
      }
      
      // 验证搜索关键字长度
      const name = query.name.toString();
      if (name.length < 1 || name.length > 100) {
        return {
          isValid: false,
          error: 'Search keyword must be between 1 and 100 characters',
        };
      }
      
      // 验证count参数
      if (query.count) {
        const count = parseInt(query.count.toString());
        if (isNaN(count) || count < 1 || count > 100) {
          return {
            isValid: false,
            error: 'Parameter "count" must be between 1 and 100',
          };
        }
      }
      
      // 验证pages参数
      if (query.pages) {
        const pages = parseInt(query.pages.toString());
        if (isNaN(pages) || pages < 1 || pages > 1000) {
          return {
            isValid: false,
            error: 'Parameter "pages" must be between 1 and 1000',
          };
        }
      }
      break;

    case 'url':
    case 'pic':
    case 'lyric':
      if (!query.id) {
        return {
          isValid: false,
          error: `Parameter "id" is required for ${types}`,
        };
      }
      
      // 验证ID格式
      const id = query.id.toString();
      if (!/^[a-zA-Z0-9_-]+$/.test(id)) {
        return {
          isValid: false,
          error: 'Invalid ID format. Only alphanumeric characters, underscores and hyphens are allowed',
        };
      }
      
      // 验证音质参数 (仅用于url类型)
      if (types === 'url' && query.br) {
        const br = parseInt(query.br.toString());
        const validBitrates = [128, 192, 320, 740, 999];
        if (isNaN(br) || !validBitrates.includes(br)) {
          return {
            isValid: false,
            error: `Invalid bitrate. Must be one of: ${validBitrates.join(', ')}`,
          };
        }
      }
      
      // 验证图片尺寸参数 (仅用于pic类型)
      if (types === 'pic' && query.size) {
        const size = parseInt(query.size.toString());
        const validSizes = [300, 500];
        if (isNaN(size) || !validSizes.includes(size)) {
          return {
            isValid: false,
            error: `Invalid size. Must be one of: ${validSizes.join(', ')}`,
          };
        }
      }
      break;
  }

  // 验证通过，返回清理后的参数
  return {
    isValid: true,
    params: {
      types,
      source: source || 'netease',
      name: query.name?.toString(),
      id: query.id?.toString(),
      count: query.count?.toString() || '20',
      pages: query.pages?.toString() || '1',
      br: query.br?.toString() || '999',
      size: query.size?.toString() || '300',
    },
  };
}

/**
 * 验证字符串是否为有效的数字
 */
export function isValidNumber(value: string, min?: number, max?: number): boolean {
  const num = parseInt(value);
  if (isNaN(num)) return false;
  if (min !== undefined && num < min) return false;
  if (max !== undefined && num > max) return false;
  return true;
}

/**
 * 验证字符串长度
 */
export function isValidLength(value: string, min: number, max: number): boolean {
  return value.length >= min && value.length <= max;
}

/**
 * 验证字符串格式（正则表达式）
 */
export function isValidFormat(value: string, pattern: RegExp): boolean {
  return pattern.test(value);
}
