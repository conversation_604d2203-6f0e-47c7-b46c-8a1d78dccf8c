#!/usr/bin/env tsx

/**
 * 生产环境停止脚本
 * 安全地停止生产环境服务器
 */

import * as fs from 'fs';
import * as path from 'path';

class ProductionStopper {
  private pidFile = 'server.pid';

  private async stopServer(): Promise<boolean> {
    if (!fs.existsSync(this.pidFile)) {
      console.log('⚠️  未找到服务器进程ID文件，服务器可能未运行');
      return false;
    }

    try {
      const pid = parseInt(fs.readFileSync(this.pidFile, 'utf-8').trim());
      
      if (!pid || isNaN(pid)) {
        console.log('❌ 无效的进程ID');
        return false;
      }

      console.log(`🛑 正在停止服务器进程 (PID: ${pid})...`);

      // 尝试优雅停止
      try {
        process.kill(pid, 'SIGTERM');
        console.log('✅ 发送SIGTERM信号成功');
        
        // 等待进程停止
        await this.waitForProcessStop(pid, 10000);
        
      } catch (error: any) {
        if (error.code === 'ESRCH') {
          console.log('⚠️  进程已经停止');
        } else {
          console.log('⚠️  优雅停止失败，尝试强制停止...');
          try {
            process.kill(pid, 'SIGKILL');
            console.log('✅ 强制停止成功');
          } catch (killError: any) {
            if (killError.code === 'ESRCH') {
              console.log('✅ 进程已经停止');
            } else {
              console.error('❌ 强制停止失败:', killError);
              return false;
            }
          }
        }
      }

      // 清理PID文件
      fs.unlinkSync(this.pidFile);
      console.log('✅ 清理进程ID文件');

      return true;
    } catch (error) {
      console.error('❌ 停止服务器失败:', error);
      return false;
    }
  }

  private async waitForProcessStop(pid: number, timeout: number): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        process.kill(pid, 0); // 检查进程是否存在
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error: any) {
        if (error.code === 'ESRCH') {
          console.log('✅ 进程已停止');
          return;
        }
      }
    }
    
    throw new Error('进程停止超时');
  }

  private restoreEnvironment(): void {
    console.log('🔧 恢复环境配置...');
    
    if (fs.existsSync('.env.backup')) {
      fs.copyFileSync('.env.backup', '.env');
      fs.unlinkSync('.env.backup');
      console.log('✅ 恢复原始环境配置');
    }
  }

  private displayStopInfo(): void {
    console.log('\n' + '='.repeat(50));
    console.log('🎉 UNM API Server 已安全停止');
    console.log('='.repeat(50));
    console.log('💡 使用 npm run start-production 重新启动');
    console.log('💡 使用 npm run dev 启动开发环境');
    console.log('='.repeat(50) + '\n');
  }

  async stop(): Promise<void> {
    try {
      console.log('🛑 UNM API Server 生产环境停止器');
      console.log('='.repeat(50) + '\n');

      const stopped = await this.stopServer();
      
      if (stopped) {
        this.restoreEnvironment();
        this.displayStopInfo();
      } else {
        console.log('❌ 服务器停止失败');
        process.exit(1);
      }

    } catch (error) {
      console.error('❌ 停止过程中发生错误:', error);
      process.exit(1);
    }
  }
}

// 运行生产环境停止器
async function main() {
  const stopper = new ProductionStopper();
  await stopper.stop();
}

if (require.main === module) {
  main().catch(console.error);
}

export { ProductionStopper };
