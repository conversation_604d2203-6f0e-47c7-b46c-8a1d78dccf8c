// API相关常量
export const API_TYPES = {
  SEARCH: 'search',
  URL: 'url',
  PIC: 'pic',
  LYRIC: 'lyric',
} as const;

// 音质常量
export const BITRATES = {
  LOW: 128,
  MEDIUM: 192,
  HIGH: 320,
  LOSSLESS: 740,
  MASTER: 999,
} as const;

// 图片尺寸常量
export const IMAGE_SIZES = {
  SMALL: 300,
  LARGE: 500,
} as const;

// 默认值常量
export const DEFAULTS = {
  SOURCE: 'netease',
  COUNT: 20,
  PAGES: 1,
  BITRATE: 999,
  IMAGE_SIZE: 300,
} as const;

// 限制常量
export const LIMITS = {
  MAX_COUNT: 100,
  MIN_COUNT: 1,
  MAX_PAGES: 1000,
  MIN_PAGES: 1,
  MAX_KEYWORD_LENGTH: 100,
  MIN_KEYWORD_LENGTH: 1,
} as const;

// HTTP状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// 缓存键前缀
export const CACHE_KEYS = {
  SEARCH: 'search',
  URL: 'url',
  PIC: 'pic',
  LYRIC: 'lyric',
} as const;

// 错误消息常量
export const ERROR_MESSAGES = {
  INVALID_TYPE: 'Invalid types parameter',
  MISSING_NAME: 'Missing required parameter: name',
  MISSING_ID: 'Missing required parameter: id',
  INVALID_SOURCE: 'Invalid source parameter',
  INVALID_COUNT: 'Count must be between 1 and 100',
  INVALID_PAGES: 'Pages must be between 1 and 1000',
  INVALID_BITRATE: 'Invalid bitrate parameter',
  INVALID_SIZE: 'Invalid size parameter',
  KEYWORD_TOO_LONG: 'Keyword is too long',
  KEYWORD_TOO_SHORT: 'Keyword is too short',
  INTERNAL_ERROR: 'Internal server error',
  NOT_FOUND: 'Resource not found',
  RATE_LIMIT: 'Too many requests, please try again later',
} as const;
