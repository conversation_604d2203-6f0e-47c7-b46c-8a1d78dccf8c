#!/usr/bin/env tsx

/**
 * 环境配置文件格式修复脚本
 * 自动为包含空格、逗号或特殊字符的环境变量值添加引号
 */

import * as fs from 'fs';
import * as path from 'path';

const envFiles = ['.env', '.env.example', '.env.complete'];

// 需要添加引号的模式
const needsQuotesPatterns = [
  /^([A-Z_]+)=(.+[, |].+)$/,  // 包含逗号、空格或管道符
  /^([A-Z_]+)=(.+\s.+)$/,     // 包含空格
  /^([A-Z_]+)=(.+[\u4e00-\u9fa5].+)$/, // 包含中文字符
];

function fixEnvFile(filePath: string): void {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  console.log(`🔧 修复文件: ${filePath}`);
  
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n');
  let modified = false;
  
  const fixedLines = lines.map((line, index) => {
    // 跳过注释和空行
    if (line.trim().startsWith('#') || line.trim() === '') {
      return line;
    }
    
    // 检查是否已经有引号
    if (line.includes('="') && line.endsWith('"')) {
      return line;
    }
    
    // 检查是否需要添加引号
    for (const pattern of needsQuotesPatterns) {
      const match = line.match(pattern);
      if (match) {
        const [, key, value] = match;
        const fixedLine = `${key}="${value}"`;
        if (fixedLine !== line) {
          console.log(`  第${index + 1}行: ${key} -> 添加引号`);
          modified = true;
          return fixedLine;
        }
      }
    }
    
    return line;
  });
  
  if (modified) {
    fs.writeFileSync(filePath, fixedLines.join('\n'));
    console.log(`✅ ${filePath} 修复完成`);
  } else {
    console.log(`✅ ${filePath} 格式正确，无需修复`);
  }
}

function validateEnvFile(filePath: string): boolean {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }

  console.log(`🔍 验证文件: ${filePath}`);
  
  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n');
  let hasErrors = false;
  
  lines.forEach((line, index) => {
    // 跳过注释和空行
    if (line.trim().startsWith('#') || line.trim() === '') {
      return;
    }
    
    // 检查环境变量格式
    if (line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=');
      
      // 检查键名格式
      if (!/^[A-Z_][A-Z0-9_]*$/.test(key)) {
        console.log(`  ⚠️  第${index + 1}行: 键名格式不正确 "${key}"`);
        hasErrors = true;
      }
      
      // 检查是否需要引号但没有引号
      if (value && !value.startsWith('"') && !value.endsWith('"')) {
        if (value.includes(' ') || value.includes(',') || value.includes('|') || /[\u4e00-\u9fa5]/.test(value)) {
          console.log(`  ⚠️  第${index + 1}行: 值可能需要引号 "${key}=${value}"`);
          hasErrors = true;
        }
      }
    }
  });
  
  if (!hasErrors) {
    console.log(`✅ ${filePath} 验证通过`);
  }
  
  return !hasErrors;
}

function main() {
  console.log('🚀 开始修复环境配置文件格式...\n');
  
  // 修复所有环境文件
  envFiles.forEach(file => {
    fixEnvFile(file);
    console.log('');
  });
  
  console.log('📋 验证修复结果...\n');
  
  // 验证所有环境文件
  let allValid = true;
  envFiles.forEach(file => {
    const isValid = validateEnvFile(file);
    allValid = allValid && isValid;
    console.log('');
  });
  
  if (allValid) {
    console.log('🎉 所有环境配置文件格式正确！');
  } else {
    console.log('❌ 部分环境配置文件存在格式问题，请检查上述警告');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { fixEnvFile, validateEnvFile };
