#!/usr/bin/env tsx

/**
 * 优化任务自动化设置脚本
 * 自动执行优化任务的初始化和配置工作
 */

import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface TaskStatus {
  id: string;
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

class OptimizationSetup {
  private tasks: TaskStatus[] = [];
  private logFile = 'logs/optimization-setup.log';

  constructor() {
    this.initializeTasks();
    this.ensureLogDirectory();
  }

  private initializeTasks() {
    this.tasks = [
      { id: 'T001', name: 'HTTP方法安全加固', status: 'pending' },
      { id: 'T002', name: 'CSP安全头部配置', status: 'pending' },
      { id: 'T003', name: 'HTTPS配置优化', status: 'pending' },
      { id: 'T004', name: '请求头大小限制', status: 'pending' },
      { id: 'T005', name: '智能重试机制优化', status: 'pending' },
      { id: 'T006', name: '多层缓存架构实现', status: 'pending' },
      { id: 'T007', name: 'API健康检查系统增强', status: 'pending' },
      { id: 'T008', name: '慢速攻击防护', status: 'pending' },
    ];
  }

  private ensureLogDirectory() {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private log(message: string, level: 'info' | 'warn' | 'error' = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
    
    console.log(logMessage.trim());
    fs.appendFileSync(this.logFile, logMessage);
  }

  private updateTaskStatus(taskId: string, status: TaskStatus['status'], error?: string) {
    const task = this.tasks.find(t => t.id === taskId);
    if (task) {
      task.status = status;
      if (status === 'in_progress') {
        task.startTime = new Date();
      } else if (status === 'completed' || status === 'failed') {
        task.endTime = new Date();
        if (error) task.error = error;
      }
    }
  }

  /**
   * 检查环境依赖
   */
  async checkDependencies(): Promise<boolean> {
    this.log('检查环境依赖...');
    
    try {
      // 检查Node.js版本
      const { stdout: nodeVersion } = await execAsync('node --version');
      this.log(`Node.js版本: ${nodeVersion.trim()}`);
      
      // 检查npm版本
      const { stdout: npmVersion } = await execAsync('npm --version');
      this.log(`npm版本: ${npmVersion.trim()}`);
      
      // 检查TypeScript
      try {
        const { stdout: tsVersion } = await execAsync('npx tsc --version');
        this.log(`TypeScript版本: ${tsVersion.trim()}`);
      } catch (error) {
        this.log('TypeScript未安装或不可用', 'warn');
      }
      
      return true;
    } catch (error) {
      this.log(`依赖检查失败: ${error}`, 'error');
      return false;
    }
  }

  /**
   * 安装新的依赖包
   */
  async installDependencies(): Promise<boolean> {
    this.log('安装新的依赖包...');
    
    try {
      // 安装生产依赖
      await execAsync('npm install ioredis express-slow-down');
      this.log('生产依赖安装完成');
      
      // 安装开发依赖
      await execAsync('npm install --save-dev @types/ioredis');
      this.log('开发依赖安装完成');
      
      return true;
    } catch (error) {
      this.log(`依赖安装失败: ${error}`, 'error');
      return false;
    }
  }

  /**
   * 创建必要的目录结构
   */
  createDirectories(): boolean {
    this.log('创建目录结构...');
    
    const directories = [
      'src/middleware',
      'src/services',
      'src/utils',
      'src/routes',
      'logs',
      'ssl',
      'config'
    ];
    
    try {
      directories.forEach(dir => {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          this.log(`创建目录: ${dir}`);
        }
      });
      
      return true;
    } catch (error) {
      this.log(`目录创建失败: ${error}`, 'error');
      return false;
    }
  }

  /**
   * 创建环境变量模板
   */
  createEnvTemplate(): boolean {
    this.log('创建环境变量模板...');
    
    const envTemplate = `# UNM API 优化配置
# 复制此文件为 .env 并根据实际情况修改配置

# 基础配置
NODE_ENV=development
PORT=5678
SERVER_HOST=localhost

# 安全配置
ENABLE_HTTPS=false
HTTPS_PORT=443
SSL_KEY_PATH=./ssl/private-key.pem
SSL_CERT_PATH=./ssl/certificate.pem
MAX_HEADER_SIZE=8192
ENABLE_TRACE_BLOCK=true

# 缓存配置
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
CACHE_L1_TTL=300
CACHE_L2_TTL=1800

# 监控配置
METRICS_ENABLED=true
ALERT_ERROR_RATE_THRESHOLD=0.05
ALERT_RESPONSE_TIME_THRESHOLD=3000
ALERT_WEBHOOK_URL=

# 重试配置
RETRY_MAX_ATTEMPTS=3
RETRY_BASE_DELAY=1000
RETRY_MAX_DELAY=10000
RETRY_BACKOFF_FACTOR=2

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 音乐API配置
MUSIC_API_URL=https://music-api.gdstudio.xyz/api.php
HTTP_TIMEOUT=10000

# CORS配置
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# 频率限制配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_MESSAGE=Too many requests, please try again later

# 缓存配置
CACHE_TTL=300
ENABLE_CACHE=true
CACHE_MAX_SIZE=1000
CACHE_CLEANUP_INTERVAL=300000

# 音乐服务配置
DEFAULT_SOURCE=netease
SUPPORTED_SOURCES=netease,qq,xiami,baidu,kugou,kuwo,migu,joox,youtube,bilibili,pyncmd
STABLE_SOURCES=netease,kuwo,pyncmd

# API配置
API_VERSION=v1
API_BASE_PATH=/api
MAX_SEARCH_LIMIT=100
MAX_BATCH_SIZE=50
DEFAULT_SEARCH_LIMIT=20
DEFAULT_SEARCH_OFFSET=0

# UNM配置
UNM_DEFAULT_SOURCES=pyncmd,netease,kuwo
UNM_SUPPORTED_QUALITIES=128,192,320,740,999
UNM_DEFAULT_QUALITY=999
UNM_SUPPORTED_SIZES=300,500
UNM_DEFAULT_SIZE=300

# 安全配置
HELMET_ENABLED=true
REQUEST_SIZE_LIMIT=10mb

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# 静态文件配置
STATIC_PATH=/
STATIC_MAX_AGE=86400000

# 健康检查配置
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# 预热配置
WARMUP_QUERIES=
WARMUP_ENABLED=false
`;

    try {
      if (!fs.existsSync('.env.template')) {
        fs.writeFileSync('.env.template', envTemplate);
        this.log('环境变量模板创建完成: .env.template');
      }
      
      return true;
    } catch (error) {
      this.log(`环境变量模板创建失败: ${error}`, 'error');
      return false;
    }
  }

  /**
   * 创建SSL证书目录和说明文件
   */
  createSSLSetup(): boolean {
    this.log('创建SSL配置...');
    
    const sslReadme = `# SSL证书配置说明

## 开发环境

生成自签名证书用于开发测试：

\`\`\`bash
# 生成私钥
openssl genrsa -out ssl/private-key.pem 2048

# 生成证书签名请求
openssl req -new -key ssl/private-key.pem -out ssl/certificate.csr

# 生成自签名证书
openssl x509 -req -in ssl/certificate.csr -signkey ssl/private-key.pem -out ssl/certificate.pem -days 365
\`\`\`

## 生产环境

使用Let's Encrypt获取免费SSL证书：

\`\`\`bash
# 安装certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 证书文件位置
# 私钥: /etc/letsencrypt/live/yourdomain.com/privkey.pem
# 证书: /etc/letsencrypt/live/yourdomain.com/fullchain.pem
\`\`\`

## 配置环境变量

在 .env 文件中设置证书路径：

\`\`\`
ENABLE_HTTPS=true
SSL_KEY_PATH=/path/to/private-key.pem
SSL_CERT_PATH=/path/to/certificate.pem
\`\`\`
`;

    try {
      if (!fs.existsSync('ssl')) {
        fs.mkdirSync('ssl', { recursive: true });
      }
      
      fs.writeFileSync('ssl/README.md', sslReadme);
      this.log('SSL配置说明创建完成: ssl/README.md');
      
      return true;
    } catch (error) {
      this.log(`SSL配置创建失败: ${error}`, 'error');
      return false;
    }
  }

  /**
   * 运行完整的设置流程
   */
  async runSetup(): Promise<void> {
    this.log('开始优化任务设置流程...');
    
    try {
      // 检查依赖
      const depsOk = await this.checkDependencies();
      if (!depsOk) {
        throw new Error('依赖检查失败');
      }
      
      // 创建目录结构
      if (!this.createDirectories()) {
        throw new Error('目录创建失败');
      }
      
      // 创建环境变量模板
      if (!this.createEnvTemplate()) {
        throw new Error('环境变量模板创建失败');
      }
      
      // 创建SSL配置
      if (!this.createSSLSetup()) {
        throw new Error('SSL配置创建失败');
      }
      
      // 安装依赖包
      const installOk = await this.installDependencies();
      if (!installOk) {
        throw new Error('依赖安装失败');
      }
      
      this.log('优化任务设置完成！');
      this.printNextSteps();
      
    } catch (error) {
      this.log(`设置失败: ${error}`, 'error');
      process.exit(1);
    }
  }

  /**
   * 打印后续步骤
   */
  private printNextSteps(): void {
    console.log('\n🎉 优化任务设置完成！\n');
    console.log('📋 后续步骤：');
    console.log('1. 复制 .env.template 为 .env 并根据实际情况修改配置');
    console.log('2. 如需启用HTTPS，请按照 ssl/README.md 说明配置SSL证书');
    console.log('3. 如需启用Redis缓存，请安装并配置Redis服务器');
    console.log('4. 运行 npm run validate-config 验证配置');
    console.log('5. 开始执行优化任务，建议按照 OPTIMIZATION_TASKS.md 中的顺序进行');
    console.log('\n📚 相关文档：');
    console.log('- 优化任务文档: OPTIMIZATION_TASKS.md');
    console.log('- 环境变量模板: .env.template');
    console.log('- SSL配置说明: ssl/README.md');
    console.log('- 设置日志: logs/optimization-setup.log');
  }

  /**
   * 显示任务状态
   */
  showTaskStatus(): void {
    console.log('\n📊 任务状态：');
    this.tasks.forEach(task => {
      const statusIcon = {
        pending: '⏳',
        in_progress: '🔄',
        completed: '✅',
        failed: '❌'
      }[task.status];
      
      console.log(`${statusIcon} ${task.id}: ${task.name} - ${task.status}`);
    });
  }
}

// 主执行函数
async function main() {
  const setup = new OptimizationSetup();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'status':
      setup.showTaskStatus();
      break;
    case 'deps':
      await setup.checkDependencies();
      break;
    case 'install':
      await setup.installDependencies();
      break;
    default:
      await setup.runSetup();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

export { OptimizationSetup };
