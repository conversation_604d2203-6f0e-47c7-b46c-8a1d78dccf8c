# SSL证书配置说明

## 开发环境

生成自签名证书用于开发测试：

```bash
# 生成私钥
openssl genrsa -out ssl/private-key.pem 2048

# 生成证书签名请求
openssl req -new -key ssl/private-key.pem -out ssl/certificate.csr

# 生成自签名证书
openssl x509 -req -in ssl/certificate.csr -signkey ssl/private-key.pem -out ssl/certificate.pem -days 365
```

## 生产环境

使用Let's Encrypt获取免费SSL证书：

```bash
# 安装certbot
sudo apt-get install certbot

# 获取证书
sudo certbot certonly --standalone -d yourdomain.com

# 证书文件位置
# 私钥: /etc/letsencrypt/live/yourdomain.com/privkey.pem
# 证书: /etc/letsencrypt/live/yourdomain.com/fullchain.pem
```

## 配置环境变量

在 .env 文件中设置证书路径：

```
ENABLE_HTTPS=true
SSL_KEY_PATH=/path/to/private-key.pem
SSL_CERT_PATH=/path/to/certificate.pem
```
