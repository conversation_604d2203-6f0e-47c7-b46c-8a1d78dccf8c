import axios from 'axios';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

export interface ApiHealthStatus {
  url: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastChecked: Date;
  errorCount: number;
  successRate: number;
}

export interface HealthCheckResult {
  isHealthy: boolean;
  apis: ApiHealthStatus[];
  recommendedApi: string | undefined;
}

/**
 * API健康检查服务
 * 监控外部API的可用性和性能
 */
export class ApiHealthService {
  private static instance: ApiHealthService;
  private healthStatus: Map<string, ApiHealthStatus> = new Map();
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL = 60000; // 1分钟检查一次
  private readonly TIMEOUT = 5000; // 5秒超时
  private readonly MAX_ERROR_COUNT = 5; // 最大错误次数

  private constructor() {
    this.initializeApis();
    this.startHealthChecks();
  }

  public static getInstance(): ApiHealthService {
    if (!ApiHealthService.instance) {
      ApiHealthService.instance = new ApiHealthService();
    }
    return ApiHealthService.instance;
  }

  /**
   * 初始化API列表
   */
  private initializeApis(): void {
    const apis = [
      config.musicApiUrl,
      // 可以添加更多备用API
      'https://music.gdstudio.xyz/api.php'
    ];

    apis.forEach(url => {
      this.healthStatus.set(url, {
        url,
        status: 'healthy',
        responseTime: 0,
        lastChecked: new Date(),
        errorCount: 0,
        successRate: 100
      });
    });
  }

  /**
   * 开始定期健康检查
   */
  private startHealthChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, this.CHECK_INTERVAL);

    // 立即执行一次检查
    this.performHealthChecks();
  }

  /**
   * 执行健康检查
   */
  private async performHealthChecks(): Promise<void> {
    const promises = Array.from(this.healthStatus.keys()).map(url => 
      this.checkApiHealth(url)
    );

    await Promise.allSettled(promises);
    this.updateRecommendations();
  }

  /**
   * 检查单个API的健康状态
   */
  private async checkApiHealth(url: string): Promise<void> {
    const status = this.healthStatus.get(url);
    if (!status) return;

    const startTime = Date.now();
    
    try {
      // 发送健康检查请求
      await axios.get(url, {
        timeout: this.TIMEOUT,
        params: {
          types: 'search',
          source: 'netease',
          name: '测试',
          count: 1
        }
      });

      const responseTime = Date.now() - startTime;
      
      // 更新成功状态
      status.responseTime = responseTime;
      status.lastChecked = new Date();
      status.errorCount = Math.max(0, status.errorCount - 1); // 成功时减少错误计数
      
      // 计算成功率
      const totalChecks = 100; // 假设最近100次检查
      const successCount = totalChecks - status.errorCount;
      status.successRate = (successCount / totalChecks) * 100;
      
      // 根据响应时间和成功率确定状态
      if (responseTime < 2000 && status.successRate > 90) {
        status.status = 'healthy';
      } else if (responseTime < 5000 && status.successRate > 70) {
        status.status = 'degraded';
      } else {
        status.status = 'unhealthy';
      }

      logger.debug(`API health check success: ${url}`, {
        responseTime,
        successRate: status.successRate
      });

    } catch (error) {
      // 更新失败状态
      status.errorCount = Math.min(this.MAX_ERROR_COUNT, status.errorCount + 1);
      status.lastChecked = new Date();
      status.responseTime = Date.now() - startTime;
      
      // 计算成功率
      const totalChecks = 100;
      const successCount = totalChecks - status.errorCount;
      status.successRate = Math.max(0, (successCount / totalChecks) * 100);
      
      // 根据错误次数确定状态
      if (status.errorCount >= this.MAX_ERROR_COUNT) {
        status.status = 'unhealthy';
      } else if (status.errorCount >= 2) {
        status.status = 'degraded';
      }

      logger.warn(`API health check failed: ${url}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCount: status.errorCount,
        successRate: status.successRate
      });
    }
  }

  /**
   * 更新推荐API
   */
  private updateRecommendations(): void {
    // 根据健康状态和性能选择最佳API
    const healthyApis = Array.from(this.healthStatus.values())
      .filter(api => api.status === 'healthy')
      .sort((a, b) => {
        // 优先考虑成功率，然后是响应时间
        if (a.successRate !== b.successRate) {
          return b.successRate - a.successRate;
        }
        return a.responseTime - b.responseTime;
      });

    if (healthyApis.length > 0) {
      logger.info(`Recommended API: ${healthyApis[0]?.url}`);
    } else {
      logger.warn('No healthy APIs available');
    }
  }

  /**
   * 获取健康检查结果
   */
  public getHealthStatus(): HealthCheckResult {
    const apis = Array.from(this.healthStatus.values());
    const healthyApis = apis.filter(api => api.status === 'healthy');
    
    return {
      isHealthy: healthyApis.length > 0,
      apis: apis,
      recommendedApi: healthyApis.length > 0 ? healthyApis[0]?.url : undefined
    };
  }

  /**
   * 获取推荐的API URL
   */
  public getRecommendedApi(): string {
    const healthStatus = this.getHealthStatus();
    return healthStatus.recommendedApi || config.musicApiUrl;
  }

  /**
   * 手动触发健康检查
   */
  public async forceHealthCheck(): Promise<HealthCheckResult> {
    await this.performHealthChecks();
    return this.getHealthStatus();
  }

  /**
   * 停止健康检查
   */
  public stopHealthChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * 获取API统计信息
   */
  public getApiStats(): {
    totalApis: number;
    healthyApis: number;
    degradedApis: number;
    unhealthyApis: number;
    averageResponseTime: number;
  } {
    const apis = Array.from(this.healthStatus.values());
    
    return {
      totalApis: apis.length,
      healthyApis: apis.filter(api => api.status === 'healthy').length,
      degradedApis: apis.filter(api => api.status === 'degraded').length,
      unhealthyApis: apis.filter(api => api.status === 'unhealthy').length,
      averageResponseTime: apis.reduce((sum, api) => sum + api.responseTime, 0) / apis.length
    };
  }
}

// 导出单例实例
export const apiHealthService = ApiHealthService.getInstance();
